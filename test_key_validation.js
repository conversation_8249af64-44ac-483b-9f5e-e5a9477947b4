// Test script to verify key validation works with the updated Roblox validator
// This simulates the requests that the Roblox validator will make

const testKeyValidation = async () => {
  console.log('🧪 Testing Key Validation System...');
  
  // Test data that matches what the Roblox validator sends
  const testData = {
    keyCode: 'MADARA-TEST-TEST-TEST', // Replace with actual test key
    hwidHash: 'TEST_HWID_12345',
    robloxUsername: 'TestUser123',
    robloxUserId: '*********',
    fingerprint: 'TEST_HWID_12345_*********_TestUser123_*********',
    heartbeat: false,
    clientInfo: {
      executor: 'Synapse',
      placeId: '*********',
      jobId: 'test-job-id',
      accountAge: 365
    }
  };

  try {
    console.log('📤 Sending validation request...');
    
    const response = await fetch('/.netlify/functions/validate-key', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    
    console.log('📥 Response received:');
    console.log('Status:', response.status);
    console.log('Success:', result.success);
    console.log('Valid:', result.valid);
    
    if (result.success && result.valid) {
      console.log('✅ Key validation successful!');
      console.log('📅 Expires at:', result.expires_at);
      console.log('📊 Usage count:', result.usage_count);
      console.log('🔗 Newly bound:', result.newly_bound);
      console.log('🎮 Roblox HWID updated:', result.roblox_hwid_updated);
    } else {
      console.log('❌ Key validation failed:', result.error);
    }

    // Test heartbeat validation
    console.log('\n🔄 Testing heartbeat validation...');
    
    const heartbeatData = {
      ...testData,
      heartbeat: true,
      clientInfo: {
        ...testData.clientInfo,
        sessionTime: Date.now().toString()
      }
    };

    const heartbeatResponse = await fetch('/.netlify/functions/validate-key', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(heartbeatData)
    });

    const heartbeatResult = await heartbeatResponse.json();
    
    console.log('💓 Heartbeat response:');
    console.log('Success:', heartbeatResult.success);
    console.log('Valid:', heartbeatResult.valid);
    
    if (heartbeatResult.success && heartbeatResult.valid) {
      console.log('✅ Heartbeat validation successful!');
    } else {
      console.log('❌ Heartbeat validation failed:', heartbeatResult.error);
    }

  } catch (error) {
    console.error('🚨 Test failed:', error);
  }
};

// Instructions for running the test
console.log(`
🧪 Key Validation Test Script
=============================

To test the key validation:

1. Make sure you have a valid test key in your database
2. Update the 'keyCode' in testData above with your test key
3. Run this script in your browser console on your website
4. Check the console output for validation results

Note: This test simulates the exact requests that the Roblox validator makes.
`);

// Uncomment the line below to run the test automatically
// testKeyValidation();

// Export for manual testing
if (typeof window !== 'undefined') {
  window.testKeyValidation = testKeyValidation;
}
