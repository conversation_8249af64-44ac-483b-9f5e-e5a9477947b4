/**
 * Data Migration Script for IP Address Anonymization
 * 
 * This script migrates existing raw IP addresses to anonymized hashes
 * while maintaining referential integrity and security functionality.
 * 
 * Usage:
 * node migrate-existing-ips.js
 * 
 * Environment Variables Required:
 * - SUPABASE_URL: Your Supabase project URL
 * - SUPABASE_SERVICE_ROLE_KEY: Service role key for database access
 * - IP_ANONYMIZATION_SALT: Salt for IP hashing (same as backend)
 */

const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');

// Configuration
const BATCH_SIZE = 100; // Process records in batches
const DRY_RUN = process.env.DRY_RUN === 'true'; // Set to true for testing

class IPMigrator {
  constructor() {
    this.supabaseUrl = process.env.SUPABASE_URL;
    this.supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    this.salt = process.env.IP_ANONYMIZATION_SALT || 'MADARA-IP-SALT-2024-SECURE-SERVER';
    
    if (!this.supabaseUrl || !this.supabaseKey) {
      throw new Error('Missing required environment variables: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
    }
    
    this.supabase = createClient(this.supabaseUrl, this.supabaseKey);
    this.stats = {
      processed: 0,
      updated: 0,
      errors: 0,
      skipped: 0
    };
  }

  /**
   * Anonymize an IP address using the same logic as the backend
   */
  anonymizeIP(ipAddress) {
    if (!ipAddress || ipAddress === 'unknown' || ipAddress === 'localhost' || ipAddress === '127.0.0.1') {
      return 'unknown';
    }

    // Normalize IP address
    const normalizedIP = ipAddress.trim().toLowerCase();
    
    // Create salted hash
    const saltedIP = normalizedIP + this.salt;
    return crypto.createHash('sha256').update(saltedIP).digest('hex');
  }

  /**
   * Migrate a single table
   */
  async migrateTable(tableName, ipColumn = 'ip_address', hashColumn = 'ip_hash') {
    console.log(`\n🔄 Migrating table: ${tableName}`);
    
    let offset = 0;
    let hasMore = true;
    let tableStats = { processed: 0, updated: 0, errors: 0, skipped: 0 };

    while (hasMore) {
      try {
        // Fetch batch of records with IP addresses but no hash
        const { data: records, error } = await this.supabase
          .from(tableName)
          .select(`id, ${ipColumn}, ${hashColumn}`)
          .not(ipColumn, 'is', null)
          .is(hashColumn, null)
          .range(offset, offset + BATCH_SIZE - 1);

        if (error) {
          console.error(`❌ Error fetching from ${tableName}:`, error.message);
          tableStats.errors++;
          break;
        }

        if (!records || records.length === 0) {
          hasMore = false;
          break;
        }

        console.log(`   Processing batch: ${offset + 1}-${offset + records.length}`);

        // Process each record in the batch
        for (const record of records) {
          try {
            const ipHash = this.anonymizeIP(record[ipColumn]);
            
            if (ipHash === 'unknown') {
              console.log(`   ⚠️  Skipping invalid IP: ${record[ipColumn]}`);
              tableStats.skipped++;
              continue;
            }

            if (!DRY_RUN) {
              // Update the record with the anonymized hash
              const { error: updateError } = await this.supabase
                .from(tableName)
                .update({ [hashColumn]: ipHash })
                .eq('id', record.id);

              if (updateError) {
                console.error(`   ❌ Error updating record ${record.id}:`, updateError.message);
                tableStats.errors++;
                continue;
              }
            }

            tableStats.updated++;
            tableStats.processed++;

            // Log progress every 50 records
            if (tableStats.processed % 50 === 0) {
              console.log(`   ✅ Processed ${tableStats.processed} records...`);
            }

          } catch (recordError) {
            console.error(`   ❌ Error processing record ${record.id}:`, recordError.message);
            tableStats.errors++;
          }
        }

        offset += BATCH_SIZE;

      } catch (batchError) {
        console.error(`❌ Error processing batch for ${tableName}:`, batchError.message);
        tableStats.errors++;
        break;
      }
    }

    console.log(`✅ Completed ${tableName}:`, tableStats);
    
    // Update global stats
    this.stats.processed += tableStats.processed;
    this.stats.updated += tableStats.updated;
    this.stats.errors += tableStats.errors;
    this.stats.skipped += tableStats.skipped;

    return tableStats;
  }

  /**
   * Run the complete migration
   */
  async migrate() {
    console.log('🚀 Starting IP Address Anonymization Migration');
    console.log(`📊 Mode: ${DRY_RUN ? 'DRY RUN (no changes will be made)' : 'LIVE MIGRATION'}`);
    console.log(`🧂 Salt: ${this.salt.substring(0, 10)}...`);
    
    const startTime = Date.now();

    // Define tables to migrate
    const tables = [
      { name: 'ip_bans', ipColumn: 'ip_address', hashColumn: 'ip_hash' },
      { name: 'security_events', ipColumn: 'ip_address', hashColumn: 'ip_hash' },
      { name: 'admin_security_actions', ipColumn: 'ip_address', hashColumn: 'ip_hash' },
      { name: 'license_keys', ipColumn: 'ip_address', hashColumn: 'ip_hash' },
      { name: 'key_usage_logs', ipColumn: 'ip_address', hashColumn: 'ip_hash' },
      { name: 'key_sessions', ipColumn: 'ip_address', hashColumn: 'ip_hash' },
      { name: 'rate_limit_entries', ipColumn: 'ip_address', hashColumn: 'ip_hash' },
      { name: 'security_violations', ipColumn: 'ip_address', hashColumn: 'ip_hash' },
      { name: 'secure_sessions', ipColumn: 'ip_address', hashColumn: 'ip_hash' },
      { name: 'user_sessions', ipColumn: 'ip_address', hashColumn: 'ip_hash' },
      { name: 'revenue_events', ipColumn: 'ip_address', hashColumn: 'ip_hash' },
      { name: 'lootlabs_security_violations', ipColumn: 'ip_address', hashColumn: 'ip_hash' },
      { name: 'lootlabs_session_security', ipColumn: 'ip_address', hashColumn: 'ip_hash' },
      { name: 'script_views', ipColumn: 'ip_address', hashColumn: 'ip_hash' },
      { name: 'download_tokens', ipColumn: 'ip_address', hashColumn: 'ip_hash' }
    ];

    try {
      // Migrate each table
      for (const table of tables) {
        await this.migrateTable(table.name, table.ipColumn, table.hashColumn);
      }

      const duration = (Date.now() - startTime) / 1000;
      
      console.log('\n🎉 Migration completed successfully!');
      console.log('📊 Final Statistics:');
      console.log(`   ⏱️  Duration: ${duration.toFixed(2)} seconds`);
      console.log(`   📝 Total processed: ${this.stats.processed}`);
      console.log(`   ✅ Successfully updated: ${this.stats.updated}`);
      console.log(`   ⚠️  Skipped (invalid IPs): ${this.stats.skipped}`);
      console.log(`   ❌ Errors: ${this.stats.errors}`);
      
      if (DRY_RUN) {
        console.log('\n⚠️  This was a DRY RUN - no actual changes were made to the database.');
        console.log('   Set DRY_RUN=false to perform the actual migration.');
      }

    } catch (error) {
      console.error('💥 Migration failed:', error.message);
      throw error;
    }
  }

  /**
   * Verify migration results
   */
  async verify() {
    console.log('\n🔍 Verifying migration results...');
    
    const tables = [
      'ip_bans', 'security_events', 'admin_security_actions', 'license_keys',
      'key_usage_logs', 'key_sessions', 'rate_limit_entries', 'security_violations',
      'secure_sessions', 'user_sessions', 'revenue_events'
    ];

    for (const tableName of tables) {
      try {
        // Count records with IP but no hash
        const { count: missingHashes } = await this.supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true })
          .not('ip_address', 'is', null)
          .is('ip_hash', null);

        // Count records with both IP and hash
        const { count: withHashes } = await this.supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true })
          .not('ip_address', 'is', null)
          .not('ip_hash', 'is', null);

        console.log(`   ${tableName}: ${withHashes || 0} migrated, ${missingHashes || 0} remaining`);

      } catch (error) {
        console.error(`   ❌ Error verifying ${tableName}:`, error.message);
      }
    }
  }
}

// Main execution
async function main() {
  try {
    const migrator = new IPMigrator();
    
    // Run migration
    await migrator.migrate();
    
    // Verify results
    await migrator.verify();
    
    process.exit(0);
  } catch (error) {
    console.error('💥 Migration script failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { IPMigrator };
