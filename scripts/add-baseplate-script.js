import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addBaseplateScript() {
  console.log('Adding baseplate script to database...');
  
  try {
    // Read the baseplate script
    const scriptPath = path.join(process.cwd(), 'Roblox_Script', '17574618959.lua');
    const content = fs.readFileSync(scriptPath, 'utf8');
    const contentHash = crypto.createHash('sha256').update(content).digest('hex');
    
    const placeId = 17574618959;
    const gameName = "Just a baseplate";
    
    // Check if script already exists for this PlaceId
    const { data: existing } = await supabase
      .from('scripts')
      .select('id')
      .eq('place_id', placeId)
      .eq('script_type', 'game_specific')
      .single();
      
    if (existing) {
      console.log(`Updating existing script for ${gameName}...`);
      
      const { error: updateError } = await supabase
        .from('scripts')
        .update({
          content: content,
          content_hash: contentHash,
          file_size_bytes: Buffer.byteLength(content, 'utf8'),
          updated_at: new Date().toISOString(),
          version: '1.1.0'
        })
        .eq('id', existing.id);
        
      if (updateError) {
        console.error('Failed to update baseplate script:', updateError);
        return;
      }
      
      console.log('✅ Baseplate script updated successfully');
    } else {
      console.log(`Creating new script for ${gameName}...`);
      
      // Insert game script
      const { data: script, error: scriptError } = await supabase
        .from('scripts')
        .insert({
          name: `${gameName} Script`,
          description: "Custom script for Just a baseplate game. Template ready for your custom code.",
          content: content,
          script_type: 'game_specific',
          place_id: placeId,
          game_name: gameName,
          storage_type: 'database',
          access_level: 'authenticated',
          requires_valid_key: true,
          category: 'game',
          tags: ['Game Specific', gameName, 'Custom', 'Baseplate'],
          executor: 'All Executors',
          version: '1.0.0',
          file_size_bytes: Buffer.byteLength(content, 'utf8'),
          content_hash: contentHash,
          uploaded_by: 'system'
        })
        .select()
        .single();
        
      if (scriptError) {
        console.error(`Failed to add script for ${gameName}:`, scriptError);
        return;
      }
      
      // Add to game registry
      const { error: registryError } = await supabase
        .from('game_registry')
        .insert({
          place_id: placeId,
          game_name: gameName,
          game_description: "A simple baseplate game for testing and custom scripts",
          game_url: "https://www.roblox.com/games/17574618959/Just-a-baseplate",
          script_id: script.id,
          is_supported: true,
          support_level: 'full',
          genre: 'Testing',
          last_script_update: new Date().toISOString()
        });
        
      if (registryError) {
        console.error(`Failed to add registry entry for ${gameName}:`, registryError);
      }
      
      console.log(`✅ Added script for ${gameName} (PlaceId: ${placeId})`);
    }
    
    console.log('\n🎮 Baseplate Script Ready!');
    console.log(`Game: ${gameName}`);
    console.log(`PlaceId: ${placeId}`);
    console.log(`URL: https://www.roblox.com/games/17574618959/Just-a-baseplate`);
    
    console.log('\n📋 Testing Instructions:');
    console.log('1. Deploy your changes: netlify deploy --prod');
    console.log('2. Go to the baseplate game in Roblox');
    console.log('3. Use the centralized loadstring:');
    console.log('   loadstring(game:HttpGet("https://projectmadara.com/.netlify/functions/secure-script?action=get_main_loader"))()');

  } catch (error) {
    console.error(`Error processing baseplate script:`, error);
  }
}

// Run the script
addBaseplateScript().catch(console.error);
