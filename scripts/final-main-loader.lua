
-- PROJECT MADARA - ENHANCED LOADER SYSTEM
-- Completely rewritten for better security, performance, and maintainability

local ProjectMadara = {}
ProjectMadara.__index = ProjectMadara

-- Services
local Services = {
    Players = game:GetService("Players"),
    CoreGui = game:GetService("CoreGui"),
    HttpService = game:GetService("HttpService"),
    UserInputService = game:GetService("UserInputService"),
    TweenService = game:GetService("TweenService"),
    RbxAnalyticsService = game:GetService("RbxAnalyticsService"),
    RunService = game:GetService("RunService")
}

-- Constants
local CONSTANTS = {
    API_BASE = "https://projectmadara.com/.netlify/functions/",
    KEY_FILE = "projectMadara_key.dat",
    SUPPORTED_GAMES = {
        ["17574618959"] = {
            name = "Example Game",
            version = "1.0.0"
        }
        -- Add more games here
    },
    ANIMATIONS = {
        DURATION = 0.3,
        EASING = Enum.EasingStyle.Quint,
        DIRECTION = Enum.EasingDirection.Out
    },
    COLORS = {
        PRIMARY = Color3.fromRGB(42, 109, 64),
        SECONDARY = Color3.fromRGB(61, 207, 117),
        DANGER = Color3.fromRGB(255, 102, 102),
        DISCORD = Color3.fromRGB(114, 137, 218),
        BACKGROUND = Color3.fromRGB(20, 20, 20),
        SURFACE = Color3.fromRGB(31, 31, 31),
        TEXT = Color3.fromRGB(220, 220, 220)
    }
}

-- Utility Functions
local Utils = {}

function Utils.safeHttpRequest(url, method, headers, body)
    method = method or "GET"
    local success, result = pcall(function()
        if method == "GET" then
            return Services.HttpService:GetAsync(url)
        else
            return Services.HttpService:RequestAsync({
                Url = url,
                Method = method,
                Headers = headers or {},
                Body = body
            })
        end
    end)
    
    return success, result
end

function Utils.getHWID()
    local hwid = Services.RbxAnalyticsService:GetClientId()
    if not hwid or hwid == "" then
        hwid = Services.Players.LocalPlayer.UserId .. "_" .. tick()
    end
    return hwid
end

function Utils.encodeBase64(data)
    local chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
    return ((data:gsub('.', function(x) 
        local r,b='',x:byte()
        for i=8,1,-1 do r=r..(b%2^i-b%2^(i-1)>0 and '1' or '0') end
        return r;
    end)..'0000'):gsub('%d%d%d?%d?%d?%d?', function(x)
        if (#x < 6) then return '' end
        local c=0
        for i=1,6 do c=c+(x:sub(i,i)=='1' and 2^(6-i) or 0) end
        return chars:sub(c+1,c+1)
    end)..({ '', '==', '=' })[#data%3+1])
end

function Utils.createTween(object, properties, duration)
    duration = duration or CONSTANTS.ANIMATIONS.DURATION
    local tweenInfo = TweenInfo.new(
        duration,
        CONSTANTS.ANIMATIONS.EASING,
        CONSTANTS.ANIMATIONS.DIRECTION
    )
    return Services.TweenService:Create(object, tweenInfo, properties)
end

-- Notification System
local NotificationSystem = {}

function NotificationSystem.init()
    if NotificationSystem.screenGui then return end
    
    NotificationSystem.screenGui = Instance.new("ScreenGui")
    NotificationSystem.screenGui.Name = "MadaraNotifications"
    NotificationSystem.screenGui.ResetOnSpawn = false
    NotificationSystem.screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
    
    -- Try to hide GUI from detection
    local function protectGui(gui)
        if gethui then
            gui.Parent = gethui()
        elseif syn and syn.protect_gui then
            syn.protect_gui(gui)
            gui.Parent = Services.CoreGui
        else
            gui.Parent = Services.CoreGui
        end
    end
    
    protectGui(NotificationSystem.screenGui)
    
    local container = Instance.new("Frame")
    container.Name = "Container"
    container.AnchorPoint = Vector2.new(0.5, 1)
    container.Position = UDim2.new(0.5, 0, 0.95, 0)
    container.Size = UDim2.new(0, 400, 0, 0)
    container.BackgroundTransparency = 1
    container.Parent = NotificationSystem.screenGui
    
    local layout = Instance.new("UIListLayout")
    layout.HorizontalAlignment = Enum.HorizontalAlignment.Center
    layout.SortOrder = Enum.SortOrder.LayoutOrder
    layout.VerticalAlignment = Enum.VerticalAlignment.Bottom
    layout.Padding = UDim.new(0, 5)
    layout.Parent = container
    
    NotificationSystem.container = container
end

function NotificationSystem.create(message, duration, notificationType)
    NotificationSystem.init()
    
    duration = duration or 5
    notificationType = notificationType or "info"
    
    local colors = {
        success = Color3.fromRGB(46, 125, 50),
        error = Color3.fromRGB(211, 47, 47),
        warning = Color3.fromRGB(245, 124, 0),
        info = CONSTANTS.COLORS.PRIMARY
    }
    
    local notification = Instance.new("Frame")
    notification.Size = UDim2.new(0, 0, 0, 45)
    notification.BackgroundColor3 = CONSTANTS.COLORS.SURFACE
    notification.BorderSizePixel = 0
    notification.ClipsDescendants = true
    notification.Parent = NotificationSystem.container
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 6)
    corner.Parent = notification
    
    local stroke = Instance.new("UIStroke")
    stroke.Color = colors[notificationType]
    stroke.Thickness = 2
    stroke.Parent = notification
    
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, -50, 1, 0)
    textLabel.Position = UDim2.new(0, 10, 0, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = message
    textLabel.TextColor3 = CONSTANTS.COLORS.TEXT
    textLabel.TextSize = 14
    textLabel.Font = Enum.Font.Gotham
    textLabel.TextXAlignment = Enum.TextXAlignment.Left
    textLabel.TextWrapped = true
    textLabel.Parent = notification
    
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -35, 0.5, -15)
    closeButton.BackgroundColor3 = Color3.fromRGB(255, 59, 48)
    closeButton.Text = "×"
    closeButton.TextColor3 = Color3.white
    closeButton.TextSize = 18
    closeButton.Font = Enum.Font.GothamBold
    closeButton.Parent = notification
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 15)
    closeCorner.Parent = closeButton
    
    -- Animate in
    Utils.createTween(notification, {Size = UDim2.new(0, 350, 0, 45)}, 0.3):Play()
    
    local function destroy()
        Utils.createTween(notification, {
            Size = UDim2.new(0, 0, 0, 45),
            BackgroundTransparency = 1
        }, 0.2):Play()
        
        Utils.createTween(textLabel, {TextTransparency = 1}, 0.2):Play()
        Utils.createTween(closeButton, {BackgroundTransparency = 1, TextTransparency = 1}, 0.2):Play()
        Utils.createTween(stroke, {Transparency = 1}, 0.2):Play()
        
        task.wait(0.2)
        notification:Destroy()
    end
    
    closeButton.MouseButton1Click:Connect(destroy)
    task.delay(duration, destroy)
    
    return notification
end

-- Security Module
local Security = {}

function Security.validateEnvironment()
    local requiredFunctions = {
        "game", "workspace", "script", "getgenv", "loadstring"
    }
    
    for _, func in ipairs(requiredFunctions) do
        if not _G[func] then
            return false, "Missing required function: " .. func
        end
    end
    
    return true, "Environment validated"
end

function Security.antiDebug()
    -- Basic anti-debug measures
    local startTime = tick()
    task.wait(0.1)
    if tick() - startTime > 0.5 then
        return false, "Debugger detected"
    end
    
    return true, "Anti-debug passed"
end

function Security.validateKey(key, hwid)
    if not key or #key < 16 then
        return false, "Invalid key format"
    end
    
    local url = CONSTANTS.API_BASE .. "validate-key?key=" .. 
                Services.HttpService:UrlEncode(key) .. 
                "&hwid=" .. Services.HttpService:UrlEncode(hwid) ..
                "&version=2.0"
    
    local success, response = Utils.safeHttpRequest(url)
    
    if not success then
        return false, "Unable to reach validation server"
    end
    
    local data = Services.HttpService:JSONDecode(response)
    
    if data.success then
        return true, data.message or "Key validated successfully"
    else
        return false, data.error or "Invalid key"
    end
end

-- Game Management
local GameManager = {}

function GameManager.isSupported(placeId)
    return CONSTANTS.SUPPORTED_GAMES[tostring(placeId)] ~= nil
end

function GameManager.getGameInfo(placeId)
    return CONSTANTS.SUPPORTED_GAMES[tostring(placeId)]
end

function GameManager.loadScript(placeId)
    local gameInfo = GameManager.getGameInfo(placeId)
    if not gameInfo then
        return false, "Game not supported"
    end
    
    local url = CONSTANTS.API_BASE .. "secure-script?action=get_game_script&placeId=" .. 
                Services.HttpService:UrlEncode(tostring(placeId)) ..
                "&version=" .. Services.HttpService:UrlEncode(gameInfo.version)
    
    local success, script = Utils.safeHttpRequest(url)
    
    if not success then
        return false, "Failed to fetch game script"
    end
    
    if script:find("error") or script == "" then
        return false, "No script available for this game"
    end
    
    local loadSuccess, loadResult = pcall(loadstring(script))
    
    if not loadSuccess then
        return false, "Failed to execute game script: " .. tostring(loadResult)
    end
    
    return true, "Game script loaded successfully"
end

-- UI System
local UISystem = {}

function UISystem.createMainFrame()
    local gui = Instance.new("ScreenGui")
    gui.Name = "ProjectMadaraUI"
    gui.ResetOnSpawn = false
    gui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
    gui.Parent = Services.CoreGui
    
    local main = Instance.new("Frame")
    main.Name = "MainFrame"
    main.AnchorPoint = Vector2.new(0.5, 0.5)
    main.Position = UDim2.new(0.5, 0, 0.5, 0)
    main.Size = UDim2.new(0, 450, 0, 350)
    main.BackgroundColor3 = CONSTANTS.COLORS.BACKGROUND
    main.BorderSizePixel = 0
    main.ClipsDescendants = true
    main.Parent = gui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = main
    
    local shadow = Instance.new("ImageLabel")
    shadow.Name = "Shadow"
    shadow.AnchorPoint = Vector2.new(0.5, 0.5)
    shadow.Position = UDim2.new(0.5, 0, 0.5, 3)
    shadow.Size = UDim2.new(1, 20, 1, 20)
    shadow.BackgroundTransparency = 1
    shadow.Image = "rbxassetid://1316045217"
    shadow.ImageColor3 = Color3.new(0, 0, 0)
    shadow.ImageTransparency = 0.8
    shadow.ScaleType = Enum.ScaleType.Slice
    shadow.SliceCenter = Rect.new(10, 10, 118, 118)
    shadow.ZIndex = -1
    shadow.Parent = main
    
    return gui, main
end

function UISystem.createHeader(parent)
    local header = Instance.new("Frame")
    header.Name = "Header"
    header.Size = UDim2.new(1, 0, 0, 60)
    header.BackgroundColor3 = CONSTANTS.COLORS.SURFACE
    header.BorderSizePixel = 0
    header.Parent = parent
    
    local headerCorner = Instance.new("UICorner")
    headerCorner.CornerRadius = UDim.new(0, 8)
    headerCorner.Parent = header
    
    local title = Instance.new("TextLabel")
    title.Name = "Title"
    title.Position = UDim2.new(0, 20, 0, 0)
    title.Size = UDim2.new(1, -60, 1, 0)
    title.BackgroundTransparency = 1
    title.Text = "Project Madara - Advanced Key System"
    title.TextColor3 = CONSTANTS.COLORS.TEXT
    title.TextSize = 18
    title.Font = Enum.Font.GothamBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = header
    
    local closeBtn = Instance.new("TextButton")
    closeBtn.Name = "CloseButton"
    closeBtn.Position = UDim2.new(1, -50, 0.5, -15)
    closeBtn.Size = UDim2.new(0, 30, 0, 30)
    closeBtn.BackgroundColor3 = Color3.fromRGB(255, 59, 48)
    closeBtn.Text = "×"
    closeBtn.TextColor3 = Color3.white
    closeBtn.TextSize = 16
    closeBtn.Font = Enum.Font.GothamBold
    closeBtn.Parent = header
    
    local closeBtnCorner = Instance.new("UICorner")
    closeBtnCorner.CornerRadius = UDim.new(0, 15)
    closeBtnCorner.Parent = closeBtn
    
    return header, closeBtn
end

function UISystem.createKeyInput(parent)
    local container = Instance.new("Frame")
    container.Name = "KeyContainer"
    container.Position = UDim2.new(0, 20, 0, 80)
    container.Size = UDim2.new(1, -40, 0, 50)
    container.BackgroundTransparency = 1
    container.Parent = parent
    
    local keyBox = Instance.new("TextBox")
    keyBox.Name = "KeyInput"
    keyBox.Size = UDim2.new(1, 0, 1, 0)
    keyBox.BackgroundColor3 = CONSTANTS.COLORS.SURFACE
    keyBox.BorderSizePixel = 0
    keyBox.PlaceholderText = "Enter your key here..."
    keyBox.PlaceholderColor3 = Color3.fromRGB(150, 150, 150)
    keyBox.Text = ""
    keyBox.TextColor3 = CONSTANTS.COLORS.TEXT
    keyBox.TextSize = 14
    keyBox.Font = Enum.Font.Gotham
    keyBox.ClearTextOnFocus = false
    keyBox.Parent = container
    
    local keyBoxCorner = Instance.new("UICorner")
    keyBoxCorner.CornerRadius = UDim.new(0, 6)
    keyBoxCorner.Parent = keyBox
    
    local keyBoxStroke = Instance.new("UIStroke")
    keyBoxStroke.Color = Color3.fromRGB(60, 60, 60)
    keyBoxStroke.Thickness = 1
    keyBoxStroke.Parent = keyBox
    
    local padding = Instance.new("UIPadding")
    padding.PaddingLeft = UDim.new(0, 15)
    padding.PaddingRight = UDim.new(0, 15)
    padding.Parent = keyBox
    
    return keyBox
end

function UISystem.createButton(parent, text, color, position, size)
    local button = Instance.new("TextButton")
    button.Position = position
    button.Size = size
    button.BackgroundColor3 = color
    button.BorderSizePixel = 0
    button.Text = text
    button.TextColor3 = Color3.white
    button.TextSize = 14
    button.Font = Enum.Font.GothamBold
    button.Parent = parent
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 6)
    corner.Parent = button
    
    -- Hover effects
    button.MouseEnter:Connect(function()
        Utils.createTween(button, {BackgroundColor3 = Color3.new(
            math.min(color.R + 0.1, 1),
            math.min(color.G + 0.1, 1),
            math.min(color.B + 0.1, 1)
        )}, 0.2):Play()
    end)
    
    button.MouseLeave:Connect(function()
        Utils.createTween(button, {BackgroundColor3 = color}, 0.2):Play()
    end)
    
    return button
end

function UISystem.makeDraggable(frame)
    local dragging = false
    local dragInput
    local dragStart
    local startPos
    
    local function update(input)
        local delta = input.Position - dragStart
        frame.Position = UDim2.new(
            startPos.X.Scale,
            startPos.X.Offset + delta.X,
            startPos.Y.Scale,
            startPos.Y.Offset + delta.Y
        )
    end
    
    frame.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            dragging = true
            dragStart = input.Position
            startPos = frame.Position
            
            input.Changed:Connect(function()
                if input.UserInputState == Enum.UserInputState.End then
                    dragging = false
                end
            end)
        end
    end)
    
    frame.InputChanged:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseMovement then
            dragInput = input
        end
    end)
    
    Services.UserInputService.InputChanged:Connect(function(input)
        if input == dragInput and dragging then
            update(input)
        end
    end)
end

-- Main Project Madara Class
function ProjectMadara.new()
    local self = setmetatable({}, ProjectMadara)
    self.placeId = tostring(game.PlaceId)
    self.hwid = Utils.getHWID()
    self.isInitialized = false
    return self
end

function ProjectMadara:init()
    if self.isInitialized then return end
    
    print("🚀 Project Madara v2.0 - Initializing...")
    
    -- Security checks
    local envValid, envMsg = Security.validateEnvironment()
    if not envValid then
        warn("❌ " .. envMsg)
        return false
    end
    
    local antiDebug, debugMsg = Security.antiDebug()
    if not antiDebug then
        warn("❌ " .. debugMsg)
        return false
    end
    
    -- Check game support
    if not GameManager.isSupported(self.placeId) then
        NotificationSystem.create(
            "Game not supported! Place ID: " .. self.placeId,
            10,
            "error"
        )
        return false
    end
    
    local gameInfo = GameManager.getGameInfo(self.placeId)
    print("✅ Game supported: " .. gameInfo.name .. " v" .. gameInfo.version)
    
    self.isInitialized = true
    return true
end

function ProjectMadara:checkSavedKey()
    if not isfile or not readfile then
        return nil
    end
    
    if isfile(CONSTANTS.KEY_FILE) then
        local savedKey = readfile(CONSTANTS.KEY_FILE)
        if savedKey and #savedKey > 0 then
            local isValid, message = Security.validateKey(savedKey, self.hwid)
            if isValid then
                return savedKey
            else
                -- Delete invalid saved key
                if delfile then
                    delfile(CONSTANTS.KEY_FILE)
                end
            end
        end
    end
    
    return nil
end

function ProjectMadara:saveKey(key)
    if writefile then
        writefile(CONSTANTS.KEY_FILE, key)
    end
end

function ProjectMadara:createUI()
    local gui, main = UISystem.createMainFrame()
    local header, closeBtn = UISystem.createHeader(main)
    local keyInput = UISystem.createKeyInput(main)
    
    -- Info text
    local infoText = Instance.new("TextLabel")
    infoText.Position = UDim2.new(0, 20, 0, 150)
    infoText.Size = UDim2.new(1, -40, 0, 60)
    infoText.BackgroundTransparency = 1
    infoText.Text = "Enter your Project Madara key to continue.\nKeys last 24 hours with automatic validation."
    infoText.TextColor3 = Color3.fromRGB(180, 180, 180)
    infoText.TextSize = 12
    infoText.Font = Enum.Font.Gotham
    infoText.TextWrapped = true
    infoText.TextYAlignment = Enum.TextYAlignment.Top
    infoText.Parent = main
    
    -- Buttons
    local validateBtn = UISystem.createButton(
        main, "Validate Key", CONSTANTS.COLORS.SECONDARY,
        UDim2.new(0, 20, 0, 240), UDim2.new(0.5, -30, 0, 40)
    )
    
    local getKeyBtn = UISystem.createButton(
        main, "Get Key", CONSTANTS.COLORS.DANGER,
        UDim2.new(0.5, 10, 0, 240), UDim2.new(0.5, -30, 0, 40)
    )
    
    local discordBtn = UISystem.createButton(
        main, "Join Discord", CONSTANTS.COLORS.DISCORD,
        UDim2.new(0, 20, 0, 290), UDim2.new(1, -40, 0, 40)
    )
    
    UISystem.makeDraggable(main)
    
    return {
        gui = gui,
        main = main,
        keyInput = keyInput,
        validateBtn = validateBtn,
        getKeyBtn = getKeyBtn,
        discordBtn = discordBtn,
        closeBtn = closeBtn
    }
end

function ProjectMadara:start()
    if not self:init() then
        return
    end
    
    -- Check for saved key first
    local savedKey = self:checkSavedKey()
    if savedKey then
        NotificationSystem.create("Using saved key...", 3, "info")
        local success, message = GameManager.loadScript(self.placeId)
        if success then
            NotificationSystem.create(message, 5, "success")
        else
            NotificationSystem.create("Error: " .. message, 5, "error")
        end
        return
    end
    
    -- Create UI
    local ui = self:createUI()
    
    -- Event handlers
    ui.closeBtn.MouseButton1Click:Connect(function()
        ui.gui:Destroy()
        NotificationSystem.create("Key system closed", 3, "info")
    end)
    
    ui.getKeyBtn.MouseButton1Click:Connect(function()
        if setclipboard then
            setclipboard("https://projectl.xyz/")
            NotificationSystem.create("Key website copied to clipboard!", 3, "success")
        end
    end)
    
    ui.discordBtn.MouseButton1Click:Connect(function()
        if setclipboard then
            setclipboard("https://discord.gg/fNxgAAjBdq")
            NotificationSystem.create("Discord invite copied to clipboard!", 3, "success")
        end
    end)
    
    local function validateKey()
        local key = ui.keyInput.Text:gsub("%s+", "")
        
        if #key == 0 then
            NotificationSystem.create("Please enter a key", 3, "warning")
            return
        end
        
        ui.validateBtn.Text = "Validating..."
        ui.keyInput.TextEditable = false
        
        local isValid, message = Security.validateKey(key, self.hwid)
        
        if isValid then
            self:saveKey(key)
            ui.gui:Destroy()
            NotificationSystem.create("Key validated successfully!", 3, "success")
            
            local success, loadMessage = GameManager.loadScript(self.placeId)
            if success then
                NotificationSystem.create(loadMessage, 5, "success")
            else
                NotificationSystem.create("Error: " .. loadMessage, 5, "error")
            end
        else
            NotificationSystem.create("Invalid key: " .. message, 5, "error")
            ui.validateBtn.Text = "Validate Key"
            ui.keyInput.TextEditable = true
            ui.keyInput.Text = ""
        end
    end
    
    ui.validateBtn.MouseButton1Click:Connect(validateKey)
    ui.keyInput.FocusLost:Connect(function(enterPressed)
        if enterPressed then
            validateKey()
        end
    end)
end

-- Initialize and start
local madara = ProjectMadara.new()
madara:start()