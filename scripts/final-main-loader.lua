
-- PROJECT MADARA - ENHANCED LOADER SYSTEM
-- Completely rewritten for better security, performance, and maintainability

local ProjectMadara = {}
ProjectMadara.__index = ProjectMadara

-- Services
local Services = {
    Players = game:GetService("Players"),
    CoreGui = game:GetService("CoreGui"),
    HttpService = game:GetService("HttpService"),
    UserInputService = game:GetService("UserInputService"),
    TweenService = game:GetService("TweenService"),
    RbxAnalyticsService = game:GetService("RbxAnalyticsService"),
    RunService = game:GetService("RunService")
}

-- Constants
local CONSTANTS = {
    API_BASE = "https://projectmadara.com/.netlify/functions/",
    KEY_FILE = "projectMadara_key.dat",
    SUPPORTED_GAMES = {
        ["17574618959"] = {
            name = "Example Game",
            version = "1.0.0"
        }
        -- Add more games here
    },
    ANIMATIONS = {
        DURATION = 0.3,
        EASING = Enum.EasingStyle.Quint,
        DIRECTION = Enum.EasingDirection.Out
    },
    COLORS = {
        PRIMARY = Color3.fromRGB(42, 109, 64),
        SECONDARY = Color3.fromRGB(61, 207, 117),
        DANGER = Color3.fromRGB(255, 102, 102),
        DISCORD = Color3.fromRGB(114, 137, 218),
        BACKGROUND = Color3.fromRGB(20, 20, 20),
        SURFACE = Color3.fromRGB(31, 31, 31),
        TEXT = Color3.fromRGB(220, 220, 220)
    }
}

-- Utility Functions
local Utils = {}

function Utils.safeHttpRequest(url, method, headers, body)
    method = method or "GET"
    local success, result = pcall(function()
        if method == "GET" then
            return Services.HttpService:GetAsync(url)
        else
            return Services.HttpService:RequestAsync({
                Url = url,
                Method = method,
                Headers = headers or {},
                Body = body
            })
        end
    end)
    
    return success, result
end

function Utils.getHWID()
    local hwid = Services.RbxAnalyticsService:GetClientId()
    if not hwid or hwid == "" then
        warn("❌ Unable to get HWID")
        return nil
    end
    return hwid
end

function Utils.encodeBase64(data)
    local chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
    return ((data:gsub('.', function(x) 
        local r,b='',x:byte()
        for i=8,1,-1 do r=r..(b%2^i-b%2^(i-1)>0 and '1' or '0') end
        return r;
    end)..'0000'):gsub('%d%d%d?%d?%d?%d?', function(x)
        if (#x < 6) then return '' end
        local c=0
        for i=1,6 do c=c+(x:sub(i,i)=='1' and 2^(6-i) or 0) end
        return chars:sub(c+1,c+1)
    end)..({ '', '==', '=' })[#data%3+1])
end

function Utils.createTween(object, properties, duration)
    duration = duration or CONSTANTS.ANIMATIONS.DURATION
    local tweenInfo = TweenInfo.new(
        duration,
        CONSTANTS.ANIMATIONS.EASING,
        CONSTANTS.ANIMATIONS.DIRECTION
    )
    return Services.TweenService:Create(object, tweenInfo, properties)
end

-- Notification System (Enhanced from working copy)
local NotificationSystem = {}

function NotificationSystem.init()
    if NotificationSystem.screenGui then return end

    NotificationSystem.screenGui = Instance.new("ScreenGui")
    NotificationSystem.screenGui.Name = "MadaraNotifications"
    NotificationSystem.screenGui.ResetOnSpawn = false
    NotificationSystem.screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling

    -- Enhanced GUI protection from working copy
    local HIDEUI = get_hidden_gui or gethui
    if syn and typeof(syn) == "table" and RenderWindow then
        syn.protect_gui = gethui
    end

    local function Hide_UI(gui)
        if HIDEUI then
            gui["Parent"] = HIDEUI()
        elseif (not is_sirhurt_closure) and (syn and syn.protect_gui) then
            syn.protect_gui(gui)
            gui["Parent"] = Services.CoreGui
        elseif Services.CoreGui:FindFirstChild("RobloxGui") then
            gui["Parent"] = Services.CoreGui.RobloxGui
        else
            gui["Parent"] = Services.CoreGui
        end
    end

    Hide_UI(NotificationSystem.screenGui)

    local container = Instance.new("Frame")
    container.Name = "Container"
    container.AnchorPoint = Vector2.new(0.5, 0.949999988079071)
    container.Position = UDim2.new(0.5, 0, 0.954999983, 0)
    container.Size = UDim2.new(0, 100, 0, 100)
    container.BackgroundTransparency = 1
    container.Parent = NotificationSystem.screenGui

    local layout = Instance.new("UIListLayout")
    layout.HorizontalAlignment = Enum.HorizontalAlignment.Center
    layout.SortOrder = Enum.SortOrder.LayoutOrder
    layout.VerticalAlignment = Enum.VerticalAlignment.Bottom
    layout.Parent = container

    NotificationSystem.container = container
end

function NotificationSystem.create(message, duration, notificationType)
    NotificationSystem.init()

    duration = duration or 5

    local frame_2 = Instance.new("Frame")
    frame_2.BackgroundColor3 = Color3.new(1, 1, 1)
    frame_2.BorderColor3 = Color3.new(0, 0, 0)
    frame_2.BorderSizePixel = 0
    frame_2.BackgroundTransparency = 1
    frame_2.Size = UDim2.new(0, 100, 0, 0)
    frame_2.Visible = true
    frame_2.Parent = NotificationSystem.container

    -- Main Notification Frame
    local frame_3 = Instance.new("Frame")
    frame_3.AnchorPoint = Vector2.new(0.5, 1)
    frame_3.AutomaticSize = Enum.AutomaticSize.X
    frame_3.BackgroundColor3 = Color3.new(0.141176, 0.141176, 0.141176)
    frame_3.BackgroundTransparency = 0.20000000298023224
    frame_3.BorderColor3 = Color3.new(0, 0, 0)
    frame_3.Position = UDim2.new(0.5, 0, 1, 60)
    frame_3.Size = UDim2.new(0, 0, 0, 30)
    frame_3.Visible = true
    frame_3.Parent = frame_2

    local uicorner = Instance.new("UICorner")
    uicorner.CornerRadius = UDim.new(0, 6)
    uicorner.Parent = frame_3

    local uipadding = Instance.new("UIPadding")
    uipadding.PaddingBottom = UDim.new(0, 3)
    uipadding.PaddingLeft = UDim.new(0, 3)
    uipadding.PaddingRight = UDim.new(0, 3)
    uipadding.PaddingTop = UDim.new(0, 3)
    uipadding.Parent = frame_3

    local uistroke = Instance.new("UIStroke")
    uistroke.ApplyStrokeMode = Enum.ApplyStrokeMode.Border
    uistroke.Color = Color3.new(0.0313726, 0.0313726, 0.0313726)
    uistroke.Parent = frame_3

    local text_label = Instance.new("TextLabel")
    text_label.Font = Enum.Font.Gotham
    text_label.Text = message
    text_label.TextColor3 = Color3.new(0.784314, 0.784314, 0.784314)
    text_label.TextSize = 14
    text_label.AutomaticSize = Enum.AutomaticSize.X
    text_label.BackgroundColor3 = Color3.new(1, 1, 1)
    text_label.BackgroundTransparency = 1
    text_label.BorderColor3 = Color3.new(0, 0, 0)
    text_label.BorderSizePixel = 0
    text_label.Size = UDim2.new(0, 0, 0, 24)
    text_label.Visible = true
    text_label.Parent = frame_3

    local uipadding_2 = Instance.new("UIPadding")
    uipadding_2.PaddingLeft = UDim.new(0, 5)
    uipadding_2.PaddingRight = UDim.new(0, 30)
    uipadding_2.Parent = text_label

    local text_button = Instance.new("TextButton")
    text_button.Font = Enum.Font.SourceSans
    text_button.Text = ""
    text_button.TextColor3 = Color3.new(0, 0, 0)
    text_button.TextSize = 14
    text_button.AnchorPoint = Vector2.new(1, 0.5)
    text_button.BackgroundColor3 = Color3.new(0, 0, 0)
    text_button.BackgroundTransparency = 1
    text_button.BorderColor3 = Color3.new(0, 0, 0)
    text_button.BorderSizePixel = 0
    text_button.Position = UDim2.new(1, 0, 0.5, 0)
    text_button.Size = UDim2.new(0, 24, 0, 24)
    text_button.Visible = true
    text_button.Parent = frame_3

    local uicorner_2 = Instance.new("UICorner")
    uicorner_2.CornerRadius = UDim.new(0, 5)
    uicorner_2.Parent = text_button

    local image_button = Instance.new("ImageButton")
    image_button.Image = "rbxassetid://3926305904"
    image_button.ImageColor3 = Color3.new(0.784314, 0.784314, 0.784314)
    image_button.ImageRectOffset = Vector2.new(924, 724)
    image_button.ImageRectSize = Vector2.new(36, 36)
    image_button.AnchorPoint = Vector2.new(0.5, 0.5)
    image_button.BackgroundTransparency = 1
    image_button.LayoutOrder = 3
    image_button.Position = UDim2.new(0.5, 0, 0.5, 0)
    image_button.Size = UDim2.new(0, 18, 0, 18)
    image_button.Visible = true
    image_button.ZIndex = 2
    image_button.Parent = text_button

    Services.TweenService:Create(frame_3, TweenInfo.new(0.2, Enum.EasingStyle.Quint), {Position = UDim2.new(0.5, 0, 1, 0)}):Play()
    Services.TweenService:Create(frame_2, TweenInfo.new(0.2, Enum.EasingStyle.Quint), {Size = UDim2.new(0, 100, 0, 35)}):Play()

    local function close_notif()
        Services.TweenService:Create(image_button, TweenInfo.new(0.15, Enum.EasingStyle.Quint), {ImageTransparency = 1}):Play()
        Services.TweenService:Create(text_button, TweenInfo.new(0.15, Enum.EasingStyle.Quint), {BackgroundTransparency = 1}):Play()
        Services.TweenService:Create(text_label, TweenInfo.new(0.15, Enum.EasingStyle.Quint), {TextTransparency = 1}):Play()
        task.wait(.17)
        Services.TweenService:Create(frame_3, TweenInfo.new(0.25, Enum.EasingStyle.Quint), {BackgroundTransparency = 1}):Play()
        Services.TweenService:Create(uistroke, TweenInfo.new(0.24, Enum.EasingStyle.Quint), {Transparency = 1}):Play()
        task.wait(.05)
        Services.TweenService:Create(frame_2, TweenInfo.new(0.2, Enum.EasingStyle.Quint), {Size = UDim2.new(0, 100, 0, 0)}):Play()
        task.wait(.2)
        frame_2:Destroy()
    end

    text_button.MouseEnter:Connect(function()
        Services.TweenService:Create(text_button, TweenInfo.new(0.25, Enum.EasingStyle.Quint), {BackgroundTransparency = 0.8}):Play()
        Services.TweenService:Create(image_button, TweenInfo.new(0.3, Enum.EasingStyle.Quint), {ImageColor3 = Color3.new(0.890196, 0.054902, 0.054902)}):Play()
    end)

    text_button.MouseLeave:Connect(function()
        Services.TweenService:Create(text_button, TweenInfo.new(0.25, Enum.EasingStyle.Quint), {BackgroundTransparency = 1}):Play()
        Services.TweenService:Create(image_button, TweenInfo.new(0.3, Enum.EasingStyle.Quint), {ImageColor3 = Color3.new(0.784314, 0.784314, 0.784314)}):Play()
    end)

    text_button.MouseButton1Click:Connect(close_notif)
    image_button.MouseButton1Click:Connect(close_notif)
    task.delay(tonumber(duration) and duration or 10, close_notif)

    return frame_2
end

-- Security Module
local Security = {}

function Security.validateKey(key, hwid)
    if not key or #key == 0 then
        return false, "A valid key is required"
    end

    print("🔑 Validating key: " .. key)
    print("🆔 HWID: " .. hwid)

    local url = CONSTANTS.API_BASE .. "validate-key?key=" .. key .. "&hwid=" .. hwid

    local success, response = Utils.safeHttpRequest(url)

    if not success then
        return false, "Unable to reach validation server"
    end

    if response then
        print("📨 Server response: " .. response)

        -- Check if it's a success response (contains "return true")
        if string.find(response, "return true") then
            local loadSuccess, result = pcall(loadstring(response))
            if loadSuccess and result then
                return result, "Key validated successfully"
            else
                return true, "Key is valid" -- Key is valid even if script execution fails
            end
        else
            -- It's an error message, execute it to show the warning
            local loadSuccess, result = pcall(loadstring(response))
            if loadSuccess then
                -- This will show the warn() message from server
            end
            return false, "Invalid key"
        end
    else
        return false, "No response from validation server"
    end
end

-- Game Management
local GameManager = {}

function GameManager.isSupported(placeId)
    return CONSTANTS.SUPPORTED_GAMES[tostring(placeId)] ~= nil
end

function GameManager.getGameInfo(placeId)
    return CONSTANTS.SUPPORTED_GAMES[tostring(placeId)]
end

function GameManager.loadScript(placeId)
    local gameInfo = GameManager.getGameInfo(placeId)
    if not gameInfo then
        return false, "Game not supported"
    end

    local scriptUrl = CONSTANTS.API_BASE .. "secure-script?action=get_game_script&placeId=" .. placeId

    print("🎮 Loading game script for Place ID: " .. placeId)

    local success, result = Utils.safeHttpRequest(scriptUrl)

    if success and result and result ~= "" and not string.find(result, "error") then
        local loadSuccess, response = pcall(loadstring(result))
        if loadSuccess then
            print("✅ Game script loaded successfully!")
            return true, "Game script loaded successfully!"
        else
            warn("❌ Failed to execute game script")
            return false, "Failed to execute game script"
        end
    else
        warn("❌ No script found for this game")
        warn("❌ Place ID " .. placeId .. " is not supported yet")
        return false, "No script found for this game"
    end
end

-- UI System
local UISystem = {}

function UISystem.createMainFrame()
    local gui = Instance.new("ScreenGui")
    gui.Name = "ProjectMadaraUI"
    gui.ResetOnSpawn = false
    gui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
    gui.Parent = Services.CoreGui
    
    local main = Instance.new("Frame")
    main.Name = "MainFrame"
    main.AnchorPoint = Vector2.new(0.5, 0.5)
    main.Position = UDim2.new(0.5, 0, 0.5, 0)
    main.Size = UDim2.new(0, 450, 0, 350)
    main.BackgroundColor3 = CONSTANTS.COLORS.BACKGROUND
    main.BorderSizePixel = 0
    main.ClipsDescendants = true
    main.Parent = gui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = main
    
    local shadow = Instance.new("ImageLabel")
    shadow.Name = "Shadow"
    shadow.AnchorPoint = Vector2.new(0.5, 0.5)
    shadow.Position = UDim2.new(0.5, 0, 0.5, 3)
    shadow.Size = UDim2.new(1, 20, 1, 20)
    shadow.BackgroundTransparency = 1
    shadow.Image = "rbxassetid://1316045217"
    shadow.ImageColor3 = Color3.new(0, 0, 0)
    shadow.ImageTransparency = 0.8
    shadow.ScaleType = Enum.ScaleType.Slice
    shadow.SliceCenter = Rect.new(10, 10, 118, 118)
    shadow.ZIndex = -1
    shadow.Parent = main
    
    return gui, main
end

function UISystem.createHeader(parent)
    local header = Instance.new("Frame")
    header.Name = "Header"
    header.Size = UDim2.new(1, 0, 0, 60)
    header.BackgroundColor3 = CONSTANTS.COLORS.SURFACE
    header.BorderSizePixel = 0
    header.Parent = parent
    
    local headerCorner = Instance.new("UICorner")
    headerCorner.CornerRadius = UDim.new(0, 8)
    headerCorner.Parent = header
    
    local title = Instance.new("TextLabel")
    title.Name = "Title"
    title.Position = UDim2.new(0, 20, 0, 0)
    title.Size = UDim2.new(1, -60, 1, 0)
    title.BackgroundTransparency = 1
    title.Text = "Project Madara - Advanced Key System"
    title.TextColor3 = CONSTANTS.COLORS.TEXT
    title.TextSize = 18
    title.Font = Enum.Font.GothamBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = header
    
    local closeBtn = Instance.new("TextButton")
    closeBtn.Name = "CloseButton"
    closeBtn.Position = UDim2.new(1, -50, 0.5, -15)
    closeBtn.Size = UDim2.new(0, 30, 0, 30)
    closeBtn.BackgroundColor3 = Color3.fromRGB(255, 59, 48)
    closeBtn.Text = "×"
    closeBtn.TextColor3 = Color3.white
    closeBtn.TextSize = 16
    closeBtn.Font = Enum.Font.GothamBold
    closeBtn.Parent = header
    
    local closeBtnCorner = Instance.new("UICorner")
    closeBtnCorner.CornerRadius = UDim.new(0, 15)
    closeBtnCorner.Parent = closeBtn
    
    return header, closeBtn
end

function UISystem.createKeyInput(parent)
    local container = Instance.new("Frame")
    container.Name = "KeyContainer"
    container.Position = UDim2.new(0, 20, 0, 80)
    container.Size = UDim2.new(1, -40, 0, 50)
    container.BackgroundTransparency = 1
    container.Parent = parent
    
    local keyBox = Instance.new("TextBox")
    keyBox.Name = "KeyInput"
    keyBox.Size = UDim2.new(1, 0, 1, 0)
    keyBox.BackgroundColor3 = CONSTANTS.COLORS.SURFACE
    keyBox.BorderSizePixel = 0
    keyBox.PlaceholderText = "Enter your key here..."
    keyBox.PlaceholderColor3 = Color3.fromRGB(150, 150, 150)
    keyBox.Text = ""
    keyBox.TextColor3 = CONSTANTS.COLORS.TEXT
    keyBox.TextSize = 14
    keyBox.Font = Enum.Font.Gotham
    keyBox.ClearTextOnFocus = false
    keyBox.Parent = container
    
    local keyBoxCorner = Instance.new("UICorner")
    keyBoxCorner.CornerRadius = UDim.new(0, 6)
    keyBoxCorner.Parent = keyBox
    
    local keyBoxStroke = Instance.new("UIStroke")
    keyBoxStroke.Color = Color3.fromRGB(60, 60, 60)
    keyBoxStroke.Thickness = 1
    keyBoxStroke.Parent = keyBox
    
    local padding = Instance.new("UIPadding")
    padding.PaddingLeft = UDim.new(0, 15)
    padding.PaddingRight = UDim.new(0, 15)
    padding.Parent = keyBox
    
    return keyBox
end

function UISystem.createButton(parent, text, color, position, size)
    local button = Instance.new("TextButton")
    button.Position = position
    button.Size = size
    button.BackgroundColor3 = color
    button.BorderSizePixel = 0
    button.Text = text
    button.TextColor3 = Color3.white
    button.TextSize = 14
    button.Font = Enum.Font.GothamBold
    button.Parent = parent
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 6)
    corner.Parent = button
    
    -- Hover effects
    button.MouseEnter:Connect(function()
        Utils.createTween(button, {BackgroundColor3 = Color3.new(
            math.min(color.R + 0.1, 1),
            math.min(color.G + 0.1, 1),
            math.min(color.B + 0.1, 1)
        )}, 0.2):Play()
    end)
    
    button.MouseLeave:Connect(function()
        Utils.createTween(button, {BackgroundColor3 = color}, 0.2):Play()
    end)
    
    return button
end

function UISystem.makeDraggable(frame)
    local dragging = false
    local dragInput
    local dragStart
    local startPos
    
    local function update(input)
        local delta = input.Position - dragStart
        frame.Position = UDim2.new(
            startPos.X.Scale,
            startPos.X.Offset + delta.X,
            startPos.Y.Scale,
            startPos.Y.Offset + delta.Y
        )
    end
    
    frame.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            dragging = true
            dragStart = input.Position
            startPos = frame.Position
            
            input.Changed:Connect(function()
                if input.UserInputState == Enum.UserInputState.End then
                    dragging = false
                end
            end)
        end
    end)
    
    frame.InputChanged:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseMovement then
            dragInput = input
        end
    end)
    
    Services.UserInputService.InputChanged:Connect(function(input)
        if input == dragInput and dragging then
            update(input)
        end
    end)
end

-- Main Project Madara Class
function ProjectMadara.new()
    local self = setmetatable({}, ProjectMadara)
    self.placeId = tostring(game.PlaceId)
    self.hwid = Utils.getHWID()
    self.isInitialized = false

    -- Validate HWID
    if not self.hwid then
        warn("❌ Unable to get HWID - system cannot continue")
        return nil
    end

    return self
end

function ProjectMadara:init()
    if self.isInitialized then return end

    print("🚀 Project Madara v2.0 - Initializing...")

    -- Check game support
    if not GameManager.isSupported(self.placeId) then
        NotificationSystem.create(
            "Game not supported! Place ID: " .. self.placeId,
            10
        )
        return false
    end

    local gameInfo = GameManager.getGameInfo(self.placeId)
    print("✅ Game supported: " .. gameInfo.name .. " v" .. gameInfo.version)

    self.isInitialized = true
    return true
end

function ProjectMadara:checkSavedKey()
    -- Check for both old and new key file formats
    local keyFiles = {"projectL.txt", CONSTANTS.KEY_FILE}

    if not isfile or not readfile then
        return nil
    end

    for _, keyFile in ipairs(keyFiles) do
        if isfile(keyFile) then
            local savedKey = readfile(keyFile)
            if savedKey and #savedKey > 0 then
                local isValid, message = Security.validateKey(savedKey, self.hwid)
                if isValid then
                    print("✅ Using saved key from: " .. keyFile)
                    return savedKey
                else
                    -- Delete invalid saved key
                    if delfile then
                        delfile(keyFile)
                        print("🗑️ Deleted invalid key file: " .. keyFile)
                    end
                end
            end
        end
    end

    return nil
end

function ProjectMadara:saveKey(key)
    if writefile then
        writefile("projectL.txt", key)
        print("💾 Key saved successfully")
    end
end

function ProjectMadara:createUI()
    local gui, main = UISystem.createMainFrame()
    local header, closeBtn = UISystem.createHeader(main)
    local keyInput = UISystem.createKeyInput(main)
    
    -- Info text
    local infoText = Instance.new("TextLabel")
    infoText.Position = UDim2.new(0, 20, 0, 150)
    infoText.Size = UDim2.new(1, -40, 0, 60)
    infoText.BackgroundTransparency = 1
    infoText.Text = "Enter your Project Madara key to continue.\nKeys last 24 hours with automatic validation."
    infoText.TextColor3 = Color3.fromRGB(180, 180, 180)
    infoText.TextSize = 12
    infoText.Font = Enum.Font.Gotham
    infoText.TextWrapped = true
    infoText.TextYAlignment = Enum.TextYAlignment.Top
    infoText.Parent = main
    
    -- Buttons
    local validateBtn = UISystem.createButton(
        main, "Validate Key", CONSTANTS.COLORS.SECONDARY,
        UDim2.new(0, 20, 0, 240), UDim2.new(0.5, -30, 0, 40)
    )
    
    local getKeyBtn = UISystem.createButton(
        main, "Get Key", CONSTANTS.COLORS.DANGER,
        UDim2.new(0.5, 10, 0, 240), UDim2.new(0.5, -30, 0, 40)
    )
    
    local discordBtn = UISystem.createButton(
        main, "Join Discord", CONSTANTS.COLORS.DISCORD,
        UDim2.new(0, 20, 0, 290), UDim2.new(1, -40, 0, 40)
    )
    
    UISystem.makeDraggable(main)
    
    return {
        gui = gui,
        main = main,
        keyInput = keyInput,
        validateBtn = validateBtn,
        getKeyBtn = getKeyBtn,
        discordBtn = discordBtn,
        closeBtn = closeBtn
    }
end

function ProjectMadara:start()
    if not self:init() then
        return
    end

    print("🚀 Project Madara - Starting...")
    print("🎮 Place ID: " .. self.placeId)

    -- Check for saved key first
    local savedKey = self:checkSavedKey()
    if savedKey then
        NotificationSystem.create("Using saved key...", 3)
        local success, message = GameManager.loadScript(self.placeId)
        if success then
            NotificationSystem.create("Loaded Script Successfully!", 3)
        else
            NotificationSystem.create("Error: " .. message, 5)
        end
        return
    end

    print("🔑 Starting key validation...")

    -- Create UI
    local ui = self:createUI()

    -- Event handlers
    ui.closeBtn.MouseButton1Click:Connect(function()
        ui.gui:Destroy()
        NotificationSystem.create("Key system has been closed", 3)
    end)

    ui.getKeyBtn.MouseButton1Click:Connect(function()
        if setclipboard then
            setclipboard("https://projectl.xyz/")
            NotificationSystem.create("Key website has been copied!")
        end
    end)

    ui.discordBtn.MouseButton1Click:Connect(function()
        if setclipboard then
            setclipboard("https://discord.gg/fNxgAAjBdq")
            NotificationSystem.create("Discord Invite Link Copied!", 3)
        end
    end)

    local function validateKey()
        local key = ui.keyInput.Text:gsub("%s+", "")

        if #key == 0 then
            NotificationSystem.create("Please enter a key", 3)
            return
        end

        ui.validateBtn.Text = "Validating..."
        ui.keyInput.TextEditable = false

        local isValid, message = Security.validateKey(key, self.hwid)

        if isValid then
            self:saveKey(key)
            ui.gui:Destroy()
            NotificationSystem.create("Loaded Script Successfully!", 3)

            local success, loadMessage = GameManager.loadScript(self.placeId)
            if success then
                -- Script already loaded, notification already shown
            else
                NotificationSystem.create("Error: " .. loadMessage, 5)
            end
        else
            NotificationSystem.create("Incorrect Key", 3)
            ui.validateBtn.Text = "Validate Key"
            ui.keyInput.TextEditable = true
            ui.keyInput.Text = ""
        end
    end

    ui.validateBtn.MouseButton1Click:Connect(validateKey)
    ui.keyInput.FocusLost:Connect(function(enterPressed)
        if enterPressed then
            validateKey()
        end
    end)
end

-- Initialize and start
local madara = ProjectMadara.new()
if madara then
    madara:start()
else
    warn("❌ Failed to initialize Project Madara - HWID error")
end