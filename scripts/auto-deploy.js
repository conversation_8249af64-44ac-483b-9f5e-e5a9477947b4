#!/usr/bin/env node

/**
 * Automated Deployment Script for Project Madara
 * Runs during Netlify build to automatically:
 * 1. Add/update main loader script
 * 2. Upload all scripts from Roblox_Script folder
 * 3. Update game registry
 * 4. Sync everything to database
 */

import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.dirname(__dirname);

// Environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.log('⚠️ Missing environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  console.log('ℹ️ This is expected in local development. Auto-deploy will run in Netlify.');
  console.log('ℹ️ Skipping script deployment and continuing with build...');
  process.exit(0); // Exit successfully to continue build
}

// Validate environment variables before creating client
let supabase;
try {
  if (!supabaseUrl || !supabaseServiceKey || supabaseUrl.includes('*') || supabaseServiceKey.includes('*')) {
    throw new Error('Invalid or masked environment variables');
  }
  supabase = createClient(supabaseUrl, supabaseServiceKey);
} catch (error) {
  console.log('⚠️ Invalid Supabase configuration:', error.message);
  console.log('ℹ️ This is expected in local development or if environment variables are not properly set.');
  console.log('ℹ️ Skipping script deployment and continuing with build...');
  process.exit(0); // Exit successfully to continue build
}

// Utility functions
const log = (message, type = 'info') => {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warn' ? '⚠️' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
};

// 1. Add/Update Main Loader Script
async function deployMainLoader() {
  log('Deploying main loader script...', 'info');
  
  try {
    // Use the single, definitive main loader
    let loaderPath = path.join(projectRoot, 'scripts', 'final-main-loader.lua');
    if (!fs.existsSync(loaderPath)) {
      log('Main loader not found', 'error');
      return false;
    }
    
    if (!fs.existsSync(loaderPath)) {
      log('Main loader script not found, skipping...', 'warn');
      return false;
    }
    
    const content = fs.readFileSync(loaderPath, 'utf8');
    const contentHash = crypto.createHash('sha256').update(content).digest('hex');
    
    // Check if main loader exists
    const { data: existing } = await supabase
      .from('scripts')
      .select('id, content_hash')
      .eq('script_type', 'main_loader')
      .eq('is_main_script', true)
      .single();
      
    if (existing) {
      if (existing.content_hash === contentHash) {
        log('Main loader unchanged, skipping update', 'info');
        return true;
      }
      
      // Update existing
      const { error } = await supabase
        .from('scripts')
        .update({
          content: content,
          content_hash: contentHash,
          file_size_bytes: Buffer.byteLength(content, 'utf8'),
          updated_at: new Date().toISOString(),
          version: '1.2.0'
        })
        .eq('id', existing.id);
        
      if (error) throw error;
      log('Main loader updated successfully', 'success');
    } else {
      // Create new
      const { error } = await supabase
        .from('scripts')
        .insert({
          name: 'Project Madara - Main Loader',
          description: 'Single, definitive loader with database-backed key validation using existing validate-key endpoint. Clean, consolidated system. Auto-deployed.',
          content: content,
          script_type: 'main_loader',
          is_main_script: true,
          storage_type: 'database',
          access_level: 'public',
          requires_valid_key: false,
          category: 'utility',
          tags: ['Main Loader', 'Auto-Deploy', 'Authentication'],
          executor: 'All Executors',
          version: '1.0.0',
          file_size_bytes: Buffer.byteLength(content, 'utf8'),
          content_hash: contentHash,
          uploaded_by: 'auto-deploy'
        });
        
      if (error) throw error;
      log('Main loader created successfully', 'success');
    }
    
    return true;
  } catch (error) {
    log(`Failed to deploy main loader: ${error.message}`, 'error');
    return false;
  }
}

// 2. Deploy Game Scripts from Roblox_Script folder
async function deployGameScripts() {
  log('Deploying game scripts from Roblox_Script folder...', 'info');
  
  const scriptsDir = path.join(projectRoot, 'Roblox_Script');
  
  if (!fs.existsSync(scriptsDir)) {
    log('Roblox_Script folder not found, creating it...', 'warn');
    fs.mkdirSync(scriptsDir, { recursive: true });
    return true;
  }
  
  const files = fs.readdirSync(scriptsDir).filter(file => file.endsWith('.lua'));
  
  if (files.length === 0) {
    log('No .lua files found in Roblox_Script folder', 'warn');
    return true;
  }
  
  let successCount = 0;
  let errorCount = 0;
  
  for (const file of files) {
    try {
      const placeId = parseInt(path.basename(file, '.lua'));
      
      if (isNaN(placeId)) {
        log(`Skipping ${file} - filename must be a valid PlaceId`, 'warn');
        continue;
      }
      
      const filePath = path.join(scriptsDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      const contentHash = crypto.createHash('sha256').update(content).digest('hex');
      
      // Try to get game name from script comments
      let gameName = `Game ${placeId}`;
      try {
        // First try to get from the first comment line
        const firstLineMatch = content.match(/^-- (.+)/);
        if (firstLineMatch && !firstLineMatch[1].includes('http') && !firstLineMatch[1].includes('PlaceId')) {
          gameName = firstLineMatch[1].trim();
        } else {
          // Try to extract from "Game:" comment but clean it up
          const gameMatch = content.match(/-- Game: (.+)/);
          if (gameMatch) {
            let extractedName = gameMatch[1].trim();
            // If it's a URL, extract the game name from the end
            if (extractedName.includes('roblox.com/games/')) {
              const urlParts = extractedName.split('/');
              const lastPart = urlParts[urlParts.length - 1];
              if (lastPart && lastPart !== placeId.toString()) {
                // Convert URL-style name to readable name
                gameName = lastPart.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
              }
            } else {
              gameName = extractedName;
            }
          }
        }
      } catch (e) {
        // Fallback to PlaceId
      }
      
      // Check if script exists
      const { data: existing } = await supabase
        .from('scripts')
        .select('id, content_hash')
        .eq('place_id', placeId)
        .eq('script_type', 'game_specific')
        .single();
        
      if (existing) {
        if (existing.content_hash === contentHash) {
          log(`${file} unchanged, checking game registry...`, 'info');

          // Check if game registry entry exists
          const { data: registryEntry } = await supabase
            .from('game_registry')
            .select('id')
            .eq('place_id', placeId)
            .single();

          if (!registryEntry) {
            log(`Adding missing game registry entry for ${file}`, 'info');

            // Add missing game registry entry
            const { error: registryError } = await supabase
              .from('game_registry')
              .insert({
                place_id: placeId,
                game_name: gameName,
                game_description: `Auto-deployed script for ${gameName} (PlaceId: ${placeId})`,
                script_id: existing.id,
                is_supported: true,
                support_level: 'full',
                genre: 'Auto-Deploy',
                last_script_update: new Date().toISOString()
              });

            if (registryError) {
              log(`Warning: Failed to add registry entry for ${gameName}: ${registryError.message}`, 'warn');
            } else {
              log(`✅ Added game registry entry for ${gameName}`, 'success');
            }
          } else {
            log(`Game registry entry exists for ${file}`, 'info');
          }

          successCount++;
          continue;
        }
        
        // Update existing script
        const { error } = await supabase
          .from('scripts')
          .update({
            content: content,
            content_hash: contentHash,
            file_size_bytes: Buffer.byteLength(content, 'utf8'),
            updated_at: new Date().toISOString(),
            game_name: gameName
          })
          .eq('id', existing.id);

        if (error) throw error;

        // Also update the game registry entry with correct name
        const { error: registryUpdateError } = await supabase
          .from('game_registry')
          .update({
            game_name: gameName,
            game_description: `Auto-deployed script for ${gameName} (PlaceId: ${placeId})`,
            last_script_update: new Date().toISOString()
          })
          .eq('place_id', placeId);

        if (registryUpdateError) {
          log(`Warning: Failed to update registry for ${gameName}: ${registryUpdateError.message}`, 'warn');
        }

        log(`Updated ${file} (${gameName})`, 'success');
      } else {
        // Create new script
        const { data: script, error: scriptError } = await supabase
          .from('scripts')
          .insert({
            name: `${gameName} Script`,
            description: `Auto-deployed script for ${gameName} (PlaceId: ${placeId})`,
            content: content,
            script_type: 'game_specific',
            place_id: placeId,
            game_name: gameName,
            storage_type: 'database',
            access_level: 'authenticated',
            requires_valid_key: true,
            category: 'game',
            tags: ['Game Specific', 'Auto-Deploy', gameName],
            executor: 'All Executors',
            version: '1.0.0',
            file_size_bytes: Buffer.byteLength(content, 'utf8'),
            content_hash: contentHash,
            uploaded_by: 'auto-deploy'
          })
          .select()
          .single();
          
        if (scriptError) throw scriptError;
        
        // Add to game registry
        const { error: registryError } = await supabase
          .from('game_registry')
          .upsert({
            place_id: placeId,
            game_name: gameName,
            game_description: `Auto-deployed script for ${gameName}`,
            script_id: script.id,
            is_supported: true,
            support_level: 'full',
            genre: 'Auto-Deploy',
            last_script_update: new Date().toISOString()
          }, {
            onConflict: 'place_id'
          });
          
        if (registryError) {
          log(`Warning: Failed to update registry for ${gameName}: ${registryError.message}`, 'warn');
        }
        
        log(`Created ${file} (${gameName})`, 'success');
      }
      
      successCount++;
    } catch (error) {
      log(`Failed to deploy ${file}: ${error.message}`, 'error');
      errorCount++;
    }
  }
  
  log(`Game scripts deployment complete: ${successCount} success, ${errorCount} errors`, 
      errorCount > 0 ? 'warn' : 'success');
  
  return errorCount === 0;
}

// 3. Update database schema if needed
async function updateDatabaseSchema() {
  log('Checking database schema...', 'info');
  
  try {
    // Check if new columns exist
    const { error } = await supabase
      .from('scripts')
      .select('script_type, place_id, is_main_script')
      .limit(1);
      
    if (error && error.message.includes('column')) {
      log('Database schema needs updating - please run database migration manually', 'warn');
      return false;
    }
    
    log('Database schema is up to date', 'success');
    return true;
  } catch (error) {
    log(`Database schema check failed: ${error.message}`, 'error');
    return false;
  }
}

// 4. Cleanup old/unused scripts
async function cleanupOldScripts() {
  log('Cleaning up old scripts...', 'info');
  
  try {
    // Get all game scripts from database
    const { data: dbScripts } = await supabase
      .from('scripts')
      .select('id, place_id, name')
      .eq('script_type', 'game_specific')
      .eq('uploaded_by', 'auto-deploy');
      
    if (!dbScripts) return true;
    
    // Get all files in Roblox_Script folder
    const scriptsDir = path.join(projectRoot, 'Roblox_Script');
    const files = fs.existsSync(scriptsDir) 
      ? fs.readdirSync(scriptsDir).filter(file => file.endsWith('.lua'))
      : [];
    
    const filePlaceIds = files.map(file => parseInt(path.basename(file, '.lua'))).filter(id => !isNaN(id));
    
    // Find scripts in database that don't have corresponding files
    const orphanedScripts = dbScripts.filter(script => !filePlaceIds.includes(script.place_id));
    
    if (orphanedScripts.length > 0) {
      log(`Found ${orphanedScripts.length} orphaned scripts, removing...`, 'info');
      
      for (const script of orphanedScripts) {
        const { error } = await supabase
          .from('scripts')
          .delete()
          .eq('id', script.id);
          
        if (error) {
          log(`Failed to remove orphaned script ${script.name}: ${error.message}`, 'warn');
        } else {
          log(`Removed orphaned script: ${script.name}`, 'success');
        }
      }
    } else {
      log('No orphaned scripts found', 'info');
    }
    
    return true;
  } catch (error) {
    log(`Cleanup failed: ${error.message}`, 'error');
    return false;
  }
}

// Main deployment function
async function autoDeploy() {
  log('🚀 Starting automated deployment...', 'info');
  log('=====================================', 'info');
  
  const tasks = [
    { name: 'Database Schema Check', fn: updateDatabaseSchema },
    { name: 'Main Loader Deployment', fn: deployMainLoader },
    { name: 'Game Scripts Deployment', fn: deployGameScripts },
    { name: 'Cleanup Old Scripts', fn: cleanupOldScripts }
  ];
  
  let allSuccess = true;
  
  for (const task of tasks) {
    log(`\nRunning: ${task.name}`, 'info');
    const success = await task.fn();
    if (!success) {
      allSuccess = false;
    }
  }
  
  log('\n=====================================', 'info');
  
  if (allSuccess) {
    log('🎉 Automated deployment completed successfully!', 'success');
    log('\n📋 Your centralized script system is ready!', 'info');
    log('Users can now use this single loadstring for ALL games:', 'info');
    log('loadstring(game:HttpGet("https://projectmadara.com/.netlify/functions/secure-script?action=get_main_loader"))()', 'info');
  } else {
    log('⚠️ Deployment completed with some warnings/errors', 'warn');
    log('Check the logs above for details', 'info');
  }
  
  return allSuccess;
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  autoDeploy()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      log(`Deployment failed: ${error.message}`, 'error');
      process.exit(1);
    });
}

export { autoDeploy };
