import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addSimpleMainLoader() {
  console.log('Adding simple main loader script to database...');
  
  try {
    // Read the simple main loader script
    const scriptPath = path.join(process.cwd(), 'scripts', 'simple-main-loader.lua');
    const content = fs.readFileSync(scriptPath, 'utf8');
    const contentHash = crypto.createHash('sha256').update(content).digest('hex');
    
    // Delete existing main loader if it exists
    const { data: existing } = await supabase
      .from('scripts')
      .select('id')
      .eq('script_type', 'main_loader')
      .eq('is_main_script', true);
      
    if (existing && existing.length > 0) {
      console.log('Removing existing main loader...');
      await supabase
        .from('scripts')
        .delete()
        .eq('script_type', 'main_loader')
        .eq('is_main_script', true);
    }
    
    console.log('Creating new simple main loader...');
    
    const { data, error } = await supabase
      .from('scripts')
      .insert({
        name: 'Project Madara - Simple Main Loader',
        description: 'Simple centralized authentication and game script loader. Compatible with all executors. Handles key validation once, then dynamically loads game-specific scripts based on PlaceId.',
        content: content,
        script_type: 'main_loader',
        is_main_script: true,
        storage_type: 'database',
        access_level: 'public', // Main loader needs to be publicly accessible
        requires_valid_key: false, // Authentication happens within the script
        category: 'utility',
        tags: ['Main Loader', 'Authentication', 'Dynamic Loading', 'PlaceId', 'Simple'],
        executor: 'All Executors',
        version: '1.1.0',
        file_size_bytes: Buffer.byteLength(content, 'utf8'),
        content_hash: contentHash,
        uploaded_by: 'system'
      })
      .select()
      .single();
      
    if (error) {
      console.error('Failed to add simple main loader:', error);
      return;
    }
    
    console.log('✅ Simple main loader script added successfully');
    console.log(`   Script ID: ${data.id}`);
    console.log(`   Size: ${Math.round(data.file_size_bytes / 1024)}KB`);
    
    console.log('\n📋 Simple Main Loader Usage:');
    console.log('Users should now use this single loadstring for all games:');
    console.log('loadstring(game:HttpGet("https://projectmadara.com/.netlify/functions/secure-script?action=get_main_loader"))()');

    console.log('\n🔧 Debugging:');
    console.log('If you get "attempt to call a nil value" error, try this debug script first:');
    console.log('loadstring(game:HttpGet("https://raw.githubusercontent.com/your-repo/debug-main-loader.lua"))()');
    
  } catch (error) {
    console.error('Error processing simple main loader:', error);
  }
}

// Run the script
addSimpleMainLoader().catch(console.error);
