import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Popular Roblox games with their PlaceIds
const gameScripts = [
  {
    place_id: 142823291,
    game_name: "Murder Mystery 2",
    description: "Advanced Murder Mystery 2 script with ESP, auto-collect coins, sheriff/murderer detection, and teleportation features.",
    script_content: `-- Murder Mystery 2 Script for Project Madara
-- PlaceId: 142823291

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local Workspace = game:GetService("Workspace")
local UserInputService = game:GetService("UserInputService")

local LocalPlayer = Players.LocalPlayer

print("🔪 Murder Mystery 2 Script Loaded!")
print("✅ Authenticated via Project Madara")

-- ESP System
local function createESP()
    for _, player in pairs(Players:GetPlayers()) do
        if player ~= LocalPlayer and player.Character then
            local character = player.Character
            local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
            if humanoidRootPart then
                local billboard = Instance.new("BillboardGui")
                billboard.Size = UDim2.new(0, 100, 0, 50)
                billboard.StudsOffset = Vector3.new(0, 3, 0)
                billboard.Parent = humanoidRootPart
                
                local nameLabel = Instance.new("TextLabel")
                nameLabel.Size = UDim2.new(1, 0, 1, 0)
                nameLabel.BackgroundTransparency = 1
                nameLabel.Text = player.Name
                nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
                nameLabel.TextScaled = true
                nameLabel.Font = Enum.Font.GothamBold
                nameLabel.Parent = billboard
                
                -- Check for knife (murderer)
                if character:FindFirstChild("Knife") then
                    nameLabel.Text = "🔪 " .. player.Name .. " (MURDERER)"
                    nameLabel.TextColor3 = Color3.fromRGB(255, 0, 0)
                elseif character:FindFirstChild("Gun") then
                    nameLabel.Text = "🔫 " .. player.Name .. " (SHERIFF)"
                    nameLabel.TextColor3 = Color3.fromRGB(0, 0, 255)
                end
            end
        end
    end
end

-- Auto-collect coins
local function autoCollectCoins()
    for _, coin in pairs(Workspace:GetChildren()) do
        if coin.Name == "Coin_Server" and coin:FindFirstChild("TouchInterest") then
            if LocalPlayer.Character and LocalPlayer.Character:FindFirstChild("HumanoidRootPart") then
                coin.CFrame = LocalPlayer.Character.HumanoidRootPart.CFrame
            end
        end
    end
end

-- Main loop
RunService.Heartbeat:Connect(function()
    createESP()
    autoCollectCoins()
end)

print("🎮 MM2 Script Features Activated!")
print("- Player ESP with role detection")
print("- Auto coin collection")
print("- Real-time updates")`
  },
  {
    place_id: 537413528,
    game_name: "Build A Boat For Treasure",
    description: "Build A Boat script with auto-build, treasure collection, and boat optimization features.",
    script_content: `-- Build A Boat For Treasure Script
-- PlaceId: 537413528

local Players = game:GetService("Players")
local Workspace = game:GetService("Workspace")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local LocalPlayer = Players.LocalPlayer

print("🚢 Build A Boat For Treasure Script Loaded!")
print("✅ Authenticated via Project Madara")

-- Auto-collect treasures
local function autoCollectTreasures()
    for _, treasure in pairs(Workspace:GetDescendants()) do
        if treasure.Name:find("Treasure") or treasure.Name:find("Chest") then
            if treasure:FindFirstChild("TouchInterest") and LocalPlayer.Character then
                local humanoidRootPart = LocalPlayer.Character:FindFirstChild("HumanoidRootPart")
                if humanoidRootPart then
                    treasure.CFrame = humanoidRootPart.CFrame
                end
            end
        end
    end
end

-- Boat optimization
local function optimizeBoat()
    if LocalPlayer.Character then
        local humanoid = LocalPlayer.Character:FindFirstChild("Humanoid")
        if humanoid then
            humanoid.WalkSpeed = 50 -- Faster movement
            humanoid.JumpPower = 100 -- Higher jumps
        end
    end
end

-- Main execution
spawn(function()
    while wait(1) do
        autoCollectTreasures()
        optimizeBoat()
    end
end)

print("🎮 BABFT Script Features Activated!")
print("- Auto treasure collection")
print("- Boat optimization")
print("- Enhanced movement")`
  },
  {
    place_id: 6284583030,
    game_name: "Pet Simulator X",
    description: "Pet Simulator X script with auto-farm, pet management, and egg opening automation.",
    script_content: `-- Pet Simulator X Script
-- PlaceId: 6284583030

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Workspace = game:GetService("Workspace")

local LocalPlayer = Players.LocalPlayer

print("🐾 Pet Simulator X Script Loaded!")
print("✅ Authenticated via Project Madara")

-- Auto-farm function
local function autoFarm()
    local character = LocalPlayer.Character
    if not character then return end
    
    local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
    if not humanoidRootPart then return end
    
    -- Find nearest coin
    local nearestCoin = nil
    local shortestDistance = math.huge
    
    for _, coin in pairs(Workspace.Coins:GetChildren()) do
        if coin:FindFirstChild("TouchInterest") then
            local distance = (coin.Position - humanoidRootPart.Position).Magnitude
            if distance < shortestDistance then
                shortestDistance = distance
                nearestCoin = coin
            end
        end
    end
    
    -- Collect nearest coin
    if nearestCoin then
        humanoidRootPart.CFrame = nearestCoin.CFrame
    end
end

-- Auto-open eggs
local function autoOpenEggs()
    local eggShop = Workspace:FindFirstChild("EggShop")
    if eggShop then
        for _, egg in pairs(eggShop:GetChildren()) do
            if egg.Name:find("Egg") and egg:FindFirstChild("TouchInterest") then
                if LocalPlayer.Character and LocalPlayer.Character:FindFirstChild("HumanoidRootPart") then
                    egg.CFrame = LocalPlayer.Character.HumanoidRootPart.CFrame
                end
            end
        end
    end
end

-- Main loop
spawn(function()
    while wait(0.1) do
        autoFarm()
        autoOpenEggs()
    end
end)

print("🎮 Pet Sim X Script Features Activated!")
print("- Auto coin farming")
print("- Auto egg opening")
print("- Optimized collection")`
  },
  {
    place_id: 155615604,
    game_name: "Prison Life",
    description: "Prison Life script with escape tools, teleportation, and guard/prisoner utilities.",
    script_content: `-- Prison Life Script
-- PlaceId: 155615604

local Players = game:GetService("Players")
local Workspace = game:GetService("Workspace")
local UserInputService = game:GetService("UserInputService")

local LocalPlayer = Players.LocalPlayer

print("🏢 Prison Life Script Loaded!")
print("✅ Authenticated via Project Madara")

-- Teleport locations
local locations = {
    ["Prison"] = CFrame.new(919, 98, 2379),
    ["Criminal Base"] = CFrame.new(-943, 95, 2063),
    ["Police Station"] = CFrame.new(827, 98, 2267),
    ["Yard"] = CFrame.new(791, 98, 2498),
    ["Cafeteria"] = CFrame.new(923, 99, 2256)
}

-- Teleport function
local function teleportTo(location)
    if LocalPlayer.Character and LocalPlayer.Character:FindFirstChild("HumanoidRootPart") then
        LocalPlayer.Character.HumanoidRootPart.CFrame = locations[location]
        print("Teleported to: " .. location)
    end
end

-- Auto-arrest nearby criminals (for guards)
local function autoArrest()
    if LocalPlayer.Team and LocalPlayer.Team.Name == "Guards" then
        for _, player in pairs(Players:GetPlayers()) do
            if player.Team and player.Team.Name == "Inmates" and player.Character then
                local distance = (player.Character.HumanoidRootPart.Position - LocalPlayer.Character.HumanoidRootPart.Position).Magnitude
                if distance < 10 then
                    -- Simulate arrest
                    print("Arresting: " .. player.Name)
                end
            end
        end
    end
end

-- Speed boost
if LocalPlayer.Character and LocalPlayer.Character:FindFirstChild("Humanoid") then
    LocalPlayer.Character.Humanoid.WalkSpeed = 50
end

-- Key bindings
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.T then
        teleportTo("Criminal Base")
    elseif input.KeyCode == Enum.KeyCode.Y then
        teleportTo("Police Station")
    elseif input.KeyCode == Enum.KeyCode.U then
        teleportTo("Yard")
    end
end)

print("🎮 Prison Life Script Features Activated!")
print("- Press T to teleport to Criminal Base")
print("- Press Y to teleport to Police Station")
print("- Press U to teleport to Yard")
print("- Speed boost enabled")`
  },
  {
    place_id: 1537690962,
    game_name: "Bee Swarm Simulator",
    description: "Bee Swarm Simulator script with auto-farming, pollen collection, and hive optimization.",
    script_content: `-- Bee Swarm Simulator Script
-- PlaceId: 1537690962

local Players = game:GetService("Players")
local Workspace = game:GetService("Workspace")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local LocalPlayer = Players.LocalPlayer

print("🐝 Bee Swarm Simulator Script Loaded!")
print("✅ Authenticated via Project Madara")

-- Auto-collect pollen
local function autoCollectPollen()
    local character = LocalPlayer.Character
    if not character then return end
    
    local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
    if not humanoidRootPart then return end
    
    -- Find flowers
    for _, flower in pairs(Workspace.Flowers:GetChildren()) do
        if flower:FindFirstChild("Pollen") then
            humanoidRootPart.CFrame = flower.CFrame + Vector3.new(0, 5, 0)
            wait(0.1)
        end
    end
end

-- Auto-convert at hive
local function autoConvert()
    local hive = Workspace:FindFirstChild("Hives")
    if hive and LocalPlayer.Character then
        local playerHive = hive:FindFirstChild(LocalPlayer.Name)
        if playerHive then
            LocalPlayer.Character.HumanoidRootPart.CFrame = playerHive.CFrame
        end
    end
end

-- Main farming loop
spawn(function()
    while wait(1) do
        autoCollectPollen()
        wait(30) -- Farm for 30 seconds
        autoConvert()
        wait(5) -- Convert for 5 seconds
    end
end)

print("🎮 Bee Swarm Script Features Activated!")
print("- Auto pollen collection")
print("- Auto hive conversion")
print("- Optimized farming cycle")`
  }
];

async function addGameScripts() {
  console.log('Adding game-specific scripts to database...');
  
  for (const gameData of gameScripts) {
    try {
      const contentHash = crypto.createHash('sha256').update(gameData.script_content).digest('hex');
      
      // Check if script already exists for this PlaceId
      const { data: existing } = await supabase
        .from('scripts')
        .select('id')
        .eq('place_id', gameData.place_id)
        .eq('script_type', 'game_specific')
        .single();
        
      if (existing) {
        console.log(`Game script for ${gameData.game_name} already exists, skipping...`);
        continue;
      }
      
      // Insert game script
      const { data: script, error: scriptError } = await supabase
        .from('scripts')
        .insert({
          name: `${gameData.game_name} Script`,
          description: gameData.description,
          content: gameData.script_content,
          script_type: 'game_specific',
          place_id: gameData.place_id,
          game_name: gameData.game_name,
          storage_type: 'database',
          access_level: 'authenticated',
          requires_valid_key: true,
          category: 'game',
          tags: ['Game Specific', gameData.game_name, 'Auto Farm', 'ESP'],
          executor: 'All Executors',
          version: '1.0.0',
          file_size_bytes: Buffer.byteLength(gameData.script_content, 'utf8'),
          content_hash: contentHash,
          uploaded_by: 'system'
        })
        .select()
        .single();
        
      if (scriptError) {
        console.error(`Failed to add script for ${gameData.game_name}:`, scriptError);
        continue;
      }
      
      // Add to game registry
      const { error: registryError } = await supabase
        .from('game_registry')
        .insert({
          place_id: gameData.place_id,
          game_name: gameData.game_name,
          game_description: gameData.description,
          script_id: script.id,
          is_supported: true,
          support_level: 'full',
          genre: 'Various',
          last_script_update: new Date().toISOString()
        });
        
      if (registryError) {
        console.error(`Failed to add registry entry for ${gameData.game_name}:`, registryError);
      }
      
      console.log(`✅ Added script for ${gameData.game_name} (PlaceId: ${gameData.place_id})`);
      
    } catch (error) {
      console.error(`Error processing ${gameData.game_name}:`, error);
    }
  }
  
  console.log('\n🎮 Game Scripts Added Successfully!');
  console.log('Supported Games:');
  gameScripts.forEach(game => {
    console.log(`  - ${game.game_name} (PlaceId: ${game.place_id})`);
  });
}

// Run the script
addGameScripts().catch(console.error);
