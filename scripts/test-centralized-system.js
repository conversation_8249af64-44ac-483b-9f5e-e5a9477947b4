/**
 * Test Script for Centralized Game Script System
 * Tests the complete flow from main loader to game-specific script loading
 */

import fetch from 'node-fetch';

const BASE_URL = process.env.TEST_URL || 'http://localhost:8888';
const API_BASE = `${BASE_URL}/.netlify/functions`;

// Test configuration
const TEST_CONFIG = {
  testKey: null,
  testHWID: 'TEST_HWID_' + Date.now(),
  testPlaceIds: [142823291, 537413528, 6284583030], // MM2, BABFT, Pet Sim X
  mainLoaderContent: null
};

// Utility functions
const log = (message, type = 'info') => {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
};

// Test functions
async function testMainLoaderAccess() {
  log('Testing main loader script access...', 'info');
  
  try {
    const response = await fetch(`${API_BASE}/secure-script?action=get_main_loader`);
    
    if (!response.ok) {
      throw new Error(`Main loader access failed: ${response.status}`);
    }
    
    const content = await response.text();
    TEST_CONFIG.mainLoaderContent = content;
    
    if (content.includes('PROJECT MADARA - CENTRALIZED MAIN LOADER')) {
      log('Main loader script retrieved successfully', 'success');
      log(`Content size: ${Math.round(content.length / 1024)}KB`, 'info');
      return true;
    } else {
      log('Invalid main loader content', 'error');
      return false;
    }
  } catch (error) {
    log(`Main loader access error: ${error.message}`, 'error');
    return false;
  }
}

async function testKeyGeneration() {
  log('Testing key generation for centralized system...', 'info');
  
  try {
    const response = await fetch(`${API_BASE}/generate-key`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        campaignId: 'centralized-test',
        deviceData: { hwid: TEST_CONFIG.testHWID },
        behaviorData: { test: true },
        sessionId: 'centralized-session-' + Date.now()
      })
    });

    if (!response.ok) {
      throw new Error(`Key generation failed: ${response.status}`);
    }

    const data = await response.json();
    TEST_CONFIG.testKey = data.key;
    
    log(`Key generated successfully: ${data.key}`, 'success');
    return true;
  } catch (error) {
    log(`Key generation failed: ${error.message}`, 'error');
    return false;
  }
}

async function testGameScriptLoading(placeId) {
  log(`Testing game script loading for PlaceId: ${placeId}...`, 'info');
  
  try {
    const response = await fetch(`${API_BASE}/secure-script?action=load_game_script&place_id=${placeId}&key_code=${TEST_CONFIG.testKey}&hwid=${TEST_CONFIG.testHWID}`);
    
    if (!response.ok) {
      const errorData = await response.json();
      if (response.status === 404) {
        log(`No script found for PlaceId ${placeId} (expected for unsupported games)`, 'info');
        return true; // This is expected behavior
      }
      throw new Error(`Game script loading failed: ${errorData.error || response.status}`);
    }
    
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      // Response contains script URL for large scripts
      const data = await response.json();
      if (data.script_url) {
        log(`Large script URL generated for PlaceId ${placeId}`, 'success');
        
        // Test accessing the script via URL
        const scriptResponse = await fetch(data.script_url);
        if (scriptResponse.ok) {
          const scriptContent = await scriptResponse.text();
          log(`Script content retrieved via URL (${Math.round(scriptContent.length / 1024)}KB)`, 'success');
          return true;
        } else {
          log('Failed to retrieve script via generated URL', 'error');
          return false;
        }
      }
    } else {
      // Direct script content
      const content = await response.text();
      if (content && content.includes('-- ') && content.includes('PlaceId: ' + placeId)) {
        log(`Game script loaded directly for PlaceId ${placeId} (${Math.round(content.length / 1024)}KB)`, 'success');
        return true;
      } else {
        log(`Invalid script content for PlaceId ${placeId}`, 'error');
        return false;
      }
    }
    
    return false;
  } catch (error) {
    log(`Game script loading error for PlaceId ${placeId}: ${error.message}`, 'error');
    return false;
  }
}

async function testGameRegistry() {
  log('Testing game registry API...', 'info');
  
  try {
    const response = await fetch(`${API_BASE}/game-registry`);
    
    if (!response.ok) {
      throw new Error(`Game registry access failed: ${response.status}`);
    }
    
    const games = await response.json();
    
    if (Array.isArray(games) && games.length > 0) {
      log(`Game registry retrieved successfully (${games.length} games)`, 'success');
      
      // Log some sample games
      games.slice(0, 3).forEach(game => {
        log(`  - ${game.game_name} (PlaceId: ${game.place_id}, Support: ${game.support_level})`, 'info');
      });
      
      return true;
    } else {
      log('Empty or invalid game registry', 'error');
      return false;
    }
  } catch (error) {
    log(`Game registry error: ${error.message}`, 'error');
    return false;
  }
}

async function testInvalidPlaceId() {
  log('Testing invalid PlaceId handling...', 'info');
  
  try {
    const invalidPlaceId = 999999999; // Non-existent PlaceId
    const response = await fetch(`${API_BASE}/secure-script?action=load_game_script&place_id=${invalidPlaceId}&key_code=${TEST_CONFIG.testKey}&hwid=${TEST_CONFIG.testHWID}`);
    
    if (response.status === 404) {
      log('Invalid PlaceId correctly rejected', 'success');
      return true;
    } else {
      log('Invalid PlaceId was not properly handled', 'error');
      return false;
    }
  } catch (error) {
    log(`Invalid PlaceId test error: ${error.message}`, 'error');
    return false;
  }
}

async function testUnauthorizedGameAccess() {
  log('Testing unauthorized game script access...', 'info');
  
  try {
    const response = await fetch(`${API_BASE}/secure-script?action=load_game_script&place_id=${TEST_CONFIG.testPlaceIds[0]}&key_code=invalid_key&hwid=invalid_hwid`);
    
    if (!response.ok) {
      log('Unauthorized game access correctly rejected', 'success');
      return true;
    } else {
      log('Unauthorized game access was allowed (security issue!)', 'error');
      return false;
    }
  } catch (error) {
    log(`Unauthorized game access test error: ${error.message}`, 'error');
    return false;
  }
}

async function testMainLoaderFlow() {
  log('Testing complete main loader flow simulation...', 'info');
  
  try {
    // Simulate the flow that would happen in Roblox
    
    // 1. Get main loader
    const loaderResponse = await fetch(`${API_BASE}/secure-script?action=get_main_loader`);
    if (!loaderResponse.ok) {
      throw new Error('Failed to get main loader');
    }
    
    const loaderContent = await loaderResponse.text();
    
    // 2. Simulate key validation (this would happen inside the loader)
    const keyValidation = await fetch(`${API_BASE}/validate-key`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        keyCode: TEST_CONFIG.testKey,
        hwid: TEST_CONFIG.testHWID,
        action: 'validate'
      })
    });
    
    if (!keyValidation.ok) {
      throw new Error('Key validation failed in flow');
    }
    
    // 3. Simulate game script loading for each test PlaceId
    for (const placeId of TEST_CONFIG.testPlaceIds) {
      const gameScriptResponse = await fetch(`${API_BASE}/secure-script?action=load_game_script&place_id=${placeId}&key_code=${TEST_CONFIG.testKey}&hwid=${TEST_CONFIG.testHWID}`);
      
      if (gameScriptResponse.ok) {
        log(`Flow test: Game script loaded for PlaceId ${placeId}`, 'success');
      } else {
        log(`Flow test: No script for PlaceId ${placeId} (expected)`, 'info');
      }
    }
    
    log('Complete main loader flow simulation successful', 'success');
    return true;
    
  } catch (error) {
    log(`Main loader flow test error: ${error.message}`, 'error');
    return false;
  }
}

// Main test runner
async function runCentralizedTests() {
  log('Starting Centralized Game Script System Tests', 'info');
  log('===============================================', 'info');
  
  const tests = [
    { name: 'Main Loader Access', fn: testMainLoaderAccess },
    { name: 'Key Generation', fn: testKeyGeneration },
    { name: 'Game Registry API', fn: testGameRegistry },
    { name: 'Game Script Loading (MM2)', fn: () => testGameScriptLoading(TEST_CONFIG.testPlaceIds[0]) },
    { name: 'Game Script Loading (BABFT)', fn: () => testGameScriptLoading(TEST_CONFIG.testPlaceIds[1]) },
    { name: 'Game Script Loading (Pet Sim X)', fn: () => testGameScriptLoading(TEST_CONFIG.testPlaceIds[2]) },
    { name: 'Invalid PlaceId Handling', fn: testInvalidPlaceId },
    { name: 'Unauthorized Game Access Prevention', fn: testUnauthorizedGameAccess },
    { name: 'Complete Main Loader Flow', fn: testMainLoaderFlow }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    log(`\nRunning test: ${test.name}`, 'info');
    
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      log(`Test ${test.name} threw an error: ${error.message}`, 'error');
      failed++;
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  log('\n===============================================', 'info');
  log(`Test Results: ${passed} passed, ${failed} failed`, passed === tests.length ? 'success' : 'error');
  
  if (passed === tests.length) {
    log('🎉 All tests passed! Centralized system is working correctly.', 'success');
    log('\n📋 User Instructions:', 'info');
    log('Users should now use this single loadstring for ALL games:', 'info');
    log(`loadstring(game:HttpGet("${BASE_URL}/.netlify/functions/secure-script?action=get_main_loader"))()`, 'info');
  } else {
    log('⚠️ Some tests failed. Please check the implementation.', 'error');
  }
  
  return passed === tests.length;
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runCentralizedTests().catch(console.error);
}

export { runCentralizedTests, TEST_CONFIG };
