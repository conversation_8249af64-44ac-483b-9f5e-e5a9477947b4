#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to test the authentication flow and debug issues
 */

import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const BASE_URL = 'http://localhost:8888'; // Adjust if your dev server runs on a different port

async function testAuthentication() {
  try {
    console.log('🧪 Testing authentication flow...\n');

    // Step 1: Test login
    console.log('1️⃣ Testing login...');
    const loginResponse = await fetch(`${BASE_URL}/.netlify/functions/security?action=login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'Sabin07',
        password: 'IMightBeCoolTho'
      })
    });

    console.log('Login response status:', loginResponse.status);
    
    if (!loginResponse.ok) {
      const errorText = await loginResponse.text();
      console.log('❌ Login failed:', errorText);
      return;
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful!');
    console.log('Session token received:', loginData.sessionToken ? 'Yes' : 'No');
    console.log('User role:', loginData.role);
    console.log('Username:', loginData.username);

    if (!loginData.sessionToken) {
      console.log('❌ No session token received');
      return;
    }

    // Step 2: Test session validation
    console.log('\n2️⃣ Testing session validation...');
    const validateResponse = await fetch(`${BASE_URL}/.netlify/functions/security?action=validate-session`, {
      headers: {
        'x-session-token': loginData.sessionToken,
        'Content-Type': 'application/json',
      }
    });

    console.log('Validation response status:', validateResponse.status);
    
    if (validateResponse.ok) {
      const validateData = await validateResponse.json();
      console.log('✅ Session validation successful!');
      console.log('Validated user:', validateData.username);
      console.log('Validated role:', validateData.role);
    } else {
      const errorText = await validateResponse.text();
      console.log('❌ Session validation failed:', errorText);
    }

    // Step 3: Test security endpoints
    console.log('\n3️⃣ Testing security endpoints...');
    
    const endpoints = [
      'events',
      'ip-bans',
      'settings'
    ];

    for (const endpoint of endpoints) {
      console.log(`\n   Testing ${endpoint}...`);
      const response = await fetch(`${BASE_URL}/.netlify/functions/security?action=${endpoint}`, {
        headers: {
          'x-session-token': loginData.sessionToken,
          'Content-Type': 'application/json',
        }
      });

      console.log(`   ${endpoint} status:`, response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.log(`   ❌ ${endpoint} failed:`, errorText);
      } else {
        console.log(`   ✅ ${endpoint} successful`);
      }
    }

    console.log('\n🎉 Authentication test completed!');

  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

// Check if we're running a dev server
console.log('🚀 Starting authentication test...');
console.log('📍 Testing against:', BASE_URL);
console.log('⚠️  Make sure your dev server is running (npm run dev)\n');

testAuthentication();
