#!/usr/bin/env node

/**
 * Migration script to hash existing plain text passwords in admin_users table
 * This should be run once after implementing bcrypt authentication
 */

import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables: SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function hashPassword(password) {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
}

async function migratePasswords() {
  try {
    console.log('🔍 Fetching admin users with plain text passwords...');
    
    // Get all admin users
    const { data: adminUsers, error: fetchError } = await supabase
      .from('admin_users')
      .select('id, username, password_hash');

    if (fetchError) {
      throw new Error(`Failed to fetch admin users: ${fetchError.message}`);
    }

    if (!adminUsers || adminUsers.length === 0) {
      console.log('✅ No admin users found to migrate.');
      return;
    }

    console.log(`📋 Found ${adminUsers.length} admin users to process.`);

    for (const user of adminUsers) {
      try {
        // Check if password is already hashed (bcrypt hashes start with $2a$, $2b$, or $2y$)
        if (user.password_hash.startsWith('$2')) {
          console.log(`⏭️  Skipping ${user.username} - password already hashed`);
          continue;
        }

        console.log(`🔐 Hashing password for user: ${user.username}`);
        
        // Hash the plain text password
        const hashedPassword = await hashPassword(user.password_hash);
        
        // Update the user with the hashed password
        const { error: updateError } = await supabase
          .from('admin_users')
          .update({ password_hash: hashedPassword })
          .eq('id', user.id);

        if (updateError) {
          console.error(`❌ Failed to update password for ${user.username}: ${updateError.message}`);
          continue;
        }

        console.log(`✅ Successfully hashed password for ${user.username}`);
        
      } catch (userError) {
        console.error(`❌ Error processing user ${user.username}: ${userError.message}`);
      }
    }

    console.log('🎉 Password migration completed!');
    
  } catch (error) {
    console.error('💥 Migration failed:', error.message);
    process.exit(1);
  }
}

// Run the migration
console.log('🚀 Starting admin password migration...');
migratePasswords()
  .then(() => {
    console.log('✨ Migration script completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Migration script failed:', error.message);
    process.exit(1);
  });
