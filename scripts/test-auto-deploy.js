#!/usr/bin/env node

/**
 * Test version of auto-deploy that shows what would happen
 * without actually connecting to the database
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.dirname(__dirname);

// Utility functions
const log = (message, type = 'info') => {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warn' ? '⚠️' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
};

// Test main loader deployment
function testMainLoaderDeployment() {
  log('Testing main loader deployment...', 'info');
  
  try {
    const loaderPath = path.join(projectRoot, 'scripts', 'simple-main-loader.lua');
    
    if (!fs.existsSync(loaderPath)) {
      log('❌ Main loader script not found at: ' + loaderPath, 'error');
      return false;
    }
    
    const content = fs.readFileSync(loaderPath, 'utf8');
    const contentHash = crypto.createHash('sha256').update(content).digest('hex');
    const sizeKB = Math.round(Buffer.byteLength(content, 'utf8') / 1024);
    
    log(`✅ Main loader found (${sizeKB}KB, hash: ${contentHash.substring(0, 8)}...)`, 'success');
    log(`   Content preview: ${content.substring(0, 100)}...`, 'info');
    
    // Check for correct domain
    if (content.includes('projectmadara.com')) {
      log('✅ Correct domain (projectmadara.com) found in main loader', 'success');
    } else if (content.includes('checkingbefore.netlify.app')) {
      log('⚠️ Old domain found - should be updated to projectmadara.com', 'warn');
    } else {
      log('⚠️ No recognizable domain found in main loader', 'warn');
    }
    
    return true;
  } catch (error) {
    log(`❌ Error testing main loader: ${error.message}`, 'error');
    return false;
  }
}

// Test game scripts deployment
function testGameScriptsDeployment() {
  log('Testing game scripts deployment...', 'info');
  
  const scriptsDir = path.join(projectRoot, 'Roblox_Script');
  
  if (!fs.existsSync(scriptsDir)) {
    log('⚠️ Roblox_Script folder not found - would be created during deploy', 'warn');
    return true;
  }
  
  const files = fs.readdirSync(scriptsDir).filter(file => file.endsWith('.lua'));
  
  if (files.length === 0) {
    log('⚠️ No .lua files found in Roblox_Script folder', 'warn');
    return true;
  }
  
  log(`Found ${files.length} game script(s):`, 'info');
  
  let validScripts = 0;
  let invalidScripts = 0;
  
  for (const file of files) {
    try {
      const placeId = parseInt(path.basename(file, '.lua'));
      
      if (isNaN(placeId)) {
        log(`  ❌ ${file} - Invalid filename (must be PlaceId.lua)`, 'error');
        invalidScripts++;
        continue;
      }
      
      const filePath = path.join(scriptsDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      const contentHash = crypto.createHash('sha256').update(content).digest('hex');
      const sizeKB = Math.round(Buffer.byteLength(content, 'utf8') / 1024);
      
      // Extract game name from comments
      let gameName = `Game ${placeId}`;
      const nameMatch = content.match(/-- (.+)/);
      if (nameMatch) {
        gameName = nameMatch[1].trim();
      }
      
      log(`  ✅ ${file} - ${gameName} (${sizeKB}KB, hash: ${contentHash.substring(0, 8)}...)`, 'success');
      validScripts++;
      
    } catch (error) {
      log(`  ❌ ${file} - Error reading file: ${error.message}`, 'error');
      invalidScripts++;
    }
  }
  
  log(`Game scripts summary: ${validScripts} valid, ${invalidScripts} invalid`, 
      invalidScripts > 0 ? 'warn' : 'success');
  
  return invalidScripts === 0;
}

// Test build configuration
function testBuildConfiguration() {
  log('Testing build configuration...', 'info');
  
  try {
    // Check package.json
    const packagePath = path.join(projectRoot, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    if (packageJson.scripts && packageJson.scripts.build && packageJson.scripts.build.includes('deploy-scripts')) {
      log('✅ package.json build script configured correctly', 'success');
    } else {
      log('❌ package.json build script not configured for auto-deploy', 'error');
      return false;
    }
    
    if (packageJson.scripts && packageJson.scripts['deploy-scripts']) {
      log('✅ deploy-scripts command found in package.json', 'success');
    } else {
      log('❌ deploy-scripts command missing from package.json', 'error');
      return false;
    }
    
    // Check netlify.toml
    const netlifyPath = path.join(projectRoot, 'netlify.toml');
    if (fs.existsSync(netlifyPath)) {
      const netlifyConfig = fs.readFileSync(netlifyPath, 'utf8');
      if (netlifyConfig.includes('npm run build')) {
        log('✅ netlify.toml configured correctly', 'success');
      } else {
        log('⚠️ netlify.toml may need build command update', 'warn');
      }
    } else {
      log('⚠️ netlify.toml not found', 'warn');
    }
    
    return true;
  } catch (error) {
    log(`❌ Error testing build configuration: ${error.message}`, 'error');
    return false;
  }
}

// Test environment setup
function testEnvironmentSetup() {
  log('Testing environment setup...', 'info');
  
  const requiredEnvVars = ['SUPABASE_URL', 'SUPABASE_SERVICE_ROLE_KEY'];
  let allSet = true;
  
  for (const envVar of requiredEnvVars) {
    if (process.env[envVar]) {
      log(`✅ ${envVar} is set`, 'success');
    } else {
      log(`⚠️ ${envVar} not set (expected in local environment)`, 'warn');
      // This is expected locally, not an error
    }
  }
  
  log('ℹ️ Environment variables should be set in Netlify dashboard', 'info');
  return true;
}

// Main test function
async function testAutoDeploy() {
  log('🧪 Testing Automated Deployment System', 'info');
  log('=====================================', 'info');
  
  const tests = [
    { name: 'Build Configuration', fn: testBuildConfiguration },
    { name: 'Environment Setup', fn: testEnvironmentSetup },
    { name: 'Main Loader Deployment', fn: testMainLoaderDeployment },
    { name: 'Game Scripts Deployment', fn: testGameScriptsDeployment }
  ];
  
  let allPassed = true;
  
  for (const test of tests) {
    log(`\nTesting: ${test.name}`, 'info');
    const success = test.fn();
    if (!success) {
      allPassed = false;
    }
  }
  
  log('\n=====================================', 'info');
  
  if (allPassed) {
    log('🎉 All tests passed! Auto-deploy system is ready!', 'success');
    log('\n📋 Next steps:', 'info');
    log('1. Make sure environment variables are set in Netlify:', 'info');
    log('   - SUPABASE_URL', 'info');
    log('   - SUPABASE_SERVICE_ROLE_KEY', 'info');
    log('2. Run: netlify deploy --prod', 'info');
    log('3. Check build logs for auto-deploy output', 'info');
    log('\n🎮 After deploy, users can use:', 'info');
    log('loadstring(game:HttpGet("https://projectmadara.com/.netlify/functions/secure-script?action=get_main_loader"))()', 'info');
  } else {
    log('⚠️ Some tests failed. Check the issues above.', 'warn');
  }
  
  return allPassed;
}

// Run tests
testAutoDeploy()
  .then(success => process.exit(success ? 0 : 1))
  .catch(error => {
    log(`Test failed: ${error.message}`, 'error');
    process.exit(1);
  });
