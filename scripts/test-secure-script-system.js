/**
 * Test Script for Secure Script System
 * Tests the complete flow from key generation to script access
 */

import fetch from 'node-fetch';

const BASE_URL = process.env.TEST_URL || 'http://localhost:8888';
const API_BASE = `${BASE_URL}/.netlify/functions`;

// Test configuration
const TEST_CONFIG = {
  testKey: null,
  testHWID: 'TEST_HWID_' + Date.now(),
  testScriptId: null,
  testToken: null
};

// Utility functions
const log = (message, type = 'info') => {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
};

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Test functions
async function testKeyGeneration() {
  log('Testing key generation...', 'info');
  
  try {
    const response = await fetch(`${API_BASE}/generate-key`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        campaignId: 'test-campaign',
        deviceData: { hwid: TEST_CONFIG.testHWID },
        behaviorData: { test: true },
        sessionId: 'test-session-' + Date.now()
      })
    });

    if (!response.ok) {
      throw new Error(`Key generation failed: ${response.status}`);
    }

    const data = await response.json();
    TEST_CONFIG.testKey = data.key;
    
    log(`Key generated successfully: ${data.key}`, 'success');
    return true;
  } catch (error) {
    log(`Key generation failed: ${error.message}`, 'error');
    return false;
  }
}

async function testKeyValidation() {
  log('Testing key validation...', 'info');
  
  try {
    const response = await fetch(`${API_BASE}/validate-key`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        keyCode: TEST_CONFIG.testKey,
        hwid: TEST_CONFIG.testHWID,
        action: 'validate'
      })
    });

    if (!response.ok) {
      throw new Error(`Key validation failed: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.valid) {
      log('Key validation successful', 'success');
      return true;
    } else {
      log(`Key validation failed: ${data.error}`, 'error');
      return false;
    }
  } catch (error) {
    log(`Key validation error: ${error.message}`, 'error');
    return false;
  }
}

async function testScriptListing() {
  log('Testing script listing...', 'info');
  
  try {
    const response = await fetch(`${API_BASE}/scripts`);
    
    if (!response.ok) {
      throw new Error(`Script listing failed: ${response.status}`);
    }

    const scripts = await response.json();
    
    if (scripts.length > 0) {
      TEST_CONFIG.testScriptId = scripts[0].id;
      log(`Found ${scripts.length} scripts, using script: ${scripts[0].name}`, 'success');
      return true;
    } else {
      log('No scripts found in database', 'error');
      return false;
    }
  } catch (error) {
    log(`Script listing error: ${error.message}`, 'error');
    return false;
  }
}

async function testSecureLoadstringGeneration() {
  log('Testing secure loadstring generation...', 'info');
  
  try {
    const response = await fetch(`${API_BASE}/secure-script?action=generate_loadstring&script_id=${TEST_CONFIG.testScriptId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-Key-Code': TEST_CONFIG.testKey,
        'X-HWID': TEST_CONFIG.testHWID
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Loadstring generation failed: ${errorData.error || response.status}`);
    }

    const data = await response.json();
    
    if (data.loadstring && data.loadstring.includes('loadstring(game:HttpGet(')) {
      // Extract token from loadstring
      const tokenMatch = data.loadstring.match(/token=([^"]+)/);
      if (tokenMatch) {
        TEST_CONFIG.testToken = tokenMatch[1];
        log('Secure loadstring generated successfully', 'success');
        log(`Token extracted: ${TEST_CONFIG.testToken.substring(0, 20)}...`, 'info');
        return true;
      } else {
        log('Token not found in loadstring', 'error');
        return false;
      }
    } else {
      log('Invalid loadstring format', 'error');
      return false;
    }
  } catch (error) {
    log(`Loadstring generation error: ${error.message}`, 'error');
    return false;
  }
}

async function testScriptContentRetrieval() {
  log('Testing script content retrieval...', 'info');
  
  try {
    const response = await fetch(`${API_BASE}/secure-script?token=${TEST_CONFIG.testToken}`, {
      headers: {
        'Accept-Encoding': 'gzip'
      }
    });

    if (!response.ok) {
      throw new Error(`Script retrieval failed: ${response.status}`);
    }

    const content = await response.text();
    const contentSize = response.headers.get('x-content-size') || content.length;
    const isCompressed = response.headers.get('content-encoding') === 'gzip';
    
    if (content && content.length > 0 && !content.includes('Access denied')) {
      log(`Script content retrieved successfully (${contentSize} bytes${isCompressed ? ', compressed' : ''})`, 'success');
      log(`Content preview: ${content.substring(0, 100)}...`, 'info');
      return true;
    } else {
      log('Invalid or empty script content', 'error');
      return false;
    }
  } catch (error) {
    log(`Script retrieval error: ${error.message}`, 'error');
    return false;
  }
}

async function testTokenReuse() {
  log('Testing token reuse (should work within limits)...', 'info');
  
  try {
    // Try to use the same token again
    const response = await fetch(`${API_BASE}/secure-script?token=${TEST_CONFIG.testToken}`);
    
    if (response.ok) {
      log('Token reuse successful (within usage limits)', 'success');
      return true;
    } else {
      log('Token reuse failed (may have exceeded usage limits)', 'info');
      return true; // This is expected behavior
    }
  } catch (error) {
    log(`Token reuse test error: ${error.message}`, 'error');
    return false;
  }
}

async function testInvalidToken() {
  log('Testing invalid token (should fail)...', 'info');
  
  try {
    const response = await fetch(`${API_BASE}/secure-script?token=invalid_token_123`);
    
    if (!response.ok) {
      log('Invalid token correctly rejected', 'success');
      return true;
    } else {
      log('Invalid token was accepted (security issue!)', 'error');
      return false;
    }
  } catch (error) {
    log(`Invalid token test error: ${error.message}`, 'error');
    return false;
  }
}

async function testUnauthorizedAccess() {
  log('Testing unauthorized access (should fail)...', 'info');
  
  try {
    const response = await fetch(`${API_BASE}/secure-script?action=generate_loadstring&script_id=${TEST_CONFIG.testScriptId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-Key-Code': 'invalid_key',
        'X-HWID': 'invalid_hwid'
      }
    });

    if (!response.ok) {
      log('Unauthorized access correctly rejected', 'success');
      return true;
    } else {
      log('Unauthorized access was allowed (security issue!)', 'error');
      return false;
    }
  } catch (error) {
    log(`Unauthorized access test error: ${error.message}`, 'error');
    return false;
  }
}

// Main test runner
async function runTests() {
  log('Starting Secure Script System Tests', 'info');
  log('=====================================', 'info');
  
  const tests = [
    { name: 'Key Generation', fn: testKeyGeneration },
    { name: 'Key Validation', fn: testKeyValidation },
    { name: 'Script Listing', fn: testScriptListing },
    { name: 'Secure Loadstring Generation', fn: testSecureLoadstringGeneration },
    { name: 'Script Content Retrieval', fn: testScriptContentRetrieval },
    { name: 'Token Reuse', fn: testTokenReuse },
    { name: 'Invalid Token Rejection', fn: testInvalidToken },
    { name: 'Unauthorized Access Prevention', fn: testUnauthorizedAccess }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    log(`\nRunning test: ${test.name}`, 'info');
    
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      log(`Test ${test.name} threw an error: ${error.message}`, 'error');
      failed++;
    }
    
    // Small delay between tests
    await sleep(1000);
  }
  
  log('\n=====================================', 'info');
  log(`Test Results: ${passed} passed, ${failed} failed`, passed === tests.length ? 'success' : 'error');
  
  if (passed === tests.length) {
    log('🎉 All tests passed! Secure script system is working correctly.', 'success');
  } else {
    log('⚠️ Some tests failed. Please check the implementation.', 'error');
  }
  
  return passed === tests.length;
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(console.error);
}

export { runTests, TEST_CONFIG };
