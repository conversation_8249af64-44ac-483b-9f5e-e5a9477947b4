import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addMainLoaderScript() {
  console.log('Adding main loader script to database...');
  
  try {
    // Read the main loader script
    const scriptPath = path.join(process.cwd(), 'scripts', 'final-main-loader.lua');
    const content = fs.readFileSync(scriptPath, 'utf8');
    const contentHash = crypto.createHash('sha256').update(content).digest('hex');
    
    // Check if main loader already exists
    const { data: existing } = await supabase
      .from('scripts')
      .select('id')
      .eq('script_type', 'main_loader')
      .eq('is_main_script', true)
      .single();
      
    if (existing) {
      console.log('Main loader script already exists, updating...');
      
      const { error: updateError } = await supabase
        .from('scripts')
        .update({
          content: content,
          content_hash: contentHash,
          file_size_bytes: Buffer.byteLength(content, 'utf8'),
          updated_at: new Date().toISOString(),
          version: '1.1.0'
        })
        .eq('id', existing.id);
        
      if (updateError) {
        console.error('Failed to update main loader:', updateError);
        return;
      }
      
      console.log('✅ Main loader script updated successfully');
    } else {
      console.log('Creating new main loader script...');
      
      const { data, error } = await supabase
        .from('scripts')
        .insert({
          name: 'Project Madara - Main Loader',
          description: 'Centralized authentication and game script loader. Handles key validation once, then dynamically loads game-specific scripts based on PlaceId.',
          content: content,
          script_type: 'main_loader',
          is_main_script: true,
          storage_type: 'database',
          access_level: 'public', // Main loader needs to be publicly accessible
          requires_valid_key: false, // Authentication happens within the script
          category: 'utility',
          tags: ['Main Loader', 'Authentication', 'Dynamic Loading', 'PlaceId'],
          executor: 'All Executors',
          version: '1.0.0',
          file_size_bytes: Buffer.byteLength(content, 'utf8'),
          content_hash: contentHash,
          uploaded_by: 'system'
        })
        .select()
        .single();
        
      if (error) {
        console.error('Failed to add main loader:', error);
        return;
      }
      
      console.log('✅ Main loader script added successfully');
      console.log(`   Script ID: ${data.id}`);
      console.log(`   Size: ${Math.round(data.file_size_bytes / 1024)}KB`);
    }
    
    console.log('\n📋 Main Loader Usage:');
    console.log('Users should now use this single loadstring for all games:');
    console.log('loadstring(game:HttpGet("https://projectmadara.com/.netlify/functions/secure-script?action=get_main_loader"))()');

  } catch (error) {
    console.error('Error processing main loader:', error);
  }
}

// Run the script
addMainLoaderScript().catch(console.error);
