import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Sample large Lua script content (simulating 4,000-5,000 lines)
const generateLargeLuaScript = (scriptName) => {
  const baseScript = `-- ${scriptName} - Secure Project Madara Script
-- This script is protected and can only be accessed with a valid license key
-- Generated: ${new Date().toISOString()}

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Workspace = game:GetService("Workspace")

local LocalPlayer = Players.LocalPlayer
local PlayerGui = LocalPlayer:WaitForChild("PlayerGui")

-- Security check to prevent unauthorized access
local function securityCheck()
    local success, result = pcall(function()
        return game:GetService("HttpService"):GetAsync("https://projectmadara.com/.netlify/functions/validate-key")
    end)
    if not success then
        warn("Security validation failed")
        return false
    end
    return true
end

-- Main script functionality
local ${scriptName.replace(/\s+/g, '')}Module = {}

function ${scriptName.replace(/\s+/g, '')}Module.init()
    print("Initializing ${scriptName}...")
    
    -- Create main GUI
    local ScreenGui = Instance.new("ScreenGui")
    ScreenGui.Name = "${scriptName.replace(/\s+/g, '')}GUI"
    ScreenGui.Parent = PlayerGui
    ScreenGui.ResetOnSpawn = false
    
    local MainFrame = Instance.new("Frame")
    MainFrame.Name = "MainFrame"
    MainFrame.Size = UDim2.new(0, 400, 0, 300)
    MainFrame.Position = UDim2.new(0.5, -200, 0.5, -150)
    MainFrame.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
    MainFrame.BorderSizePixel = 0
    MainFrame.Parent = ScreenGui
    
    -- Add corner radius
    local Corner = Instance.new("UICorner")
    Corner.CornerRadius = UDim.new(0, 8)
    Corner.Parent = MainFrame
    
    -- Title bar
    local TitleBar = Instance.new("Frame")
    TitleBar.Name = "TitleBar"
    TitleBar.Size = UDim2.new(1, 0, 0, 30)
    TitleBar.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    TitleBar.BorderSizePixel = 0
    TitleBar.Parent = MainFrame
    
    local TitleCorner = Instance.new("UICorner")
    TitleCorner.CornerRadius = UDim.new(0, 8)
    TitleCorner.Parent = TitleBar
    
    local Title = Instance.new("TextLabel")
    Title.Name = "Title"
    Title.Size = UDim2.new(1, -60, 1, 0)
    Title.Position = UDim2.new(0, 10, 0, 0)
    Title.BackgroundTransparency = 1
    Title.Text = "${scriptName} - Project Madara"
    Title.TextColor3 = Color3.fromRGB(255, 255, 255)
    Title.TextScaled = true
    Title.Font = Enum.Font.GothamBold
    Title.Parent = TitleBar
    
    -- Close button
    local CloseButton = Instance.new("TextButton")
    CloseButton.Name = "CloseButton"
    CloseButton.Size = UDim2.new(0, 25, 0, 25)
    CloseButton.Position = UDim2.new(1, -30, 0, 2.5)
    CloseButton.BackgroundColor3 = Color3.fromRGB(255, 100, 100)
    CloseButton.Text = "X"
    CloseButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    CloseButton.TextScaled = true
    CloseButton.Font = Enum.Font.GothamBold
    CloseButton.Parent = TitleBar
    
    local CloseCorner = Instance.new("UICorner")
    CloseCorner.CornerRadius = UDim.new(0, 4)
    CloseCorner.Parent = CloseButton
    
    CloseButton.MouseButton1Click:Connect(function()
        ScreenGui:Destroy()
    end)
    
    -- Content area
    local ContentFrame = Instance.new("ScrollingFrame")
    ContentFrame.Name = "ContentFrame"
    ContentFrame.Size = UDim2.new(1, -20, 1, -50)
    ContentFrame.Position = UDim2.new(0, 10, 0, 40)
    ContentFrame.BackgroundTransparency = 1
    ContentFrame.ScrollBarThickness = 6
    ContentFrame.Parent = MainFrame
    
    -- Add features based on script type
    ${scriptName.includes('ESP') ? `
    -- ESP Features
    local function createESP()
        for _, player in pairs(Players:GetPlayers()) do
            if player ~= LocalPlayer and player.Character then
                local character = player.Character
                local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
                if humanoidRootPart then
                    local billboard = Instance.new("BillboardGui")
                    billboard.Size = UDim2.new(0, 100, 0, 50)
                    billboard.StudsOffset = Vector3.new(0, 3, 0)
                    billboard.Parent = humanoidRootPart
                    
                    local nameLabel = Instance.new("TextLabel")
                    nameLabel.Size = UDim2.new(1, 0, 0.5, 0)
                    nameLabel.BackgroundTransparency = 1
                    nameLabel.Text = player.Name
                    nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
                    nameLabel.TextScaled = true
                    nameLabel.Font = Enum.Font.Gotham
                    nameLabel.Parent = billboard
                    
                    local healthLabel = Instance.new("TextLabel")
                    healthLabel.Size = UDim2.new(1, 0, 0.5, 0)
                    healthLabel.Position = UDim2.new(0, 0, 0.5, 0)
                    healthLabel.BackgroundTransparency = 1
                    healthLabel.Text = "Health: " .. math.floor(character.Humanoid.Health)
                    healthLabel.TextColor3 = Color3.fromRGB(0, 255, 0)
                    healthLabel.TextScaled = true
                    healthLabel.Font = Enum.Font.Gotham
                    healthLabel.Parent = billboard
                end
            end
        end
    end
    
    createESP()
    Players.PlayerAdded:Connect(createESP)
    ` : ''}
    
    ${scriptName.includes('Speed') ? `
    -- Speed Hack Features
    local speedEnabled = false
    local originalWalkSpeed = LocalPlayer.Character and LocalPlayer.Character.Humanoid.WalkSpeed or 16
    
    local function toggleSpeed()
        if LocalPlayer.Character and LocalPlayer.Character:FindFirstChild("Humanoid") then
            speedEnabled = not speedEnabled
            if speedEnabled then
                LocalPlayer.Character.Humanoid.WalkSpeed = 100
            else
                LocalPlayer.Character.Humanoid.WalkSpeed = originalWalkSpeed
            end
        end
    end
    ` : ''}
    
    ${scriptName.includes('Teleport') ? `
    -- Teleport Features
    local function teleportToPlayer(targetPlayer)
        if LocalPlayer.Character and LocalPlayer.Character:FindFirstChild("HumanoidRootPart") then
            if targetPlayer.Character and targetPlayer.Character:FindFirstChild("HumanoidRootPart") then
                LocalPlayer.Character.HumanoidRootPart.CFrame = targetPlayer.Character.HumanoidRootPart.CFrame
            end
        end
    end
    ` : ''}
    
    -- Add buttons for features
    local buttonCount = 0
    local function createButton(text, callback)
        local button = Instance.new("TextButton")
        button.Name = text .. "Button"
        button.Size = UDim2.new(1, -20, 0, 40)
        button.Position = UDim2.new(0, 10, 0, buttonCount * 50)
        button.BackgroundColor3 = Color3.fromRGB(70, 70, 70)
        button.Text = text
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.Gotham
        button.Parent = ContentFrame
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 6)
        buttonCorner.Parent = button
        
        button.MouseButton1Click:Connect(callback)
        buttonCount = buttonCount + 1
        
        ContentFrame.CanvasSize = UDim2.new(0, 0, 0, buttonCount * 50 + 10)
    end
    
    -- Create feature buttons
    createButton("Toggle Feature 1", function() print("Feature 1 toggled") end)
    createButton("Toggle Feature 2", function() print("Feature 2 toggled") end)
    createButton("Toggle Feature 3", function() print("Feature 3 toggled") end)
    
    -- Make GUI draggable
    local dragging = false
    local dragStart = nil
    local startPos = nil
    
    TitleBar.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            dragging = true
            dragStart = input.Position
            startPos = MainFrame.Position
        end
    end)
    
    TitleBar.InputChanged:Connect(function(input)
        if dragging and input.UserInputType == Enum.UserInputType.MouseMovement then
            local delta = input.Position - dragStart
            MainFrame.Position = UDim2.new(startPos.X.Scale, startPos.X.Offset + delta.X, startPos.Y.Scale, startPos.Y.Offset + delta.Y)
        end
    end)
    
    TitleBar.InputEnded:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            dragging = false
        end
    end)
    
    print("${scriptName} loaded successfully!")
end

-- Initialize the script
if securityCheck() then
    ${scriptName.replace(/\s+/g, '')}Module.init()
else
    warn("Security check failed - script access denied")
end

return ${scriptName.replace(/\s+/g, '')}Module`;

  // Repeat the script content to simulate a large file
  const repetitions = Math.floor(4000 / baseScript.split('\n').length);
  let largeScript = baseScript;
  
  for (let i = 0; i < repetitions; i++) {
    largeScript += `\n\n-- Section ${i + 1} - Additional functionality\n`;
    largeScript += baseScript.replace(/function /g, `function section${i}_`);
  }
  
  return largeScript;
};

const sampleScripts = [
  {
    name: "Universal ESP",
    description: "Advanced ESP system that works across all games. Shows player names, health, distance, and more with customizable colors and settings.",
    category: "utility",
    tags: ["ESP", "Universal", "Player Detection", "Wallhack"],
    executor: "Synapse X, ScriptWare, KRNL",
    version: "2.1.0",
    storage_type: "secure_file",
    access_level: "authenticated",
    requires_valid_key: true
  },
  {
    name: "Speed Hack Pro",
    description: "Professional speed modification tool with anti-detection features. Includes walk speed, fly speed, and jump power modifications.",
    category: "game",
    tags: ["Speed", "Movement", "Anti-Detection", "Fly"],
    executor: "Synapse X, ScriptWare",
    version: "1.8.5",
    storage_type: "secure_file",
    access_level: "authenticated",
    requires_valid_key: true
  },
  {
    name: "Teleport Manager",
    description: "Advanced teleportation system with waypoints, player teleporting, and coordinate-based movement. Includes anti-kick protection.",
    category: "utility",
    tags: ["Teleport", "Waypoints", "Coordinates", "Anti-Kick"],
    executor: "Synapse X, ScriptWare, KRNL, Fluxus",
    version: "3.0.2",
    storage_type: "secure_file",
    access_level: "authenticated",
    requires_valid_key: true
  },
  {
    name: "Auto Farm Ultimate",
    description: "Comprehensive auto-farming solution that works across multiple games. Features intelligent pathfinding and customizable farming strategies.",
    category: "game",
    tags: ["Auto Farm", "Multi-Game", "Pathfinding", "Automation"],
    executor: "Synapse X, ScriptWare",
    version: "4.2.1",
    storage_type: "secure_file",
    access_level: "premium",
    requires_valid_key: true
  },
  {
    name: "Infinite Yield Commands",
    description: "The most comprehensive admin command script available. Over 300 commands for game manipulation and administration.",
    category: "utility",
    tags: ["Admin", "Commands", "Game Control", "Infinite Yield"],
    executor: "All Executors",
    version: "5.1.3",
    storage_type: "secure_file",
    access_level: "authenticated",
    requires_valid_key: true
  }
];

async function addSampleScripts() {
  console.log('Adding sample scripts to database...');
  
  for (const scriptData of sampleScripts) {
    try {
      // Generate large script content
      const content = generateLargeLuaScript(scriptData.name);
      const contentHash = crypto.createHash('sha256').update(content).digest('hex');
      
      // Insert script into database
      const { data, error } = await supabase
        .from('scripts')
        .insert({
          ...scriptData,
          content: content,
          file_size_bytes: Buffer.byteLength(content, 'utf8'),
          content_hash: contentHash,
          uploaded_by: 'system',
          is_active: true
        })
        .select()
        .single();
        
      if (error) {
        console.error(`Failed to add script "${scriptData.name}":`, error);
      } else {
        console.log(`✅ Added script: ${scriptData.name} (${Math.round(data.file_size_bytes / 1024)}KB)`);
      }
      
    } catch (error) {
      console.error(`Error processing script "${scriptData.name}":`, error);
    }
  }
  
  console.log('Sample scripts added successfully!');
}

// Run the script
addSampleScripts().catch(console.error);
