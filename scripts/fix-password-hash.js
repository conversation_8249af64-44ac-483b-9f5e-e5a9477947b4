#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to manually hash and update a specific admin user's password
 */

import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables: SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function hashPassword(password) {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
}

async function fixPasswordHash() {
  try {
    console.log('🔍 Checking admin users and their password formats...');
    
    // Get all admin users
    const { data: adminUsers, error: fetchError } = await supabase
      .from('admin_users')
      .select('id, username, password_hash');

    if (fetchError) {
      throw new Error(`Failed to fetch admin users: ${fetchError.message}`);
    }

    if (!adminUsers || adminUsers.length === 0) {
      console.log('❌ No admin users found.');
      return;
    }

    console.log(`📋 Found ${adminUsers.length} admin users:`);
    
    for (const user of adminUsers) {
      const isHashed = user.password_hash.startsWith('$2');
      console.log(`  - ${user.username}: ${isHashed ? '✅ Already hashed' : '❌ Plain text'}`);
      
      if (!isHashed) {
        console.log(`\n🔐 Hashing password for ${user.username}...`);
        
        // Hash the plain text password
        const hashedPassword = await hashPassword(user.password_hash);
        
        // Update the user with the hashed password
        const { error: updateError } = await supabase
          .from('admin_users')
          .update({ password_hash: hashedPassword })
          .eq('id', user.id);

        if (updateError) {
          console.error(`❌ Failed to update password for ${user.username}: ${updateError.message}`);
          continue;
        }

        console.log(`✅ Successfully hashed password for ${user.username}`);
        console.log(`   New hash: ${hashedPassword.substring(0, 20)}...`);
      }
    }

    console.log('\n🎉 Password hash check/fix completed!');
    
    // Test authentication with the main user
    console.log('\n🧪 Testing authentication...');
    const testUsername = 'Sabin07';
    const testPassword = 'IMightBeCoolTho';
    
    const { data: testUser, error: testError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('username', testUsername)
      .single();

    if (testError || !testUser) {
      console.log(`❌ Could not find user ${testUsername} for testing`);
      return;
    }

    const isValid = await bcrypt.compare(testPassword, testUser.password_hash);
    console.log(`🔑 Authentication test for ${testUsername}: ${isValid ? '✅ SUCCESS' : '❌ FAILED'}`);
    
    if (!isValid) {
      console.log('⚠️  Authentication failed. You may need to manually set your password.');
      console.log('   Current hash in database:', testUser.password_hash.substring(0, 30) + '...');
      
      // Offer to reset the password
      console.log('\n🔧 Resetting password to "IMightBeCoolTho"...');
      const newHash = await hashPassword(testPassword);
      
      const { error: resetError } = await supabase
        .from('admin_users')
        .update({ password_hash: newHash })
        .eq('username', testUsername);

      if (resetError) {
        console.error(`❌ Failed to reset password: ${resetError.message}`);
      } else {
        console.log('✅ Password reset successfully!');
        console.log('   New hash:', newHash.substring(0, 30) + '...');
      }
    }
    
  } catch (error) {
    console.error('💥 Script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
console.log('🚀 Starting password hash fix script...');
fixPasswordHash()
  .then(() => {
    console.log('✨ Script completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error.message);
    process.exit(1);
  });
