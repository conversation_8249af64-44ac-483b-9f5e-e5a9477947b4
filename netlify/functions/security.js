import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import { processRequest, anonymizeIP, formatHashForDisplay } from './utils/ipAnonymization.cjs';

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);
export const handler = async (event, context) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, x-admin-username, x-admin-password, x-session-token',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Credentials': 'true'
  };
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }
  const { action } = event.queryStringParameters || {};
  if (action === 'validate-session') {
    return await handleValidateSession(event, headers);
  }
  if (action === 'login') {
    return await handleLogin(event, headers);
  }
  const sessionToken = event.headers['x-session-token'];
  if (sessionToken) {
    return await handleSessionBasedAction(event, headers, action, sessionToken);
  }
  const adminUsername = event.headers['x-admin-username'];
  const adminPassword = event.headers['x-admin-password'];
  if (!adminUsername || !adminPassword) {
    return {
      statusCode: 401,
      headers,
      body: JSON.stringify({ error: 'Admin credentials required' })
    };
  }
  const { data: adminUser, error: adminError } = await supabase
    .from('admin_users')
    .select('*')
    .eq('username', adminUsername)
    .single();
  if (adminError || !adminUser) {
    return {
      statusCode: 401,
      headers,
      body: JSON.stringify({ error: 'Invalid admin credentials' })
    };
  }
  const isPasswordValid = await bcrypt.compare(adminPassword, adminUser.password_hash);
  if (!isPasswordValid) {
    return {
      statusCode: 401,
      headers,
      body: JSON.stringify({ error: 'Invalid admin credentials' })
    };
  }
  const adminRole = adminUser.role;
  try {
    switch (action) {
      case 'ip-bans':
        return await handleIpBans(event, headers, adminRole);
      case 'ban-ip':
        if (adminRole !== 'owner') {
          return { statusCode: 403, headers, body: JSON.stringify({ error: 'Owner role required' }) };
        }
        return await handleBanIp(event, headers, adminUsername);
      case 'unban-ip':
        if (adminRole !== 'owner') {
          return { statusCode: 403, headers, body: JSON.stringify({ error: 'Owner role required' }) };
        }
        return await handleUnbanIp(event, headers, adminUsername);
      case 'events':
        return await handleSecurityEvents(event, headers);
      case 'settings':
        return await handleSecuritySettings(event, headers);
      case 'update-setting':
        if (adminRole !== 'owner') {
          return { statusCode: 403, headers, body: JSON.stringify({ error: 'Owner role required' }) };
        }
        return await handleUpdateSetting(event, headers, adminUsername);
      case 'admin-actions':
        if (adminRole !== 'owner') {
          return { statusCode: 403, headers, body: JSON.stringify({ error: 'Owner role required' }) };
        }
        return await handleAdminActions(event, headers);
      case 'script-requests':
        return await handleScriptRequests(event, headers);
      case 'update-script-request':
        return await handleUpdateScriptRequest(event, headers, adminUsername);
      case 'delete-script-request':
        return await handleDeleteScriptRequest(event, headers, adminUsername);
      case 'scripts':
        return await handleScripts(event, headers);
      case 'create-script':
        return await handleCreateScript(event, headers, adminUsername);
      case 'update-script':
        return await handleUpdateScript(event, headers, adminUsername);
      case 'delete-script':
        return await handleDeleteScript(event, headers, adminUsername);
      case 'update-log':
        return await handleUpdateLog(event, headers, adminUsername);
      case 'keys':
        return await handleKeys(event, headers);
      case 'create-key':
        return await handleCreateKey(event, headers, adminUsername);
      case 'update-key':
        return await handleUpdateKey(event, headers, adminUsername);
      case 'delete-key':
        return await handleDeleteKey(event, headers, adminUsername);
      default:
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ error: 'Invalid action' })
        };
    }
  } catch (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
async function handleIpBans(event, headers, adminRole) {
  const { data, error } = await supabase
    .from('ip_bans')
    .select('*')
    .order('banned_at', { ascending: false });
  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: error.message })
    };
  }
  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({ data, role: adminRole })
  };
}
async function handleBanIp(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  const { ip_address, reason, duration_hours, notes } = body;
  if (!ip_address || !reason) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ error: 'IP address and reason are required' })
    };
  }

  // Anonymize the IP address for storage
  const ipHash = anonymizeIP(ip_address);

  const expiresAt = duration_hours > 0
    ? new Date(Date.now() + (duration_hours * 60 * 60 * 1000)).toISOString()
    : null;
  const { data, error } = await supabase
    .from('ip_bans')
    .upsert({
      ip_address, // Keep during transition
      ip_hash: ipHash, // New anonymized field
      reason,
      banned_by: adminUsername,
      expires_at: expiresAt,
      notes,
      is_active: true
    })
    .select();
  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: error.message })
    };
  }
  // Get admin's IP and anonymize it
  const adminRawIP = event.headers['client-ip'] || event.headers['x-forwarded-for'] || 'unknown';
  const adminIPHash = anonymizeIP(adminRawIP);

  await supabase
    .from('admin_security_actions')
    .insert({
      admin_username: adminUsername,
      action_type: 'ip_ban',
      target_identifier: formatHashForDisplay(ipHash), // Store anonymized target
      details: { reason, duration_hours, notes, original_ip_hash: ipHash },
      ip_address: adminRawIP, // Keep during transition
      ip_hash: adminIPHash // New anonymized field
    });
  return {
    statusCode: 200,
    headers,
    body: JSON.stringify(data[0])
  };
}
async function handleUnbanIp(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  const { ip_address } = body;
  if (!ip_address) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ error: 'IP address is required' })
    };
  }
  // Anonymize the IP address for lookup
  const ipHash = anonymizeIP(ip_address);

  const { data, error } = await supabase
    .from('ip_bans')
    .update({ is_active: false })
    .eq('ip_hash', ipHash)
    .select();
  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: error.message })
    };
  }
  // Get admin's IP and anonymize it
  const adminRawIP = event.headers['client-ip'] || event.headers['x-forwarded-for'] || 'unknown';
  const adminIPHash = anonymizeIP(adminRawIP);

  await supabase
    .from('admin_security_actions')
    .insert({
      admin_username: adminUsername,
      action_type: 'ip_unban',
      target_identifier: formatHashForDisplay(ipHash), // Store anonymized target
      details: { original_ip_hash: ipHash },
      ip_address: adminRawIP, // Keep during transition
      ip_hash: adminIPHash // New anonymized field
    });
  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({ message: 'IP unbanned successfully' })
  };
}
async function handleSecurityEvents(event, headers) {
  const { data, error } = await supabase
    .from('security_events')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(100);
  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: error.message })
    };
  }
  return {
    statusCode: 200,
    headers,
    body: JSON.stringify(data)
  };
}
async function handleSecuritySettings(event, headers) {
  const { data, error } = await supabase
    .from('security_settings')
    .select('*')
    .order('setting_key');
  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: error.message })
    };
  }
  return {
    statusCode: 200,
    headers,
    body: JSON.stringify(data)
  };
}
async function handleUpdateSetting(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  const { setting_key, setting_value } = body;
  if (!setting_key || setting_value === undefined) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ error: 'Setting key and value are required' })
    };
  }
  const { data, error } = await supabase
    .from('security_settings')
    .update({ 
      setting_value: setting_value.toString(),
      updated_by: adminUsername,
      updated_at: new Date().toISOString()
    })
    .eq('setting_key', setting_key)
    .select();
  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: error.message })
    };
  }
  await supabase
    .from('admin_security_actions')
    .insert({
      admin_username: adminUsername,
      action_type: 'security_setting_change',
      target_identifier: setting_key,
      details: { old_value: data[0]?.setting_value, new_value: setting_value },
      ip_address: event.headers['client-ip'] || event.headers['x-forwarded-for'] || 'unknown'
    });
  return {
    statusCode: 200,
    headers,
    body: JSON.stringify(data[0])
  };
}
async function handleAdminActions(event, headers) {
  const { data, error } = await supabase
    .from('admin_security_actions')
    .select('*')
    .order('performed_at', { ascending: false })
    .limit(100);
  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: error.message })
    };
  }
  return {
    statusCode: 200,
    headers,
    body: JSON.stringify(data)
  };
}
async function handleScriptRequests(event, headers) {
  const { data, error } = await supabase
    .from('script_requests')
    .select('*')
    .order('requested_at', { ascending: false });
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify(data) };
}
async function handleUpdateScriptRequest(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  const { id, status, admin_notes } = body;
  if (!id || !status) {
    return { statusCode: 400, headers, body: JSON.stringify({ error: 'ID and status required' }) };
  }
  const { data, error } = await supabase
    .from('script_requests')
    .update({ status, admin_notes, reviewed_by: adminUsername, reviewed_at: new Date().toISOString() })
    .eq('id', id)
    .select();
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify(data[0]) };
}
async function handleDeleteScriptRequest(event, headers, adminUsername) {
  const id = event.queryStringParameters?.id;
  if (!id) {
    return { statusCode: 400, headers, body: JSON.stringify({ error: 'ID required' }) };
  }
  const { error } = await supabase
    .from('script_requests')
    .delete()
    .eq('id', id);
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify({ message: 'Deleted' }) };
}
async function handleScripts(event, headers) {
  const { data, error } = await supabase
    .from('scripts')
    .select('*')
    .order('created_at', { ascending: false });
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify(data) };
}
async function handleCreateScript(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  if (!body.name) {
    return { statusCode: 400, headers, body: JSON.stringify({ error: 'Script name required' }) };
  }
  body.uploaded_by = adminUsername;
  const { data, error } = await supabase
    .from('scripts')
    .insert(body)
    .select();
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify(data[0]) };
}
async function handleUpdateScript(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  if (!body.id) {
    return { statusCode: 400, headers, body: JSON.stringify({ error: 'Script ID required' }) };
  }
  body.updated_by = adminUsername;
  body.updated_at = new Date().toISOString();
  const { data, error } = await supabase
    .from('scripts')
    .update(body)
    .eq('id', body.id)
    .select();
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify(data[0]) };
}
async function handleDeleteScript(event, headers, adminUsername) {
  const id = event.queryStringParameters?.id;
  if (!id) {
    return { statusCode: 400, headers, body: JSON.stringify({ error: 'ID required' }) };
  }
  const { error } = await supabase
    .from('scripts')
    .delete()
    .eq('id', id);
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify({ message: 'Deleted' }) };
}
async function handleUpdateLog(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  if (!body.script_id || !body.version || !body.changes) {
    return { statusCode: 400, headers, body: JSON.stringify({ error: 'script_id, version, and changes required' }) };
  }
  const { data, error } = await supabase
    .from('script_update_logs')
    .insert({
      script_id: body.script_id,
      version: body.version,
      changes: body.changes,
      updated_by: adminUsername,
      updated_at: new Date().toISOString()
    })
    .select();
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify(data[0]) };
}
async function handleKeys(event, headers) {
  const params = event.queryStringParameters || {};
  let query = supabase
    .from('license_keys')
    .select('*')
    .order('created_at', { ascending: false });
  if (params.status && params.status !== 'all') {
    if (params.status === 'active') query = query.eq('is_active', true).eq('is_revoked', false);
    if (params.status === 'expired') query = query.lt('expires_at', new Date().toISOString());
    if (params.status === 'revoked') query = query.eq('is_revoked', true);
  }
  if (params.search) {
    query = query.ilike('key_code', `%${params.search}%`);
  }
  const page = parseInt(params.page) || 1;
  const limit = parseInt(params.limit) || 50;
  const from = (page - 1) * limit;
  const to = from + limit - 1;
  query = query.range(from, to);
  const { data, error, count } = await query;
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify({ keys: data, pagination: { page, limit, count: data.length, pages: 1 } }) };
}
async function handleCreateKey(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  const expiresInHours = parseInt(body.expiresInHours) || 24;
  const expiresAt = new Date(Date.now() + expiresInHours * 60 * 60 * 1000).toISOString();
  const { data: keyCode, error: keyError } = await supabase.rpc('generate_key_code');
  if (keyError) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to generate key' })
    };
  }
  const { data, error } = await supabase
    .from('license_keys')
    .insert({
      key_code: keyCode,
      is_active: true,
      is_revoked: false,
      created_by_admin: true,
      created_at: new Date().toISOString(),
      expires_at: expiresAt
    })
    .select();
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify(data[0]) };
}
async function handleUpdateKey(event, headers, adminUsername) {
  const body = JSON.parse(event.body);
  if (!body.id) {
    return { statusCode: 400, headers, body: JSON.stringify({ error: 'Key ID required' }) };
  }
  const updateFields = { ...body };
  delete updateFields.id;
  const { data, error } = await supabase
    .from('license_keys')
    .update(updateFields)
    .eq('id', body.id)
    .select();
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify(data[0]) };
}
async function handleSessionBasedAction(event, headers, action, sessionToken) {
  try {
    let sessionData;
    try {
      sessionData = JSON.parse(Buffer.from(sessionToken, 'base64').toString());
    } catch (parseError) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid token format' })
      };
    }
    if (!sessionData.username || !sessionData.timestamp) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid token data' })
      };
    }
    const sessionAge = Date.now() - sessionData.timestamp;
    if (sessionAge > 24 * 60 * 60 * 1000) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Session expired' })
      };
    }
    const { data: adminUser, error: adminError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('username', sessionData.username)
      .eq('is_active', true)
      .single();
    if (adminError || !adminUser) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'User not found or inactive' })
      };
    }
    const adminRole = adminUser.role;
    const adminUsername = adminUser.username;
    switch (action) {
      case 'ip-bans':
        return await handleIpBans(event, headers, adminRole);
      case 'ban-ip':
        if (adminRole !== 'owner') {
          return { statusCode: 403, headers, body: JSON.stringify({ error: 'Owner role required' }) };
        }
        return await handleBanIp(event, headers, adminUsername);
      case 'unban-ip':
        if (adminRole !== 'owner') {
          return { statusCode: 403, headers, body: JSON.stringify({ error: 'Owner role required' }) };
        }
        return await handleUnbanIp(event, headers, adminUsername);
      case 'events':
        return await handleSecurityEvents(event, headers);
      case 'settings':
        return await handleSecuritySettings(event, headers);
      case 'update-setting':
        if (adminRole !== 'owner') {
          return { statusCode: 403, headers, body: JSON.stringify({ error: 'Owner role required' }) };
        }
        return await handleUpdateSetting(event, headers, adminUsername);
      case 'admin-actions':
        if (adminRole !== 'owner') {
          return { statusCode: 403, headers, body: JSON.stringify({ error: 'Owner role required' }) };
        }
        return await handleAdminActions(event, headers);
      case 'script-requests':
        return await handleScriptRequests(event, headers);
      case 'update-script-request':
        return await handleUpdateScriptRequest(event, headers, adminUsername);
      case 'delete-script-request':
        return await handleDeleteScriptRequest(event, headers, adminUsername);
      case 'scripts':
        return await handleScripts(event, headers);
      case 'create-script':
        return await handleCreateScript(event, headers, adminUsername);
      case 'update-script':
        return await handleUpdateScript(event, headers, adminUsername);
      case 'delete-script':
        return await handleDeleteScript(event, headers, adminUsername);
      case 'update-log':
        return await handleUpdateLog(event, headers, adminUsername);
      case 'keys':
        return await handleKeys(event, headers);
      case 'create-key':
        return await handleCreateKey(event, headers, adminUsername);
      case 'update-key':
        return await handleUpdateKey(event, headers, adminUsername);
      case 'delete-key':
        return await handleDeleteKey(event, headers, adminUsername);
      default:
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ error: `Unknown action: ${action}` })
        };
    }
  } catch (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
}
async function handleValidateSession(event, headers) {
  const sessionToken = event.headers['x-session-token'];
  if (!sessionToken) {
    return {
      statusCode: 401,
      headers,
      body: JSON.stringify({ error: 'No session token provided' })
    };
  }
  try {
    let sessionData;
    try {
      sessionData = JSON.parse(Buffer.from(sessionToken, 'base64').toString());
    } catch (parseError) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid token format' })
      };
    }
    if (!sessionData.username || !sessionData.timestamp) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid token data' })
      };
    }
    const sessionAge = Date.now() - sessionData.timestamp;
    if (sessionAge > 24 * 60 * 60 * 1000) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Session expired' })
      };
    }
    const { data: adminUser, error: adminError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('username', sessionData.username)
      .eq('is_active', true)
      .single();
    if (adminError || !adminUser) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'User not found or inactive' })
      };
    }
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        username: adminUser.username,
        role: adminUser.role,
        permissions: adminUser.permissions || {}
      })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
}
async function handleLogin(event, headers) {
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }
  try {
    const { username, password } = JSON.parse(event.body);
    if (!username || !password) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Username and password required' })
      };
    }
    const { data: adminUser, error: adminError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('username', username)
      .eq('is_active', true)
      .single();
    if (adminError || !adminUser) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid credentials' })
      };
    }
    const isPasswordValid = await bcrypt.compare(password, adminUser.password_hash);
    if (!isPasswordValid) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid credentials' })
      };
    }
    await supabase
      .from('admin_users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', adminUser.id);
    const sessionData = {
      username: adminUser.username,
      role: adminUser.role,
      timestamp: Date.now()
    };
    const sessionToken = Buffer.from(JSON.stringify(sessionData)).toString('base64');
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        username: adminUser.username,
        role: adminUser.role,
        permissions: adminUser.permissions || {},
        sessionToken: sessionToken
      })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
}
async function handleDeleteKey(event, headers, adminUsername) {
  const id = event.queryStringParameters?.id;
  if (!id) {
    return { statusCode: 400, headers, body: JSON.stringify({ error: 'ID required' }) };
  }
  const { error } = await supabase
    .from('license_keys')
    .delete()
    .eq('id', id);
  if (error) {
    return { statusCode: 500, headers, body: JSON.stringify({ error: error.message }) };
  }
  return { statusCode: 200, headers, body: JSON.stringify({ message: 'Deleted' }) };
} 