import { createClient } from '@supabase/supabase-js';
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);
export const handler = async (event) => {
  try {
    // Check if lootlabs_analytics table exists and get column info
    const { data: columnInfo, error: columnError } = await supabase.rpc('get_column_info', {
      table_name: 'lootlabs_analytics',
      column_name: 'session_id'
    });
    if (columnError) {
      // Try to query the table to see if it exists and what type session_id is
      try {
        const { data: testData, error: testError } = await supabase
          .from('lootlabs_analytics')
          .select('session_id')
          .limit(1);
        if (testError) {
          if (testError.message.includes('relation "lootlabs_analytics" does not exist')) {
            return {
              statusCode: 200,
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                success: true,
                message: 'lootlabs_analytics table does not exist - will be created automatically on first use',
                action: 'table_missing'
              })
            };
          } else {
            throw testError;
          }
        }
        // Table exists, try to insert a test record to check compatibility
        const testSessionId = `test-${Date.now()}`;
        const { error: insertError } = await supabase
          .from('lootlabs_analytics')
          .insert({
            event_type: 'schema_test',
            session_id: testSessionId,
            event_data: { test: true }
          });
        if (insertError) {
          return {
            statusCode: 500,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              success: false,
              message: 'Database schema issue detected',
              error: insertError.message,
              action: 'manual_fix_required'
            })
          };
        }
        // Clean up test record
        await supabase
          .from('lootlabs_analytics')
          .delete()
          .eq('event_type', 'schema_test')
          .eq('session_id', testSessionId);
        return {
          statusCode: 200,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            success: true,
            message: 'Database schema is compatible - session_id accepts TEXT values',
            action: 'schema_compatible'
          })
        };
      } catch (directError) {
        return {
          statusCode: 500,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            success: false,
            message: 'Failed to check database schema',
            error: directError.message
          })
        };
      }
    }
    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        success: true,
        message: 'Database schema check completed',
        columnInfo
      })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        success: false,
        message: 'Database schema fix failed',
        error: error.message
      })
    };
  }
};
