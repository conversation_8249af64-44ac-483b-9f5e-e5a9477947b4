import { createClient } from '@supabase/supabase-js';
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};
import { verifyLootlabsCompletion } from './utils/serverSideLootlabsVerification.js';
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
let supabase;
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-requested-with',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Credentials': 'true'
};
export const handler = async (event) => {
  try {
    if (!supabase) {
      try {
        supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
      } catch (e) {
        return {
          statusCode: 500,
          headers: corsHeaders,
          body: JSON.stringify({ error: 'Database connection failed', details: e.message })
        };
      }
    }
    if (event.httpMethod === 'OPTIONS') {
      return { statusCode: 200, headers: corsHeaders };
    }
    if (event.httpMethod === 'GET') {
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          message: 'step-session function is working',
          timestamp: new Date().toISOString(),
          methods: ['POST'],
          actions: ['start', 'complete', 'status', 'mark_compromised', 'cleanup_session']
        })
      };
    }
    if (event.httpMethod === 'POST') {
      let body;
      try {
        body = JSON.parse(event.body || '{}');
      } catch (parseError) {
        console.log('JSON parse error:', parseError);
        return {
          statusCode: 400,
          headers: corsHeaders,
          body: JSON.stringify({ error: 'Invalid JSON in request body', details: parseError.message })
        };
      }

      console.log('step-session POST request:', { action: body.action, sessionId: body.sessionId, step: body.step });

      if (body.action === 'start' || body.action === 'create') {
        const rawIpAddress = event.headers['x-forwarded-for'] || '';
        const ipAddress = rawIpAddress.split(',')[0].trim();
        const userAgent = event.headers['user-agent'] || '';
        const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(); // 24 hour expiry
        const { data: existingSession, error: fetchError } = await supabase
          .from('key_sessions')
          .select('session_id, expires_at, step1, step2')
          .eq('ip_address', ipAddress)
          .gt('expires_at', new Date().toISOString())
          .order('created_at', { ascending: false })
          .limit(1)
          .single();
        if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 means no rows found
          return {
            statusCode: 500,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Failed to check for existing session', details: fetchError.message })
          };
        }
        let sessionId;
        if (existingSession) {
          sessionId = existingSession.session_id;
          const { error: updateError } = await supabase.from('key_sessions')
            .update({ expires_at: expiresAt, user_agent: userAgent })
            .eq('session_id', sessionId);
          if (updateError) {
            return {
              statusCode: 500,
              headers: corsHeaders,
              body: JSON.stringify({ error: 'Failed to extend session expiry', details: updateError.message })
            };
          }
        } else {
          sessionId = generateUUID();
          const { data: defaultCampaign, error: campaignError } = await supabase
            .from('lootlabs_campaigns')
            .select('id')
            .eq('is_active', true)
            .limit(1)
            .single();
          if (campaignError) {
          }
          let sessionData = {
            session_id: sessionId,
            step1: false,
            step2: false,
            expires_at: expiresAt,
            ip_address: ipAddress,
            user_agent: userAgent
          };
          if (defaultCampaign?.id) {
            sessionData.campaign_id = defaultCampaign.id;
          }
          const { data: insertData, error: insertError } = await supabase.from('key_sessions').insert(sessionData).select();
          if (insertError) {
            return {
              statusCode: 500,
              headers: corsHeaders,
              body: JSON.stringify({ error: 'Failed to create new session', details: insertError.message })
            };
          }
          const { data: verifyData, error: verifyError } = await supabase
            .from('key_sessions')
            .select('session_id, step1, step2, campaign_id')
            .eq('session_id', sessionId)
            .single();
          if (verifyError || !verifyData) {
            return {
              statusCode: 500,
              headers: corsHeaders,
              body: JSON.stringify({ error: 'Session created but verification failed', details: verifyError?.message })
            };
          }
        }
        return {
          statusCode: 200,
          headers: {
            ...corsHeaders,
            'Set-Cookie': `keysession=${sessionId}; HttpOnly; Path=/; Max-Age=86400; SameSite=Strict`, // 24 hour cookie
          },
          body: JSON.stringify({ sessionId, step1: existingSession?.step1 || false, step2: existingSession?.step2 || false })
        };
      }
      if (body.action === 'status') {
        const rawIpAddress = event.headers['x-forwarded-for'] || '';
        const ipAddress = rawIpAddress.split(',')[0].trim();
        const { data: sessionData, error: sessionError } = await supabase
          .from('key_sessions')
          .select('session_id, step1, step2, expires_at')
          .eq('ip_address', ipAddress)
          .gt('expires_at', new Date().toISOString())
          .order('created_at', { ascending: false })
          .limit(1)
          .single();
        if (sessionError && sessionError.code !== 'PGRST116') {
          return {
            statusCode: 500,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Failed to fetch session status' })
          };
        }
        if (!sessionData) {
          return {
            statusCode: 200,
            headers: corsHeaders,
            body: JSON.stringify({ sessionId: null, step1: false, step2: false, expired: true })
          };
        }
        return {
          statusCode: 200,
          headers: corsHeaders,
          body: JSON.stringify({
            sessionId: sessionData.session_id,
            step1: sessionData.step1,
            step2: sessionData.step2,
            expired: false
          })
        };
      }
      if (body.action === 'complete') {
        console.log('Complete action received:', { sessionId: body.sessionId, step: body.step });

        if (!body.sessionId) {
          return {
            statusCode: 400,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Missing sessionId for complete action' })
          };
        }

        if (body.step !== 1 && body.step !== 2) {
          return {
            statusCode: 400,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Invalid step for complete action', received: body.step, expected: '1 or 2' })
          };
        }
        const userAgent = event.headers['user-agent'] || '';
        const rawIpAddressForLog = event.headers['x-forwarded-for'] || '';
        const ipAddressForLog = rawIpAddressForLog.split(',')[0].trim();
        await supabase.from('key_usage_logs').insert({
          key_id: null, hwid_hash: null, ip_address: ipAddressForLog, user_agent: userAgent,
          action: 'lootlabs_step_completion', success: true,
          error_message: null,
          details: JSON.stringify({ sessionId: body.sessionId, step: body.step })
        });
        const { verifyLootlabsCompletion } = await import('./utils/serverSideLootlabsVerification.js');
        const { data: sessionData, error: sessionError } = await supabase
          .from('key_sessions')
          .select('campaign_id')
          .eq('session_id', body.sessionId)
          .single();
        let campaignId = sessionData?.campaign_id;
        if (sessionError || !campaignId) {
          const { data: defaultCampaign } = await supabase
            .from('lootlabs_campaigns')
            .select('id')
            .eq('is_active', true)
            .limit(1)
            .single();
          if (defaultCampaign) {
            campaignId = defaultCampaign.id;
            await supabase
              .from('key_sessions')
              .update({ campaign_id: campaignId })
              .eq('session_id', body.sessionId);
          } else {
            return {
              statusCode: 400,
              headers: corsHeaders,
              body: JSON.stringify({ error: 'No active campaign available' })
            };
          }
        }
        const { data: campaignData, error: campaignError } = await supabase
          .from('lootlabs_campaigns')
          .select('return_url')
          .eq('id', campaignId)
          .single();
        if (campaignError || !campaignData?.return_url) {
          return {
            statusCode: 400,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Campaign return URL not found' })
          };
        }
        const expectedCallbackUrl = `${campaignData.return_url}?step=${body.step}&session=${body.sessionId}`;
        const verificationData = {
          referrer: event.headers.referer || event.headers.referrer,
          userAgent: userAgent,
          startTime: body.startTime,
          mouseMovement: body.mouseMovement || 0,
          keyboardActivity: body.keyboardActivity || 0,
          scrollActivity: body.scrollActivity || 0,
          focusEvents: body.focusEvents || 0,
          dataParameter: body.token, // This is the &data parameter from Lootlabs redirect
          expectedCallbackUrl: expectedCallbackUrl
        };
        let lootlabsVerificationSuccess = false;
        let lootlabsErrorMessage = '';
        try {
          const enhancedVerificationData = {
            ...verificationData,
            userAgent: event.headers['user-agent'] || '',
            referrer: event.headers['referer'] || event.headers['referrer'] || '',
            startTime: body.startTime || Date.now() - 30000, // Default to 30 seconds ago
            mouseMovement: body.behaviorData?.mouseMovements?.length || 0,
            keyboardActivity: body.behaviorData?.typingSpeed?.length || 0,
            scrollActivity: body.behaviorData?.scrollBehavior?.length || 0,
            focusEvents: body.behaviorData?.focusLossCount || 0,
            expectedCallbackUrl: `${event.headers.origin || 'https://projectmadara.com'}/generate-key?step=${body.step}&session=${body.sessionId}`
          };
          const verificationResult = await verifyLootlabsCompletion(body.sessionId, body.step, enhancedVerificationData);
          lootlabsVerificationSuccess = verificationResult.verified;
          lootlabsErrorMessage = verificationResult.error ||
            (verificationResult.verified ? null : `Server-side verification failed with score: ${verificationResult.score?.toFixed(2) || 'unknown'} (confidence: ${(verificationResult.confidence * 100).toFixed(1)}%)`);
          if (!verificationResult.verified) {
          }
        } catch (verificationError) {
          if (verificationError.message?.includes('network') || verificationError.message?.includes('timeout')) {
            lootlabsVerificationSuccess = true;
            lootlabsErrorMessage = null;
          } else {
            lootlabsVerificationSuccess = false;
            lootlabsErrorMessage = 'Server-side verification temporarily unavailable';
          }
        }
        if (!lootlabsVerificationSuccess) {
          await supabase.from('key_usage_logs').insert({
            key_id: null, hwid_hash: null, ip_address: ipAddressForLog, user_agent: userAgent,
            action: 'lootlabs_verification_failed', success: false,
            error_message: lootlabsErrorMessage,
            details: JSON.stringify({ sessionId: body.sessionId, step: body.step })
          });
          return {
            statusCode: 400,
            headers: corsHeaders,
            body: JSON.stringify({ error: `Lootlabs verification failed: ${lootlabsErrorMessage}` })
          };
        }
        const trackingToken = `lootlabs_${body.sessionId}_${body.step}_${Date.now()}`; // Add timestamp to make unique
        try {
          const tokenInsertResult = await supabase
            .from('used_tokens')
            .insert({
              token: trackingToken,
              session_id: body.sessionId,
              step: body.step,
              user_agent: userAgent,
              created_at: new Date().toISOString()
            });
          if (tokenInsertResult.error) {
          } else {
          }
        } catch (tokenError) {
        }
        const { data: currentSessionData, error: currentSessionError } = await supabase
          .from('key_sessions')
          .select('*')
          .eq('session_id', body.sessionId)
          .single();
        if (currentSessionError || !currentSessionData) {
          return {
            statusCode: 400,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Invalid session' })
          };
        }
        const stepField = body.step === 1 ? 'step1' : 'step2';
        if (currentSessionData[stepField] === true) {
          return { statusCode: 200, headers: corsHeaders, body: JSON.stringify({ success: true, alreadyCompleted: true }) };
        }
        if (currentSessionData.security_violation) {
          await supabase.from('key_usage_logs').insert({
            key_id: null, hwid_hash: null, ip_address: ipAddressForLog, user_agent: userAgent,
            action: 'session_security_violation', success: false,
            error_message: 'Session marked as compromised due to security violation.',
            details: JSON.stringify({ sessionId: body.sessionId, step: body.step })
          });
          return {
            statusCode: 403,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Session compromised due to security violation' })
          };
        }
        const field = body.step === 1 ? 'step1' : 'step2';
        const updateResult = await supabase
          .from('key_sessions')
          .update({ [field]: true })
          .eq('session_id', body.sessionId);
        if (updateResult.error) {
          await supabase.from('key_usage_logs').insert({
            key_id: null, hwid_hash: null, ip_address: ipAddressForLog, user_agent: userAgent,
            action: 'session_update_failed', success: false,
            error_message: `Failed to update session status: ${updateResult.error.message}`,
            details: JSON.stringify({ sessionId: body.sessionId, step: body.step, field: field, error: updateResult.error })
          });
          return {
            statusCode: 500,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Failed to update session status. Please try again.' })
          };
        }
        const { data: updatedSessionData, error: verifyError } = await supabase
          .from('key_sessions')
          .select('step1, step2')
          .eq('session_id', body.sessionId)
          .single();
        if (verifyError) {
        } else {
        }
        return { statusCode: 200, headers: corsHeaders, body: JSON.stringify({ success: true }) };
      }
      if (body.action === 'mark_compromised') {
        const { error } = await supabase
          .from('key_sessions')
          .update({
            security_violation: true,
            violation_type: body.violationType,
            violation_timestamp: new Date().toISOString()
          })
          .eq('session_id', body.sessionId);
        if (error) {
          return {
            statusCode: 500,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Failed to mark session as compromised' })
          };
        }
        return { statusCode: 200, headers: corsHeaders, body: JSON.stringify({ success: true }) };
      }
      if (body.action === 'cleanup_session') {
        const userAgent = event.headers['user-agent'] || '';
        const rawIpAddressForLog = event.headers['x-forwarded-for'] || '';
        const ipAddressForLog = rawIpAddressForLog.split(',')[0].trim();

        const { error: deleteError } = await supabase
          .from('key_sessions')
          .delete()
          .eq('session_id', body.sessionId);
        if (deleteError) {
          return {
            statusCode: 500,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Failed to cleanup session' })
          };
        }
        await supabase.from('key_usage_logs').insert({
          key_id: null,
          hwid_hash: null,
          ip_address: ipAddressForLog,
          user_agent: userAgent,
          action: 'session_cleanup',
          success: true,
          error_message: null,
          details: JSON.stringify({
            sessionId: body.sessionId,
            reason: body.reason || 'security_violation_cleanup',
            timestamp: new Date().toISOString()
          })
        });
        return { statusCode: 200, headers: corsHeaders, body: JSON.stringify({ success: true }) };
      }
      if (body.action === 'status' && body.sessionId) {
        const { data, error } = await supabase
          .from('key_sessions')
          .select('step1, step2')
          .eq('session_id', body.sessionId)
          .single();
        if (error || !data) {
          return {
            statusCode: 404,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Session not found or error fetching status' })
          };
        }
        return {
          statusCode: 200,
          headers: corsHeaders,
          body: JSON.stringify({ status: data })
        };
      }
      console.log('Unrecognized action:', body.action);
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({
          error: 'Invalid request',
          details: `Unrecognized action: ${body.action}`,
          validActions: ['start', 'create', 'status', 'complete', 'mark_compromised', 'cleanup_session']
        })
      };
    }
    return { statusCode: 405, headers: corsHeaders, body: JSON.stringify({ error: 'Method Not Allowed' }) };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error', details: error.message })
    };
  }
}; 