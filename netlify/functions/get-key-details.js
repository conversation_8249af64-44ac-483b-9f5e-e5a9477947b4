import { createClient } from '@supabase/supabase-js';
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
if (!supabaseUrl || !supabaseServiceRoleKey) {
  throw new Error('Missing Supabase environment variables');
}
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Credentials': 'true'
};
export const handler = async (event, context) => {
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers: corsHeaders, body: '' };
  }
  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }
  try {
    const keyId = event.path.split('/').pop();
    const userAgent = event.headers['user-agent'] || '';
    const ipAddress = event.headers['x-forwarded-for']?.split(',')[0]?.trim() || 'unknown';
    if (!keyId) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Key ID required' })
      };
    }
    // Get key details from database
    const { data: keyData, error: keyError } = await supabase
      .from('license_keys')
      .select(`
        id,
        key_code,
        created_at,
        expires_at,
        last_used_at,
        usage_count,
        is_active,
        roblox_hwid,
        ip_address,
        user_agent
      `)
      .eq('id', keyId)
      .single();
    if (keyError || !keyData) {
      return {
        statusCode: 404,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Key not found' })
      };
    }
    // Verify access - key must belong to the same IP address
    if (keyData.ip_address !== ipAddress) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Access denied - key belongs to different user' })
      };
    }
    // Determine key status
    const now = new Date();
    const expiresAt = new Date(keyData.expires_at);
    let status = 'active';
    if (!keyData.is_active) {
      status = 'revoked';
    } else if (expiresAt < now) {
      status = 'expired';
    }
    // Transform data for frontend
    const transformedKey = {
      id: keyData.id,
      key_code: keyData.key_code,
      game_name: 'Roblox',
      script_name: 'Project Madara',
      generated_at: keyData.created_at,
      expires_at: keyData.expires_at,
      last_used: keyData.last_used_at,
      usage_count: keyData.usage_count || 0,
      status: status,
      hwid_hash: keyData.roblox_hwid,
      metadata: {}
    };
    // Log key access
    await supabase.from('key_usage_logs').insert({
      key_id: keyData.id,
      ip_address: ipAddress,
      user_agent: userAgent,
      action: 'key_viewed',
      success: true,
      details: JSON.stringify({
        key_id: keyId,
        status: status
      })
    });
    // Update last_used timestamp
    await supabase
      .from('license_keys')
      .update({ last_used_at: new Date().toISOString() })
      .eq('id', keyId);
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        ...transformedKey
      })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
