import { createClient } from '@supabase/supabase-js';
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
if (!supabaseUrl || !supabaseServiceRoleKey) {
  throw new Error('Missing Supabase environment variables');
}
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-session-token, x-admin-username, x-admin-password',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Credentials': 'true'
};
export const handler = async (event, context) => {
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers: corsHeaders, body: '' };
  }
  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }
  try {
    const userAgent = event.headers['user-agent'] || '';
    const ipAddress = event.headers['x-forwarded-for']?.split(',')[0]?.trim() || 'unknown';
    // Get overall key statistics
    const { data: keyStats, error: keyStatsError } = await supabase
      .from('license_keys')
      .select('id, created_at, expires_at, usage_count, is_active')
      .eq('ip_address', ipAddress);
    if (keyStatsError) {
    }
    // Ad system removed - no longer tracking ad statistics
    // Get revenue statistics
    const { data: revenueStats, error: revenueStatsError } = await supabase
      .from('revenue_events')
      .select('amount, revenue_source, occurred_at')
      .eq('metadata->ip_address', ipAddress);
    if (revenueStatsError) {
    }
    // Calculate statistics
    const keys = keyStats || [];
    const revenue = revenueStats || [];
    const now = new Date();
    const activeKeys = keys.filter(key =>
      key.is_active && new Date(key.expires_at) > now
    ).length;
    const totalDownloads = keys.reduce((sum, key) => sum + (key.usage_count || 0), 0);
    const totalRevenue = revenue.reduce((sum, rev) => sum + parseFloat(rev.amount || 0), 0);
    // Recent activity (last 7 days)
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const recentKeys = keys.filter(key => new Date(key.created_at) > weekAgo).length;
    // Game breakdown
    const { data: gameStats, error: gameStatsError } = await supabase
      .from('license_keys')
      .select(`
        scripts!inner(game_name),
        id
      `)
      .eq('ip_address', ipAddress);
    const gameBreakdown = {};
    if (gameStats && !gameStatsError) {
      gameStats.forEach(key => {
        const gameName = key.scripts?.game_name || 'Unknown';
        gameBreakdown[gameName] = (gameBreakdown[gameName] || 0) + 1;
      });
    }
    const stats = {
      // Key Statistics
      totalKeys: keys.length,
      activeKeys: activeKeys,
      expiredKeys: keys.length - activeKeys,
      totalDownloads: totalDownloads,
      recentKeys: recentKeys,
      // Revenue Statistics
      totalRevenue: totalRevenue,
      // Breakdowns
      gameBreakdown: gameBreakdown,
      // Activity indicators
      availableTasks: 0, // Placeholder for future tasks system
      pendingReferrals: 0, // Placeholder for future referral system
      // Performance metrics
      averageKeyLifetime: calculateAverageKeyLifetime(keys),
      mostUsedGame: getMostUsedGame(gameBreakdown),
      // User engagement
      lastActivity: getLastActivity(keys),
      engagementScore: calculateEngagementScore(keys, revenue)
    };
    // Log analytics
    await supabase.from('user_analytics').insert({
      event_type: 'dashboard_stats_viewed',
      event_data: {
        total_keys: stats.totalKeys,
        active_keys: stats.activeKeys,
        total_revenue: stats.totalRevenue
      },
      occurred_at: new Date().toISOString(),
      ip_address: ipAddress,
      user_agent: userAgent
    });
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        stats: stats,
        generated_at: new Date().toISOString()
      })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
const calculateAverageKeyLifetime = (keys) => {
  if (keys.length === 0) return 0;
  const lifetimes = keys.map(key => {
    const created = new Date(key.created_at);
    const expires = new Date(key.expires_at);
    return expires - created;
  });
  const averageMs = lifetimes.reduce((sum, lifetime) => sum + lifetime, 0) / lifetimes.length;
  return Math.round(averageMs / (1000 * 60 * 60)); // Convert to hours
};
const getMostUsedGame = (gameBreakdown) => {
  if (Object.keys(gameBreakdown).length === 0) return 'None';
  return Object.entries(gameBreakdown)
    .sort(([,a], [,b]) => b - a)[0]?.[0] || 'None';
};
const getLastActivity = (keys) => {
  const allDates = keys.map(k => new Date(k.created_at));
  if (allDates.length === 0) return null;
  return Math.max(...allDates);
};
const calculateEngagementScore = (keys, revenue) => {
  // Simple engagement score based on activity
  let score = 0;
  // Points for keys generated
  score += keys.length * 10;
  // Points for revenue generation
  score += revenue.length * 15;
  // Recent activity bonus (last 7 days)
  const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
  const recentActivity = keys.filter(k => new Date(k.created_at) > weekAgo).length;
  score += recentActivity * 20;
  // Cap at 1000
  return Math.min(score, 1000);
};
