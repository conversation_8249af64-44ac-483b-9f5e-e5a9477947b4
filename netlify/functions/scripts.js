import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);
const allowCors = (fn) => async (request, context) => {
    const origin = request.headers.get('origin') || '*';
    const headers = {
        'Access-Control-Allow-Origin': origin,
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Credentials': 'true',
    };
    if (request.method === 'OPTIONS') {
        return new Response('', { status: 204, headers });
    }
    const result = await fn(request, context, headers);
    return result;
};
const verifySimpleAdmin = async (request) => {
  const username = request.headers.get('x-admin-username');
  const password = request.headers.get('x-admin-password');
  if (!username || !password) {
    return { error: 'Missing credentials' };
  }
  try {
    const { data: adminUser, error: dbError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('username', username)
      .eq('is_active', true)
      .single();
    if (dbError || !adminUser) {
      return { error: 'Invalid credentials' };
    }
    const isPasswordValid = await bcrypt.compare(password, adminUser.password_hash);
    if (isPasswordValid) {
      return { admin: { username, role: adminUser.role, permissions: adminUser.permissions } };
    }
    return { error: 'Invalid credentials' };
  } catch (error) {
    return { error: 'Authentication failed' };
  }
};
const originalHandler = async (request, context, corsHeaders) => {
    try {
        const url = new URL(request.url);
        const queryStringParameters = Object.fromEntries(url.searchParams.entries());
        const headers = request.headers;
        let body = null;
        if (request.method === 'POST' || request.method === 'PUT') {
            body = await request.json();
        }
        if (request.method !== 'GET') {
            const authResult = await verifySimpleAdmin(request);
            if (authResult.error) {
                return new Response(JSON.stringify({ error: authResult.error }), {
                    status: 401,
                    headers: corsHeaders
                });
            }
        }
    if (request.method === 'GET') {
        const { id } = queryStringParameters;
        // Select fields excluding sensitive content for security
        const publicFields = `
            id, name, description, category, tags, executor, version,
            views, rating, rating_count, uploaded_by, created_at, updated_at,
            is_active, storage_type, access_level, requires_valid_key, file_size_bytes
        `;

        if (id) {
            const { data, error } = await supabase
                .from('scripts')
                .select(publicFields)
                .eq('id', id)
                .single();
            if (error || !data) {
                return new Response(JSON.stringify({ error: error?.message || 'Script not found' }), {
                    status: 404,
                    headers: corsHeaders
                });
            }

            // Add placeholder content for backward compatibility
            const responseData = {
                ...data,
                content: '-- Use the "Get Secure Script" button to access this script with your valid key'
            };

            return new Response(JSON.stringify(responseData), {
                status: 200,
                headers: corsHeaders
            });
        } else {
            const { data, error } = await supabase
                .from('scripts')
                .select(publicFields)
                .eq('is_active', true)
                .order('created_at', { ascending: false });
            if (error) {
                return new Response(JSON.stringify([]), {
                    status: 200,
                    headers: corsHeaders
                });
            }

            // Add placeholder content for all scripts
            const responseData = (data || []).map(script => ({
                ...script,
                content: '-- Use the "Get Secure Script" button to access this script with your valid key'
            }));

            return new Response(JSON.stringify(responseData), {
                status: 200,
                headers: corsHeaders
            });
        }
    }
    if (request.method === 'POST') {
        if (!body.name || !body.content) {
            return new Response(JSON.stringify({ error: 'Name and content are required.' }), {
                status: 400,
                headers: corsHeaders
            });
        }
        const { data, error } = await supabase
            .from('scripts')
            .insert([{ ...body }])
            .select()
            .single();
        if (error) {
            return new Response(JSON.stringify({ error: error.message }), {
                status: 500,
                headers: corsHeaders
            });
        }
        return new Response(JSON.stringify(data), {
            status: 201,
            headers: corsHeaders
        });
    }
    if (request.method === 'PUT' && queryStringParameters.action === 'update-log') {
        const { script_id, version, changes, updated_by } = body || {};
        if (!script_id || !version || !changes || !updated_by) {
            return new Response(JSON.stringify({ error: 'Missing required fields.' }), {
                status: 400,
                headers: corsHeaders
            });
        }
        const { data, error } = await supabase
            .from('script_update_logs')
            .insert([{
                script_id,
                version,
                changes,
                updated_by
            }])
            .select()
            .single();
        if (error) {
            return new Response(JSON.stringify({ error: error.message }), {
                status: 500,
                headers: corsHeaders
            });
        }
        return new Response(JSON.stringify({ success: true, log: data }), {
            status: 201,
            headers: corsHeaders
        });
    }
    if (request.method === 'PUT' && !queryStringParameters.action) {
        const { id, ...updateData } = body || {};
        if (!id) {
            return new Response(JSON.stringify({ error: 'Script ID is required.' }), {
                status: 400,
                headers: corsHeaders
            });
        }
        const { data, error } = await supabase
            .from('scripts')
            .update(updateData)
            .eq('id', id)
            .select()
            .single();
        if (error) {
            return new Response(JSON.stringify({ error: error.message }), {
                status: 500,
                headers: corsHeaders
            });
        }
        return new Response(JSON.stringify(data), {
            status: 200,
            headers: corsHeaders
        });
    }
    if (request.method === 'DELETE') {
        const { id } = queryStringParameters;
        if (!id) {
            return new Response(JSON.stringify({ error: 'Script ID is required.' }), {
                status: 400,
                headers: corsHeaders
            });
        }
        const { error } = await supabase
            .from('scripts')
            .delete()
            .eq('id', id);
        if (error) {
            return new Response(JSON.stringify({ error: error.message }), {
                status: 500,
                headers: corsHeaders
            });
        }
        return new Response(JSON.stringify({ success: true }), {
            status: 200,
            headers: corsHeaders
        });
    }
        return new Response(JSON.stringify({ error: 'Method Not Allowed' }), {
            status: 405,
            headers: corsHeaders
        });
    } catch (error) {
        return new Response(JSON.stringify({
            error: 'Internal server error',
            message: error.message
        }), {
            status: 500,
            headers: corsHeaders
        });
    }
};
export default allowCors(originalHandler); 