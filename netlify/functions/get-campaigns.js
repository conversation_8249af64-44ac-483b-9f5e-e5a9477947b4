import { createClient } from '@supabase/supabase-js';
// CORS helper
const allowCors = (handler) => {
  return async (request, context) => {
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
    };
    if (request.method === 'OPTIONS') {
      return new Response('', { status: 200, headers });
    }
    try {
      const result = await handler(request, context, headers);
      return result;
    } catch (error) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers,
      });
    }
  };
};
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY; // Use anon key for public read
const supabase = createClient(supabaseUrl, supabaseAnonKey);
const getCampaignsHandler = async (request, _context, corsHeaders) => {
  if (request.method !== 'GET') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: corsHeaders,
    });
  }
  try {
    const { data: campaigns, error } = await supabase
      .from('lootlabs_campaigns')
      .select('id, name, tier_id, number_of_tasks, theme, return_url') // Select necessary fields
      .eq('is_active', true);
    if (error) {
      return new Response(JSON.stringify({
        error: 'Failed to fetch campaigns',
        details: error.message,
        code: error.code
      }), {
        status: 500,
        headers: corsHeaders,
      });
    }
    return new Response(JSON.stringify(campaigns), {
      status: 200,
      headers: corsHeaders,
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};
export default allowCors(getCampaignsHandler);