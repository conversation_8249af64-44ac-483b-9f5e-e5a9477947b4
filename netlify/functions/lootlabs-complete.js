import { createClient } from '@supabase/supabase-js';
import { verifyLootlabsCompletion } from './utils/serverSideLootlabsVerification.js';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Credentials': 'true'
};

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
if (!supabaseUrl || !supabaseServiceRoleKey) {
  throw new Error('Missing Supabase environment variables');
}
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);
export const handler = async (event) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers: corsHeaders };
  }

  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  const { step, session } = event.queryStringParameters || {};

  // Enhanced logging for debugging
  console.log('Lootlabs completion request:', {
    step,
    session,
    queryParams: event.queryStringParameters,
    headers: {
      referer: event.headers['referer'] || event.headers['referrer'],
      userAgent: event.headers['user-agent']
    }
  });

  if (!step || !session) {
    console.log('Missing parameters:', { step, session });
    return {
      statusCode: 400,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Missing step or session parameter',
        received: { step, session }
      })
    };
  }

  const stepNum = parseInt(step);
  if (stepNum !== 1 && stepNum !== 2) {
    console.log('Invalid step number:', stepNum);
    return {
      statusCode: 400,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Invalid step number',
        received: stepNum,
        expected: '1 or 2'
      })
    };
  }
  try {
    // Validate session exists
    const { data: sessionData, error: sessionError } = await supabase
      .from('key_sessions')
      .select('*')
      .eq('session_id', session)
      .single();

    if (sessionError || !sessionData) {
      console.log('Session validation failed:', { sessionError, sessionData });
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({
          error: 'Invalid session',
          details: sessionError?.message || 'Session not found'
        })
      };
    }
    // Enhanced verification using referrer and timing analysis
    // Verify with Lootlabs API that the task was actually completed
    const verificationData = {
      userAgent: event.headers['user-agent'] || '',
      ipAddress: event.headers['x-forwarded-for']?.split(',')[0]?.trim() || '',
      referrer: event.headers['referer'] || event.headers['referrer'] || '',
      startTime: sessionData.created_at ? new Date(sessionData.created_at).getTime() : Date.now()
    };

    console.log('Verification data:', verificationData);

    const lootlabsVerified = await verifyLootlabsCompletion(session, stepNum, verificationData);

    console.log('Lootlabs verification result:', lootlabsVerified);

    if (!lootlabsVerified.verified) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({
          error: 'Lootlabs verification failed - task not completed',
          details: lootlabsVerified.reason || 'Verification failed',
          score: lootlabsVerified.score || 0
        })
      };
    }
    // Log the verification for audit purposes
    // Check if step is already completed
    const field = stepNum === 1 ? 'step1' : 'step2';
    if (sessionData[field] !== true) {
      // Mark step as completed
      const { error: updateError } = await supabase
        .from('key_sessions')
        .update({ [field]: true })
        .eq('session_id', session);
      if (updateError) {
        console.log('Database update error:', updateError);
        return {
          statusCode: 500,
          headers: corsHeaders,
          body: JSON.stringify({
            error: 'Database update failed',
            details: updateError.message
          })
        };
      }
      // Log the verified completion
      await supabase.from('key_usage_logs').insert({
        key_id: null,
        hwid_hash: null,
        ip_address: event.headers['x-forwarded-for']?.split(',')[0]?.trim() || '',
        user_agent: event.headers['user-agent'] || '',
        action: 'lootlabs_verified_completion',
        success: true,
        error_message: null,
        details: JSON.stringify({
          sessionId: session,
          step: stepNum,
          field,
          verificationScore: lootlabsVerified.score,
          verificationMethod: 'referrer_timing_analysis',
          referrer: event.headers['referer'] || event.headers['referrer'] || ''
        })
      });
    }
    // Determine redirect URL based on step completion
    const baseUrl = process.env.SITE_URL || 'https://projectmadara.com';
    let redirectUrl;
    if (stepNum === 1) {
      // Step 1 completed, redirect to step 2
      redirectUrl = `${baseUrl}/generate-key?step=2&session=${session}&completed=1`;
    } else if (stepNum === 2) {
      // Step 2 completed, redirect to step 3 (key generation)
      redirectUrl = `${baseUrl}/generate-key?step=3&session=${session}&completed=2`;
    }
    // Redirect user to the appropriate step
    console.log('Redirecting to:', redirectUrl);
    return {
      statusCode: 302,
      headers: {
        ...corsHeaders,
        'Location': redirectUrl,
        'Cache-Control': 'no-cache'
      },
      body: ''
    };
  } catch (error) {
    console.log('Function error:', error);
    // Redirect to error page or main page
    const baseUrl = process.env.SITE_URL || 'https://projectmadara.com';
    const errorUrl = `${baseUrl}/generate-key?session=${session}&error=completion_failed`;
    return {
      statusCode: 302,
      headers: {
        ...corsHeaders,
        'Location': errorUrl,
        'Cache-Control': 'no-cache'
      },
      body: ''
    };
  }
};
