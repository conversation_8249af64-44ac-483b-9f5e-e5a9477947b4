import { createClient } from '@supabase/supabase-js';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Credentials': 'true'
};

export const handler = async (event) => {
  try {
    if (event.httpMethod === 'OPTIONS') {
      return { statusCode: 200, headers: corsHeaders };
    }
    
    if (event.httpMethod === 'GET') {
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          message: 'generate-key endpoint is live. Use POST for key generation.'
        })
      };
    }

    if (event.httpMethod !== 'POST') {
      return {
        statusCode: 405,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Parse request body
    const body = JSON.parse(event.body || '{}');
    const { sessionId } = body;
    
    if (!sessionId) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Session ID required' })
      };
    }

    // Initialize Supabase
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Check session exists and steps are completed
    const { data: session, error: sessionError } = await supabase
      .from('key_sessions')
      .select('*')
      .eq('session_id', sessionId)
      .single();

    if (sessionError || !session) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid session' })
      };
    }

    if (!session.step1 || !session.step2) {
      return {
        statusCode: 403,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'All verification steps must be completed first' })
      };
    }

    // Check for existing key in last 24 hours
    const ipAddress = event.headers['x-forwarded-for']?.split(',')[0]?.trim() || 'unknown';
    const userAgent = event.headers['user-agent'] || '';
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    const { data: existingKeys } = await supabase
      .from('license_keys')
      .select('key_code, expires_at')
      .eq('ip_address', ipAddress)
      .eq('is_revoked', false)
      .gte('created_at', twentyFourHoursAgo.toISOString())
      .order('created_at', { ascending: false })
      .limit(1);

    if (existingKeys && existingKeys.length > 0) {
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          success: true,
          key_exists: true,
          key: existingKeys[0].key_code,
          expires_at: existingKeys[0].expires_at,
          message: 'You have already generated a key within the last 24 hours. Here is your existing key.'
        })
      };
    }

    // Generate new key using the database function that creates MADARA- prefixed keys
    const { data: keyCode, error: keyError } = await supabase.rpc('generate_key_code');

    if (keyError) {
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Failed to generate key code' })
      };
    }

    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    const { data: key, error: insertError } = await supabase
      .from('license_keys')
      .insert({
        key_code: keyCode,
        expires_at: expiresAt.toISOString(),
        ip_address: ipAddress,
        user_agent: userAgent,
        session_id: sessionId,
        is_active: true
      })
      .select()
      .single();

    if (insertError) {
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Failed to generate key' })
      };
    }

    // Log successful key generation
    await supabase.from('key_usage_logs').insert({
      key_id: key.id,
      ip_address: ipAddress,
      user_agent: userAgent,
      action: 'key_generated',
      success: true
    });

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        key: key.key_code,
        expires_at: key.expires_at,
        message: 'Key generated successfully! Valid for 24 hours.'
      })
    };

  } catch (error) {
    console.error('Generate key error:', error);
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error', details: error.message })
    };
  }
};
