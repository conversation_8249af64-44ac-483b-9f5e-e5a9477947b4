import { createClient } from '@supabase/supabase-js';
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);
const allowCors = (fn) => async (request, context) => {
    const origin = request.headers.get('origin') || '*';
    const headers = {
        'Access-Control-Allow-Origin': origin,
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Credentials': 'true',
    };
    if (request.method === 'OPTIONS') {
        return new Response('', { status: 204, headers });
    }
    const result = await fn(request, context, headers);
    return result;
};
const handler = async (request, context, corsHeaders) => {
    if (request.method !== 'GET') {
        return new Response(JSON.stringify({ error: 'Method Not Allowed' }), {
            status: 405,
            headers: corsHeaders
        });
    }
    const url = new URL(request.url);
    const id = url.searchParams.get('id');
    if (!id) {
        return new Response(JSON.stringify({ error: 'Missing request ID.' }), {
            status: 400,
            headers: corsHeaders
        });
    }
    const { data, error } = await supabase
        .from('script_requests')
        .select('*')
        .eq('id', id)
        .single();
    if (error || !data) {
        return new Response(JSON.stringify({ error: 'Request not found.' }), {
            status: 404,
            headers: corsHeaders
        });
    }
    return new Response(JSON.stringify(data), {
        status: 200,
        headers: corsHeaders
    });
};
export default allowCors(handler); 