import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);
class SessionManager {
  constructor() {
    this.sessionDuration = 24 * 60 * 60 * 1000; // 24 hours
    this.cleanupInterval = 60 * 60 * 1000; // 1 hour cleanup
    // Start periodic cleanup
    setInterval(() => this.cleanupExpiredSessions(), this.cleanupInterval);
  }
  generateSessionToken() {
    return crypto.randomBytes(32).toString('hex');
  }
  generateSessionId() {
    return crypto.randomUUID();
  }
  async createSession(ipAddress, userAgent, deviceFingerprint = null) {
    const sessionId = this.generateSessionId();
    const sessionToken = this.generateSessionToken();
    const expiresAt = new Date(Date.now() + this.sessionDuration);
    // Hash the IP for additional security
    const ipHash = crypto.createHash('sha256').update(ipAddress).digest('hex');
    try {
      const { data, error } = await supabase
        .from('secure_sessions')
        .insert({
          session_id: sessionId,
          session_token: sessionToken,
          ip_address: ipAddress,
          ip_hash: ipHash,
          user_agent: userAgent,
          device_fingerprint: deviceFingerprint,
          expires_at: expiresAt.toISOString(),
          created_at: new Date().toISOString(),
          last_activity: new Date().toISOString(),
          is_active: true,
          security_level: 'high',
          access_count: 0
        })
        .select()
        .single();
      if (error) {
        return { error: 'Failed to create session' };
      }
      return {
        sessionId,
        sessionToken,
        expiresAt: expiresAt.toISOString(),
        created: true
      };
    } catch (error) {
      return { error: 'Session creation failed' };
    }
  }
  async validateSession(sessionToken, ipAddress, userAgent) {
    if (!sessionToken || !ipAddress) {
      return { valid: false, reason: 'Missing session token or IP address' };
    }
    try {
      const { data: session, error } = await supabase
        .from('secure_sessions')
        .select('*')
        .eq('session_token', sessionToken)
        .eq('is_active', true)
        .single();
      if (error || !session) {
        return { valid: false, reason: 'Session not found' };
      }
      // Check expiration
      if (new Date(session.expires_at) < new Date()) {
        await this.invalidateSession(sessionToken);
        return { valid: false, reason: 'Session expired' };
      }
      // Verify IP address binding
      if (session.ip_address !== ipAddress) {
        await this.logSecurityViolation(session.session_id, 'ip_mismatch', {
          expectedIp: session.ip_address,
          actualIp: ipAddress
        });
        return { valid: false, reason: 'IP address mismatch' };
      }
      // Optional: Check user agent consistency
      if (session.user_agent !== userAgent) {
        await this.logSecurityViolation(session.session_id, 'user_agent_change', {
          expectedUA: session.user_agent,
          actualUA: userAgent
        });
        // Don't invalidate for UA changes, just log
      }
      // Update last activity
      await this.updateSessionActivity(sessionToken);
      return {
        valid: true,
        session: {
          sessionId: session.session_id,
          ipAddress: session.ip_address,
          createdAt: session.created_at,
          expiresAt: session.expires_at,
          securityLevel: session.security_level,
          accessCount: session.access_count + 1
        }
      };
    } catch (error) {
      return { valid: false, reason: 'Validation failed' };
    }
  }
  async updateSessionActivity(sessionToken) {
    try {
      await supabase
        .from('secure_sessions')
        .update({
          last_activity: new Date().toISOString(),
          access_count: supabase.raw('access_count + 1')
        })
        .eq('session_token', sessionToken);
    } catch (error) {
    }
  }
  async invalidateSession(sessionToken) {
    try {
      await supabase
        .from('secure_sessions')
        .update({
          is_active: false,
          invalidated_at: new Date().toISOString()
        })
        .eq('session_token', sessionToken);
      return { success: true };
    } catch (error) {
      return { error: 'Failed to invalidate session' };
    }
  }
  async getSessionByIp(ipAddress) {
    try {
      const { data: sessions, error } = await supabase
        .from('secure_sessions')
        .select('*')
        .eq('ip_address', ipAddress)
        .eq('is_active', true)
        .gt('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false })
        .limit(1);
      if (error || !sessions || sessions.length === 0) {
        return { found: false };
      }
      return {
        found: true,
        session: sessions[0]
      };
    } catch (error) {
      return { found: false, error: 'Lookup failed' };
    }
  }
  async cleanupExpiredSessions() {
    try {
      const { data, error } = await supabase
        .from('secure_sessions')
        .update({ is_active: false })
        .lt('expires_at', new Date().toISOString())
        .eq('is_active', true);
      if (!error) {
      }
    } catch (error) {
    }
  }
  async logSecurityViolation(sessionId, violationType, details) {
    try {
      await supabase
        .from('session_security_events')
        .insert({
          session_id: sessionId,
          violation_type: violationType,
          violation_details: details,
          timestamp: new Date().toISOString()
        });
    } catch (error) {
    }
  }
  async getSessionStats(ipAddress) {
    try {
      const { data, error } = await supabase
        .from('secure_sessions')
        .select('session_id, created_at, expires_at, access_count, security_level')
        .eq('ip_address', ipAddress)
        .order('created_at', { ascending: false })
        .limit(10);
      if (error) {
        return { error: 'Failed to get session stats' };
      }
      return {
        sessions: data,
        totalSessions: data.length,
        activeSessions: data.filter(s => new Date(s.expires_at) > new Date()).length
      };
    } catch (error) {
      return { error: 'Stats retrieval failed' };
    }
  }
  async extendSession(sessionToken, additionalHours = 24) {
    try {
      const newExpiresAt = new Date(Date.now() + (additionalHours * 60 * 60 * 1000));
      const { data, error } = await supabase
        .from('secure_sessions')
        .update({
          expires_at: newExpiresAt.toISOString(),
          last_activity: new Date().toISOString()
        })
        .eq('session_token', sessionToken)
        .eq('is_active', true)
        .select()
        .single();
      if (error) {
        return { error: 'Failed to extend session' };
      }
      return {
        success: true,
        newExpiresAt: newExpiresAt.toISOString()
      };
    } catch (error) {
      return { error: 'Extension failed' };
    }
  }
}
// Global instance
export const sessionManager = new SessionManager();
// Convenience functions
export const createSession = (ipAddress, userAgent, deviceFingerprint) => {
  return sessionManager.createSession(ipAddress, userAgent, deviceFingerprint);
};
export const validateSession = (sessionToken, ipAddress, userAgent) => {
  return sessionManager.validateSession(sessionToken, ipAddress, userAgent);
};
export const invalidateSession = (sessionToken) => {
  return sessionManager.invalidateSession(sessionToken);
};
export const getSessionByIp = (ipAddress) => {
  return sessionManager.getSessionByIp(ipAddress);
};
