import crypto from 'crypto';
class ServerSignatureVerifier {
  constructor() {
    this.usedNonces = new Map();
    this.maxNonceAge = 5 * 60 * 1000; // 5 minutes
    this.cleanupInterval = 60 * 1000; // Clean up every minute
    // Start cleanup interval
    setInterval(() => this.cleanupExpiredNonces(), this.cleanupInterval);
  }
  generateServerSigningKey(userAgent, clientFingerprint, timestamp) {
    const screenInfo = '1920x1080'; // Default for server-side
    const timezoneOffset = 0; // UTC for server
    const keyMaterial = `${timestamp}-${userAgent}-${screenInfo}-${timezoneOffset}`;
    return crypto.createHash('sha256').update(keyMaterial).digest('hex');
  }
  createSignature(method, url, body, nonce, timestamp, signingKey) {
    const bodyHash = body ? crypto.createHash('sha256').update(JSON.stringify(body)).digest('hex') : '';
    const message = `${method}|${url}|${bodyHash}|${nonce}|${timestamp}`;
    return crypto.createHmac('sha256', signingKey).update(message).digest('hex');
  }
  verifyRequestSignature(headers, method, url, body) {
    const signature = headers['x-request-signature'];
    const nonce = headers['x-request-nonce'];
    const timestamp = headers['x-request-timestamp'];
    const clientFingerprint = headers['x-client-fingerprint'];
    const userAgent = headers['user-agent'] || '';
    // Check required headers
    if (!signature || !nonce || !timestamp || !clientFingerprint) {
      return {
        valid: false,
        reason: 'Missing required signature headers',
        details: {
          hasSignature: !!signature,
          hasNonce: !!nonce,
          hasTimestamp: !!timestamp,
          hasFingerprint: !!clientFingerprint
        }
      };
    }
    const requestTimestamp = parseInt(timestamp);
    const now = Date.now();
    // Check timestamp validity (5 minutes window)
    if (Math.abs(now - requestTimestamp) > this.maxNonceAge) {
      return {
        valid: false,
        reason: 'Request timestamp outside acceptable window',
        details: {
          requestTime: requestTimestamp,
          serverTime: now,
          difference: Math.abs(now - requestTimestamp)
        }
      };
    }
    // Check nonce uniqueness (prevent replay attacks)
    if (this.usedNonces.has(nonce)) {
      return {
        valid: false,
        reason: 'Nonce already used (replay attack detected)',
        details: { nonce }
      };
    }
    // Generate expected signature
    const signingKey = this.generateServerSigningKey(userAgent, clientFingerprint, requestTimestamp);
    const expectedSignature = this.createSignature(method, url, body, nonce, requestTimestamp, signingKey);
    // Verify signature
    if (expectedSignature !== signature) {
      return {
        valid: false,
        reason: 'Invalid signature',
        details: {
          expected: expectedSignature.substring(0, 8) + '...',
          received: signature.substring(0, 8) + '...'
        }
      };
    }
    // Store nonce to prevent reuse
    this.usedNonces.set(nonce, now);
    return {
      valid: true,
      reason: 'Signature verified successfully',
      details: {
        nonce,
        timestamp: requestTimestamp,
        fingerprint: clientFingerprint.substring(0, 8) + '...'
      }
    };
  }
  cleanupExpiredNonces() {
    const now = Date.now();
    const expiredNonces = [];
    for (const [nonce, timestamp] of this.usedNonces.entries()) {
      if (now - timestamp > this.maxNonceAge) {
        expiredNonces.push(nonce);
      }
    }
    expiredNonces.forEach(nonce => this.usedNonces.delete(nonce));
    if (expiredNonces.length > 0) {
    }
  }
  createResponseSignature(responseBody, requestNonce) {
    const timestamp = Date.now();
    const responseHash = crypto.createHash('sha256').update(JSON.stringify(responseBody)).digest('hex');
    const message = `${responseHash}|${requestNonce}|${timestamp}`;
    const signature = crypto.createHmac('sha256', process.env.RESPONSE_SIGNING_SECRET || 'default-secret').update(message).digest('hex');
    return {
      'X-Response-Signature': signature,
      'X-Response-Timestamp': timestamp.toString(),
      'X-Response-Nonce': requestNonce
    };
  }
  requireSignedRequest(headers, method, url, body) {
    const verification = this.verifyRequestSignature(headers, method, url, body);
    if (!verification.valid) {
      const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, X-Request-Signature, X-Request-Nonce, X-Request-Timestamp, X-Client-Fingerprint',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Credentials': 'true'
      };
      return {
        statusCode: 401,
        headers: corsHeaders,
        body: JSON.stringify({
          error: 'Request signature verification failed',
          reason: verification.reason,
          details: verification.details
        })
      };
    }
    return null; // No error, signature is valid
  }
}
// Global instance
export const signatureVerifier = new ServerSignatureVerifier();
// Middleware function for easy integration
export const requireSignedRequest = (headers, method, url, body) => {
  return signatureVerifier.requireSignedRequest(headers, method, url, body);
};
export const verifyRequestSignature = (headers, method, url, body) => {
  return signatureVerifier.verifyRequestSignature(headers, method, url, body);
};
export const createResponseSignature = (responseBody, requestNonce) => {
  return signatureVerifier.createResponseSignature(responseBody, requestNonce);
};
