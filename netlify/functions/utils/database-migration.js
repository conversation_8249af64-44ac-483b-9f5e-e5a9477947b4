// Database migration utilities for Project Madara
import { createClient } from '@supabase/supabase-js';
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);
/**
 * Check if campaign_id column exists in key_sessions table
 */
export async function checkCampaignIdColumn() {
  try {
    const { data, error } = await supabase.rpc('check_column_exists', {
      table_name: 'key_sessions',
      column_name: 'campaign_id'
    });
    if (error) {
      // Fallback: try to select campaign_id column
      const { error: selectError } = await supabase
        .from('key_sessions')
        .select('campaign_id')
        .limit(1);
      return !selectError || !selectError.message.includes('column "campaign_id" does not exist');
    }
    return data;
  } catch (err) {
    return false;
  }
}
/**
 * Add campaign_id column to key_sessions table if it doesn't exist
 */
export async function addCampaignIdColumn() {
  try {
    // This would require admin privileges, so we'll log the SQL needed
    const sql = `
      ALTER TABLE key_sessions 
      ADD COLUMN IF NOT EXISTS campaign_id UUID REFERENCES lootlabs_campaigns(id);
      CREATE INDEX IF NOT EXISTS idx_key_sessions_campaign_id ON key_sessions(campaign_id);
    `;
    return false; // Return false since we can't execute DDL from functions
  } catch (err) {
    return false;
  }
}
/**
 * Ensure there's at least one active campaign
 */
export async function ensureActiveCampaign() {
  try {
    const { data: existingCampaigns, error: fetchError } = await supabase
      .from('lootlabs_campaigns')
      .select('id, name, is_active')
      .eq('is_active', true);
    if (fetchError) {
      return null;
    }
    if (existingCampaigns && existingCampaigns.length > 0) {
      return existingCampaigns[0];
    }
    // No active campaigns found, check if any campaigns exist
    const { data: allCampaigns, error: allError } = await supabase
      .from('lootlabs_campaigns')
      .select('id, name, is_active');
    if (allError) {
      return null;
    }
    if (allCampaigns && allCampaigns.length > 0) {
      // Activate the first campaign
      const { data: updatedCampaign, error: updateError } = await supabase
        .from('lootlabs_campaigns')
        .update({ is_active: true })
        .eq('id', allCampaigns[0].id)
        .select()
        .single();
      if (updateError) {
        return null;
      }
      return updatedCampaign;
    }
    return null;
  } catch (err) {
    return null;
  }
}
/**
 * Run basic database checks and migrations
 */
export async function runDatabaseChecks() {
  const checks = {
    campaignIdColumn: await checkCampaignIdColumn(),
    activeCampaign: await ensureActiveCampaign()
  };
  return checks;
}
