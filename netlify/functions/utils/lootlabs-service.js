import { createClient } from '@supabase/supabase-js';
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
/**
 * Lootlabs Backend Service
 * Handles server-side Lootlabs API interactions for security and API key protection
 */
const LOOTLABS_API_KEY = process.env.LOOTLABS_API_KEY || '2f98a08a2f6f50d0e019640ee4b0e272fe4446c7f0979a5ceddcd0f6a9629ace';
const LOOTLABS_BASE_URL = 'https://creators.lootlabs.gg/api/public';
/**
 * Create a content locker using Lootlabs API
 * @param {Object} options - Content locker options
 * @returns {Promise<Object>} Lootlabs API response
 */
export async function createLootlabsLink(options) {
  const { title, url, tier_id, number_of_tasks, theme, thumbnail } = options;
  // Validate required parameters
  if (!title || !url || !tier_id || !number_of_tasks) {
    throw new Error('Missing required parameters: title, url, tier_id, number_of_tasks');
  }
  // Validate parameter ranges
  if (title.length > 30) {
    throw new Error('Title must be 30 characters or less');
  }
  if (tier_id < 1 || tier_id > 3) {
    throw new Error('tier_id must be between 1 and 3');
  }
  if (number_of_tasks < 1 || number_of_tasks > 5) {
    throw new Error('number_of_tasks must be between 1 and 5');
  }
  if (theme && (theme < 1 || theme > 5)) {
    throw new Error('theme must be between 1 and 5');
  }
  const requestBody = {
    title,
    url,
    tier_id,
    number_of_tasks,
    ...(theme && { theme }),
    ...(thumbnail && { thumbnail })
  };
  try {
    const response = await fetch(`${LOOTLABS_BASE_URL}/content_locker`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${LOOTLABS_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });
    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      throw new Error(`Invalid JSON response from Lootlabs API (HTTP ${response.status})`);
    }
    if (!response.ok) {
      // Provide more specific error messages based on status code
      if (response.status === 401) {
        throw new Error('Invalid Lootlabs API key - check your API key configuration');
      } else if (response.status === 403) {
        throw new Error('Lootlabs API access forbidden - check your account permissions');
      } else if (response.status === 429) {
        throw new Error('Lootlabs API rate limit exceeded - please try again later');
      } else if (response.status >= 500) {
        throw new Error('Lootlabs API server error - please try again later');
      }
      throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
    }
    if (data.type === 'error') {
      throw new Error(data.message || 'Lootlabs API returned an error');
    }
    // Validate response structure - Lootlabs returns message as an array
    if (!data.message || !Array.isArray(data.message) || !data.message[0] || !data.message[0].loot_url) {
      throw new Error('Invalid response structure from Lootlabs API - missing loot_url in message array');
    }
    return data;
  } catch (error) {
    // Re-throw with more context if it's a network error
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error('Network error connecting to Lootlabs API - check your internet connection');
    }
    throw new Error(`Failed to create Lootlabs link: ${error.message}`);
  }
}
/**
 * Encrypt a URL for anti-bypass protection
 * @param {string} destinationUrl - URL to encrypt
 * @returns {Promise<string>} Encrypted URL string
 */
export async function encryptLootlabsUrl(destinationUrl) {
  if (!destinationUrl) {
    throw new Error('destinationUrl is required');
  }
  const requestBody = {
    destination_url: destinationUrl,
    api_token: LOOTLABS_API_KEY
  };
  try {
    const response = await fetch(`${LOOTLABS_BASE_URL}/url_encryptor`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${LOOTLABS_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });
    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      throw new Error(`Invalid JSON response from Lootlabs URL encryption API (HTTP ${response.status})`);
    }
    if (!response.ok) {
      // Provide more specific error messages
      if (response.status === 401) {
        throw new Error('Invalid Lootlabs API key for URL encryption');
      } else if (response.status === 400) {
        throw new Error('Invalid URL provided for encryption');
      }
      throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
    }
    if (data.type === 'error') {
      throw new Error(data.message || 'Lootlabs URL encryption failed');
    }
    // Validate response structure
    if (!data.message) {
      throw new Error('Invalid response structure from Lootlabs URL encryption API - missing encrypted URL');
    }
    return data.message; // The encrypted URL string
  } catch (error) {
    // Re-throw with more context if it's a network error
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error('Network error connecting to Lootlabs URL encryption API');
    }
    throw new Error(`Failed to encrypt URL: ${error.message}`);
  }
}
/**
 * Verify an encrypted URL by re-encrypting the expected URL and comparing.
 * This acts as a pseudo-decryption/verification for Lootlabs anti-bypass.
 * @param {string} receivedEncryptedUrl - The 'data' parameter received from Lootlabs redirect.
 * @param {string} originalCallbackUrl - The original URL that was sent to Lootlabs for encryption.
 * @returns {Promise<boolean>} True if the received URL matches the re-encrypted original URL, false otherwise.
 */
export async function verifyEncryptedUrl(receivedEncryptedUrl, originalCallbackUrl) {
  if (!receivedEncryptedUrl || !originalCallbackUrl) {
    return false;
  }
  try {
    const requestBody = {
      destination_url: originalCallbackUrl,
      api_token: LOOTLABS_API_KEY
    };
    const response = await fetch(`${LOOTLABS_BASE_URL}/url_encryptor`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${LOOTLABS_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });
    const data = await response.json();
    if (!response.ok || data.type === 'error') {
      return false;
    }
    // Compare the received encrypted URL with the newly re-encrypted original URL
    const reEncryptedUrl = data.message;
    const isVerified = receivedEncryptedUrl === reEncryptedUrl;
    return isVerified;
  } catch (error) {
    return false;
  }
}
/**
 * Create campaign links for key generation steps
 * @param {Object} campaign - Campaign configuration
 * @param {string} sessionId - Session ID for tracking
 * @param {string} returnUrl - Override return URL (optional)
 * @returns {Promise<Object>} Created links for both steps
 */
export async function createCampaignLinks(campaign, sessionId, returnUrl = null) {
  const { name, tier_id, number_of_tasks, theme, return_url } = campaign;
  // Use provided returnUrl or fall back to campaign return_url
  const baseReturnUrl = returnUrl || return_url;
  if (!baseReturnUrl) {
    throw new Error('No return URL available - check campaign configuration or provide returnUrl parameter');
  }
  try {
    // Create completion endpoints that will mark steps complete and then redirect
    // Extract the base URL (protocol + domain) from the return URL
    const urlObj = new URL(baseReturnUrl);
    const baseUrl = `${urlObj.protocol}//${urlObj.host}`;
    const completionUrl1 = `${baseUrl}/.netlify/functions/lootlabs-complete?step=1&session=${sessionId}`;
    const completionUrl2 = `${baseUrl}/.netlify/functions/lootlabs-complete?step=2&session=${sessionId}`;
    // No need to encrypt URLs since we're using direct completion endpoints
    // Create Step 1 link - use completion URL that will mark step complete
    const step1Title = `${name} S1`.substring(0, 30);
    const step1Response = await createLootlabsLink({
      title: step1Title,
      url: completionUrl1, // Use completion URL that marks step complete
      tier_id,
      number_of_tasks,
      theme
    });
    // Create Step 2 link - use completion URL that will mark step complete
    const step2Title = `${name} S2`.substring(0, 30);
    const step2Response = await createLootlabsLink({
      title: step2Title,
      url: completionUrl2, // Use completion URL that marks step complete
      tier_id,
      number_of_tasks,
      theme
    });
    // Access the correct response structure - Lootlabs returns message as an array
    const step1LootUrl = step1Response.message[0].loot_url;
    const step1Short = step1Response.message[0].short;
    const step2LootUrl = step2Response.message[0].loot_url;
    const step2Short = step2Response.message[0].short;
    if (!step1LootUrl || !step2LootUrl) {
      throw new Error('Invalid Lootlabs API response - missing loot_url');
    }
    // Use the Lootlabs URLs directly - they will redirect to our completion endpoints
    const finalStep1Url = step1LootUrl;
    const finalStep2Url = step2LootUrl;
    return {
      step1: {
        url: finalStep1Url,
        short: step1Short
      },
      step2: {
        url: finalStep2Url,
        short: step2Short
      },
      campaign_data: {
        name,
        tier_id,
        number_of_tasks,
        theme,
        request_time_step1: step1Response.request_time,
        request_time_step2: step2Response.request_time
      }
    };
  } catch (error) {
    throw new Error(`Campaign link creation failed: ${error.message}`);
  }
}
/**
 * Enhanced Lootlabs completion verification with multiple validation methods
 * @param {string} sessionId - Session ID
 * @param {number} step - Step number (1 or 2)
 * @param {Object} verificationData - Additional verification data
 * @returns {Promise<Object>} Verification result
 */
export async function verifyLootlabsCompletion(sessionId, step, verificationData = {}) {
  const verification = {
    success: false,
    verified: false,
    method: 'enhanced_tracking',
    timestamp: new Date().toISOString(),
    details: {}
  };
  try {
    // Method 1: Time-based verification
    const timeVerification = await verifyCompletionTiming(sessionId, step, verificationData);
    verification.details.timeVerification = timeVerification;
    // Method 2: Referrer verification
    const referrerVerification = await verifyReferrer(verificationData.referrer);
    verification.details.referrerVerification = referrerVerification;
    // Method 3: Session state verification
    const sessionVerification = await verifySessionState(sessionId, step);
    verification.details.sessionVerification = sessionVerification;
    // Method 4: User behavior verification
    const behaviorVerification = await verifyUserBehavior(sessionId, verificationData);
    verification.details.behaviorVerification = behaviorVerification;
    // Method 5: Encrypted URL data verification (Anti-Bypass)
    let encryptedUrlVerification = { isValid: false, score: 0.0, details: 'No data parameter received' };
    if (verificationData.dataParameter && verificationData.expectedCallbackUrl) {
      const isUrlVerified = await verifyEncryptedUrl(verificationData.dataParameter, verificationData.expectedCallbackUrl);
      encryptedUrlVerification = {
        isValid: isUrlVerified,
        score: isUrlVerified ? 1.0 : 0.0,
        details: isUrlVerified ? 'Encrypted URL matched' : 'Encrypted URL mismatch'
      };
    }
    verification.details.encryptedUrlVerification = encryptedUrlVerification;
    // Calculate overall verification score
    const verificationScore = calculateVerificationScore(verification.details);
    verification.score = verificationScore;
    verification.verified = verificationScore >= 0.4; // 40% confidence threshold (very forgiving)
    verification.success = true;
    return verification;
  } catch (error) {
    verification.error = error.message;
    return verification;
  }
}
/**
 * Verify completion timing to detect suspicious behavior
 */
async function verifyCompletionTiming(sessionId, step, verificationData) {
  const minCompletionTime = 5000; // 5 seconds minimum (more forgiving)
  const maxCompletionTime = 1800000; // 30 minutes maximum (more forgiving)
  const startTime = verificationData.startTime || Date.now() - 30000; // Default fallback
  const completionTime = Date.now() - startTime;
  // Be more forgiving with timing - give partial scores for reasonable times
  let score = 0.0;
  let isValid = false;
  if (completionTime >= minCompletionTime && completionTime <= maxCompletionTime) {
    score = 1.0;
    isValid = true;
  } else if (completionTime > 0 && completionTime < minCompletionTime) {
    // Too fast, but give partial score (might be legitimate)
    score = 0.3;
    isValid = true;
  } else if (completionTime > maxCompletionTime) {
    // Too slow, but give partial score (user might have been distracted)
    score = 0.5;
    isValid = true;
  } else {
    // Invalid timing (negative or zero)
    score = 0.7; // Give benefit of doubt for timing issues
    isValid = true;
  }
  return {
    completionTime,
    isValid,
    score,
    details: { minTime: minCompletionTime, maxTime: maxCompletionTime, actual: completionTime }
  };
}
/**
 * Verify referrer to ensure user came from Lootlabs
 */
async function verifyReferrer(referrer) {
  const validReferrers = [
    'lootlabs.gg',
    'creators.lootlabs.gg',
    'gateway.lootlabs.gg'
  ];
  if (!referrer) {
    // Give partial score for missing referrer (common in some browsers/privacy settings)
    return { isValid: true, score: 0.5, reason: 'No referrer provided (privacy/browser setting)' };
  }
  const isValidReferrer = validReferrers.some(domain => referrer.includes(domain));
  return {
    isValid: true, // Always consider valid, just adjust score
    score: isValidReferrer ? 1.0 : 0.3, // Partial score for non-Lootlabs referrer
    referrer,
    validReferrers,
    reason: isValidReferrer ? 'Valid Lootlabs referrer' : 'Non-Lootlabs referrer (may be legitimate)'
  };
}
/**
 * Verify session state consistency
 */
async function verifySessionState(sessionId, step) {
  try {
    const { data: session, error } = await supabase
      .from('key_sessions')
      .select('step1, step2')
      .eq('session_id', sessionId)
      .single();
    if (error || !session) {
      return {
        isValid: false,
        score: 0.0,
        sessionId,
        step,
        reason: error ? error.message : 'Session not found'
      };
    }
    let isValid = false;
    let score = 0.0;
    let reason = '';
    if (step === 1) {
      // For step 1, we just need the session to exist
      isValid = true;
      score = 1.0;
      reason = 'Session exists';
    } else if (step === 2) {
      // For step 2, step 1 must be completed
      if (session.step1) {
        isValid = true;
        score = 1.0;
        reason = 'Previous step (1) completed';
      } else {
        isValid = false;
        score = 0.0;
        reason = 'Previous step (1) not completed';
      }
    } else {
      isValid = false;
      score = 0.0;
      reason = 'Invalid step number';
    }
    return {
      isValid,
      score,
      sessionId,
      step,
      reason
    };
  } catch (err) {
    return {
      isValid: false,
      score: 0.0,
      sessionId,
      step,
      reason: `Internal server error: ${err.message}`
    };
  }
}
/**
 * Verify user behavior patterns
 */
async function verifyUserBehavior(sessionId, verificationData) {
  const behaviorScore = calculateBehaviorScore(verificationData);
  return {
    isValid: behaviorScore >= 0.5,
    score: behaviorScore,
    factors: {
      mouseMovement: verificationData.mouseMovement || 0,
      keyboardActivity: verificationData.keyboardActivity || 0,
      scrollActivity: verificationData.scrollActivity || 0,
      focusEvents: verificationData.focusEvents || 0
    }
  };
}
/**
 * Calculate behavior score based on user interaction data
 */
function calculateBehaviorScore(data) {
  let score = 0;
  // Mouse movement indicates human behavior
  if (data.mouseMovement > 10) score += 0.3;
  // Keyboard activity
  if (data.keyboardActivity > 0) score += 0.2;
  // Scroll activity
  if (data.scrollActivity > 0) score += 0.2;
  // Focus events (tab switching, etc.)
  if (data.focusEvents > 0) score += 0.3;
  return Math.min(score, 1.0);
}
/**
 * Calculate overall verification score
 */
function calculateVerificationScore(details) {
  const weights = {
    timeVerification: 0.25,
    referrerVerification: 0.25,
    sessionVerification: 0.25,
    behaviorVerification: 0.25,
    encryptedUrlVerification: 0.3 // Bonus points if available, but not required
  };
  let totalScore = 0;
  let totalWeight = 0;
  // Calculate base score from core verifications (required)
  const coreVerifications = ['timeVerification', 'referrerVerification', 'sessionVerification', 'behaviorVerification'];
  coreVerifications.forEach(key => {
    if (details[key] && typeof details[key].score === 'number') {
      totalScore += details[key].score * weights[key];
      totalWeight += weights[key];
    }
  });
  // Add bonus from encrypted URL verification if available
  if (details.encryptedUrlVerification && typeof details.encryptedUrlVerification.score === 'number') {
    totalScore += details.encryptedUrlVerification.score * weights.encryptedUrlVerification;
    totalWeight += weights.encryptedUrlVerification;
  }
  // Ensure we have at least some core verifications
  if (totalWeight === 0) {
    return 0;
  }
  const finalScore = totalScore / totalWeight;
  return finalScore;
}
/**
 * Create encrypted URL using Lootlabs URL Encryptor
 * @param {string} url - URL to encrypt
 * @param {string} password - Password for encryption (optional)
 * @returns {Promise<string>} Encrypted URL
 */
export async function createEncryptedUrl(url, password = null) {
  if (!url) {
    throw new Error('URL is required for encryption');
  }
  const requestBody = {
    url,
    ...(password && { password })
  };
  try {
    const response = await fetch(`${LOOTLABS_BASE_URL}/url_encryptor`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${LOOTLABS_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });
    const data = await response.json();
    if (!response.ok) {
      throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
    }
    if (data.type === 'error') {
      throw new Error(data.message || 'Lootlabs URL encryption failed');
    }
    return data.message; // The encrypted URL
  } catch (error) {
    throw new Error(`Failed to encrypt URL: ${error.message}`);
  }
}
/**
 * Create enhanced Lootlabs links with dynamic configuration
 * @param {Object} options - Enhanced link creation options
 * @returns {Promise<Object>} Enhanced link creation result
 */
export async function createEnhancedLootlabsLinks(options) {
  const {
    campaignId,
    sessionId,
    useEncryption = false,
    encryptionPassword = null,
    customTheme = null,
    dynamicTier = false,
    userLocation = null,
    deviceType = null
  } = options;
  try {
    // Get campaign details with enhanced configuration
    const campaign = await getEnhancedCampaignConfig(campaignId, {
      userLocation,
      deviceType,
      dynamicTier
    });
    const baseReturnUrl = campaign.return_url;
    // Create step URLs
    const step1Url = `${baseReturnUrl}?step=1&session=${sessionId}`;
    const step2Url = `${baseReturnUrl}?step=2&session=${sessionId}`;
    let finalStep1Url = step1Url;
    let finalStep2Url = step2Url;
    // Apply URL encryption if requested
    if (useEncryption) {
      finalStep1Url = await createEncryptedUrl(step1Url, encryptionPassword);
      finalStep2Url = await createEncryptedUrl(step2Url, encryptionPassword);
    }
    // Create Step 1 link with enhanced options
    const step1Title = `${campaign.name} S1`.substring(0, 30);
    const step1Response = await createLootlabsLink({
      title: step1Title,
      url: finalStep1Url,
      tier_id: campaign.tier_id,
      number_of_tasks: campaign.number_of_tasks,
      theme: customTheme || campaign.theme,
      thumbnail: campaign.thumbnail
    });
    // Create Step 2 link with enhanced options
    const step2Title = `${campaign.name} S2`.substring(0, 30);
    const step2Response = await createLootlabsLink({
      title: step2Title,
      url: finalStep2Url,
      tier_id: campaign.tier_id,
      number_of_tasks: campaign.number_of_tasks,
      theme: customTheme || campaign.theme,
      thumbnail: campaign.thumbnail
    });
    return {
      success: true,
      step1_url: step1Response.message[0].loot_url,
      step2_url: step2Response.message[0].loot_url,
      step1_short_code: step1Response.message[0].short,
      step2_short_code: step2Response.message[0].short,
      campaign: {
        id: campaign.id,
        name: campaign.name,
        tier_id: campaign.tier_id,
        number_of_tasks: campaign.number_of_tasks,
        theme: customTheme || campaign.theme,
        enhanced: true
      },
      encryption: {
        enabled: useEncryption,
        step1_encrypted: useEncryption ? finalStep1Url : null,
        step2_encrypted: useEncryption ? finalStep2Url : null
      },
      analytics: {
        created_at: new Date().toISOString(),
        session_id: sessionId,
        user_location: userLocation,
        device_type: deviceType
      }
    };
  } catch (error) {
    throw new Error(`Enhanced link creation failed: ${error.message}`);
  }
}
/**
 * Get enhanced campaign configuration with dynamic adjustments
 */
async function getEnhancedCampaignConfig(campaignId, options = {}) {
  // This would typically fetch from your database
  // For now, return a mock enhanced configuration
  const baseCampaign = {
    id: campaignId,
    name: 'Enhanced Campaign',
    tier_id: 2,
    number_of_tasks: 2,
    theme: 3,
    return_url: process.env.SITE_URL || 'https://your-site.com/get-key',
    thumbnail: null
  };
  // Apply dynamic tier adjustment based on user location/device
  let adjustedTierId = baseCampaign.tier_id;
  let adjustedNumberOfTasks = baseCampaign.number_of_tasks;
  if (options.dynamicTier) {
    adjustedTierId = getDynamicTier(options.userLocation, options.deviceType);
    adjustedNumberOfTasks = getLocationBasedTasks(options.userLocation);
  }
  return {
    ...baseCampaign,
    tier_id: adjustedTierId,
    number_of_tasks: adjustedNumberOfTasks,
  };
}
function getDynamicTier(userLocation, deviceType) {
  // Simple logic for demonstration
  if (userLocation === 'US' && deviceType === 'desktop') return 3;
  if (deviceType === 'mobile') return 2;
  return 1;
}
function getLocationBasedTasks(userLocation) {
  // Simple logic for demonstration
  if (userLocation === 'US') return 2;
  return 1;
}
export function getLootlabsTiers() {
  return [
    { id: 1, name: 'MCPEDL Safe', description: 'Safe and quick ads.' },
    { id: 2, name: 'Gaming Offers', description: 'Targeted offers for gamers.' },
    { id: 3, name: 'Profit Max', description: 'Maximize revenue with diverse offers.' },
  ];
}
export function getLootlabsThemes() {
  return [
    { id: 1, name: 'Classic' },
    { id: 2, name: 'Sims' },
    { id: 3, name: 'Minecraft' },
    { id: 4, name: 'GTA' },
    { id: 5, name: 'Space' },
  ];
}
