import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Utility function to hash passwords
export const hashPassword = async (password) => {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
};

// Utility function to verify passwords
export const verifyPassword = async (password, hashedPassword) => {
  return await bcrypt.compare(password, hashedPassword);
};
export const validateAdminCredentials = async (headers) => {
  const username = headers['x-admin-username'] || headers.get?.('x-admin-username');
  const password = headers['x-admin-password'] || headers.get?.('x-admin-password');
  if (!username || !password) {
    return { error: 'Missing admin credentials', status: 401 };
  }
  try {
    const { data: adminUser, error: dbError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('username', username)
      .eq('is_active', true)
      .single();
    if (dbError || !adminUser) {
      return { error: 'Invalid admin credentials', status: 401 };
    }
    // Use bcrypt to verify password
    const isPasswordValid = await verifyPassword(password, adminUser.password_hash);
    if (isPasswordValid) {
      return {
        admin: {
          username: adminUser.username,
          role: adminUser.role,
          permissions: adminUser.permissions || {}
        }
      };
    }
    return { error: 'Invalid admin credentials', status: 401 };
  } catch (error) {
    return { error: 'Authentication failed', status: 500 };
  }
};
export const validateSessionToken = async (sessionToken) => {
  if (!sessionToken) {
    return { error: 'Missing session token', status: 401 };
  }
  try {
    let sessionData;
    try {
      sessionData = JSON.parse(Buffer.from(sessionToken, 'base64').toString());
    } catch (parseError) {
      return { error: 'Invalid token format', status: 401 };
    }
    if (!sessionData.username || !sessionData.timestamp) {
      return { error: 'Invalid token data', status: 401 };
    }
    const sessionAge = Date.now() - sessionData.timestamp;
    if (sessionAge > 24 * 60 * 60 * 1000) {
      return { error: 'Session expired', status: 401 };
    }
    const { data: adminUser, error: adminError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('username', sessionData.username)
      .eq('is_active', true)
      .single();
    if (adminError || !adminUser) {
      return { error: 'User not found or inactive', status: 401 };
    }
    return { 
      admin: { 
        username: adminUser.username, 
        role: adminUser.role, 
        permissions: adminUser.permissions || {} 
      } 
    };
  } catch (error) {
    return { error: 'Token validation failed', status: 500 };
  }
};
export const requireAdminAuth = async (headers, requiredRole = null) => {
  const sessionToken = headers['x-session-token'] || headers.get?.('x-session-token');
  let authResult;
  if (sessionToken) {
    authResult = await validateSessionToken(sessionToken);
  } else {
    authResult = await validateAdminCredentials(headers);
  }
  if (authResult.error) {
    return authResult;
  }
  if (requiredRole && authResult.admin.role !== requiredRole && authResult.admin.role !== 'owner') {
    return { error: 'Insufficient permissions', status: 403 };
  }
  return authResult;
};
export const createAuthResponse = (error, status = 401) => {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, x-admin-username, x-admin-password, x-session-token',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Credentials': 'true'
  };
  return {
    statusCode: status,
    headers: corsHeaders,
    body: JSON.stringify({ error })
  };
};
