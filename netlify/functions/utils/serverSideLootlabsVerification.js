import crypto from 'crypto';
import { createClient } from '@supabase/supabase-js';
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);
class ServerSideLootlabsVerifier {
  constructor() {
    this.apiKey = process.env.LOOTLABS_API_KEY;
    this.apiSecret = process.env.LOOTLABS_API_SECRET || process.env.LOOTLABS_API_KEY; // Fallback to API key if secret not set
    this.baseUrl = 'https://creators.lootlabs.gg/api/public';
    this.verificationCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    // Log configuration status (without exposing actual keys)
  }
  async verifyStepCompletion(sessionId, step, verificationData) {
    try {
      // Check for rapid successive attempts (bypass detection)
      const rapidAttemptCheck = await this.checkRapidAttempts(sessionId, step);
      if (!rapidAttemptCheck.isValid) {
        return {
          verified: false,
          score: 0,
          reason: 'Rapid successive attempts detected - likely bypass',
          details: rapidAttemptCheck
        };
      }

      // Multi-layer verification approach
      const verificationResults = await Promise.all([
        this.verifyWithLootlabsAPI(sessionId, step, verificationData),
        this.verifyReferrerChain(verificationData),
        this.verifyTimingPatterns(sessionId, step, verificationData),
        this.verifyUserBehavior(sessionId, verificationData),
        this.verifyDeviceConsistency(sessionId, verificationData)
      ]);
      const [apiResult, referrerResult, timingResult, behaviorResult, deviceResult] = verificationResults;
      // Calculate composite verification score
      const compositeScore = this.calculateCompositeScore(verificationResults);
      // Enhanced verification logic
      const isVerified = this.determineVerificationStatus(verificationResults, compositeScore);
      // Log verification attempt
      await this.logVerificationAttempt(sessionId, step, {
        apiResult,
        referrerResult,
        timingResult,
        behaviorResult,
        deviceResult,
        compositeScore,
        isVerified
      });
      return {
        verified: isVerified,
        score: compositeScore,
        details: {
          lootlabsAPI: apiResult,
          referrerChain: referrerResult,
          timingPatterns: timingResult,
          userBehavior: behaviorResult,
          deviceConsistency: deviceResult
        },
        confidence: this.calculateConfidence(verificationResults)
      };
    } catch (error) {
      // Log the error but don't block legitimate users
      await this.logVerificationError(sessionId, step, error);
      // Fallback to basic verification
      return this.fallbackVerification(sessionId, step, verificationData);
    }
  }
  async verifyWithLootlabsAPI(sessionId, step, verificationData) {
    try {
      // Skip API verification if credentials are not configured
      if (!this.apiKey || !this.apiSecret) {
        return {
          verified: true, // Don't fail verification due to missing config
          score: 0.5, // Neutral score
          reason: 'API verification skipped - credentials not configured',
          apiResponse: null
        };
      }
      // Check cache first
      const cacheKey = `${sessionId}-${step}`;
      const cached = this.verificationCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.result;
      }
      // Prepare API request
      const requestData = {
        session_id: sessionId,
        step: step,
        data_parameter: verificationData.dataParameter,
        referrer: verificationData.referrer,
        user_agent: verificationData.userAgent,
        timestamp: Date.now()
      };
      // Create signature for API request
      const signature = this.createAPISignature(requestData);
      const response = await fetch(`${this.baseUrl}/verify_completion`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey,
          'X-Signature': signature,
          'User-Agent': 'Project-Madara-Server/1.0'
        },
        body: JSON.stringify(requestData)
      });
      if (!response.ok) {
        throw new Error(`Lootlabs API error: ${response.status}`);
      }
      const result = await response.json();
      // Cache the result
      this.verificationCache.set(cacheKey, {
        result: {
          verified: result.verified || false,
          score: result.score || 0,
          reason: result.reason || 'API verification'
        },
        timestamp: Date.now()
      });
      return {
        verified: result.verified || false,
        score: result.score || 0,
        reason: result.reason || 'API verification',
        apiResponse: true
      };
    } catch (error) {
      return {
        verified: false,
        score: 0,
        reason: 'API verification failed',
        error: error.message
      };
    }
  }
  async verifyReferrerChain(verificationData) {
    try {
      const referrer = verificationData.referrer;
      const expectedCallbackUrl = verificationData.expectedCallbackUrl;
      // Check if referrer matches expected Lootlabs domain
      const lootlabsDomains = [
        'lootlabs.gg',
        'creators.lootlabs.gg',
        'api.lootlabs.gg',
        'loot-link.com',  
        'loot-links.com',
        'lootlink.org', 
        'lootlinks.co',
        'lootdest.com' 
      ];
      let referrerValid = false;
      if (referrer) {
        try {
          const referrerUrl = new URL(referrer);
          referrerValid = lootlabsDomains.some(domain =>
            referrerUrl.hostname.includes(domain)
          );
        } catch (e) {
          referrerValid = false;
        }
      } else {
        // No referrer is now a major red flag for bypass attempts
      }
      // Verify callback URL structure
      let callbackValid = false;
      if (expectedCallbackUrl) {
        try {
          const callbackUrl = new URL(expectedCallbackUrl);
          callbackValid = callbackUrl.pathname.includes('/generate-key') &&
                         callbackUrl.searchParams.has('step') &&
                         callbackUrl.searchParams.has('session');
        } catch (e) {
          callbackValid = false;
        }
      }

      // More reasonable scoring - prioritize valid referrer
      let score = 0.5; // Base score
      if (referrerValid) score += 0.4; // Valid referrer is most important
      if (callbackValid) score += 0.1; // Callback is nice to have

      return {
        verified: referrerValid, // Only require valid referrer
        score,
        reason: 'Referrer chain verification',
        details: {
          referrerValid,
          callbackValid,
          referrer: referrer ? new URL(referrer).hostname : null
        }
      };
    } catch (error) {
      return {
        verified: false,
        score: 0,
        reason: 'Referrer verification failed',
        error: error.message
      };
    }
  }
  async verifyTimingPatterns(sessionId, step, verificationData) {
    try {
      const now = Date.now();
      const startTime = verificationData.startTime;
      const completionTime = startTime ? now - startTime : 0;
      // Get session creation time from database
      const { data: sessionData } = await supabase
        .from('key_sessions')
        .select('created_at')
        .eq('session_id', sessionId)
        .single();
      let sessionAge = 0;
      if (sessionData) {
        sessionAge = now - new Date(sessionData.created_at).getTime();
      }
      // More reasonable timing thresholds for legitimate users
      const minCompletionTime = 10000; // 10 seconds minimum (reasonable for clicking and completing)
      const maxCompletionTime = 600000; // 10 minutes maximum
      const maxSessionAge = 3600000; // 1 hour maximum
      let score = 1.0;
      let reasons = [];
      // Check completion time - more reasonable penalties
      if (completionTime < minCompletionTime) {
        score -= 0.4; // Reduced penalty for legitimate fast users
        reasons.push('Very fast completion');
      } else if (completionTime > maxCompletionTime) {
        score -= 0.2; // Reduced penalty
        reasons.push('Slow completion');
      }
      // Check session age
      if (sessionAge > maxSessionAge) {
        score -= 0.3;
        reasons.push('Session too old');
      }
      // Check for step sequence
      if (step === 2) {
        const { data: step1Data } = await supabase
          .from('key_sessions')
          .select('step1')
          .eq('session_id', sessionId)
          .single();
        if (!step1Data?.step1) {
          score -= 0.5;
          reasons.push('Step 1 not completed');
        }
      }
      return {
        verified: score >= 0.4, // More reasonable threshold
        score: Math.max(score, 0),
        reason: 'Timing pattern verification',
        details: {
          completionTime,
          sessionAge,
          reasons
        }
      };
    } catch (error) {
      return {
        verified: false,
        score: 0,
        reason: 'Timing verification failed',
        error: error.message
      };
    }
  }
  async verifyUserBehavior(sessionId, verificationData) {
    try {
      const behaviorData = {
        mouseMovement: verificationData.mouseMovement || 0,
        keyboardActivity: verificationData.keyboardActivity || 0,
        scrollActivity: verificationData.scrollActivity || 0,
        focusEvents: verificationData.focusEvents || 0
      };
      let score = 0;
      let humanLikeIndicators = 0;
      // More lenient behavior verification since we don't collect detailed behavior data
      // Give base score for legitimate users
      score = 0.6; // Base score for users who complete tasks

      // Check for human-like behavior patterns (bonus points)
      if (behaviorData.mouseMovement > 10) {
        score += 0.2;
        humanLikeIndicators++;
      }
      if (behaviorData.keyboardActivity > 0) {
        score += 0.1;
        humanLikeIndicators++;
      }
      if (behaviorData.scrollActivity > 0) {
        score += 0.1;
        humanLikeIndicators++;
      }
      if (behaviorData.focusEvents > 0 && behaviorData.focusEvents < 10) {
        score += 0.1;
        humanLikeIndicators++;
      }
      return {
        verified: score >= 0.5, // More reasonable threshold
        score: Math.min(score, 1.0), // Cap at 1.0
        reason: 'User behavior verification',
        details: {
          behaviorData,
          humanLikeIndicators
        }
      };
    } catch (error) {
      return {
        verified: false,
        score: 0,
        reason: 'Behavior verification failed',
        error: error.message
      };
    }
  }
  async verifyDeviceConsistency(sessionId, verificationData) {
    try {
      // Get stored device fingerprint from session
      const { data: sessionData } = await supabase
        .from('key_sessions')
        .select('user_agent')
        .eq('session_id', sessionId)
        .single();
      if (!sessionData) {
        return {
          verified: false,
          score: 0,
          reason: 'Session not found'
        };
      }
      const storedUserAgent = sessionData.user_agent;
      const currentUserAgent = verificationData.userAgent;
      // Check user agent consistency
      const userAgentMatch = storedUserAgent === currentUserAgent;
      let score = userAgentMatch ? 1.0 : 0.3;
      return {
        verified: score >= 0.5,
        score,
        reason: 'Device consistency verification',
        details: {
          userAgentMatch,
          storedUA: storedUserAgent ? storedUserAgent.substring(0, 50) + '...' : null,
          currentUA: currentUserAgent ? currentUserAgent.substring(0, 50) + '...' : null
        }
      };
    } catch (error) {
      return {
        verified: false,
        score: 0,
        reason: 'Device verification failed',
        error: error.message
      };
    }
  }
  calculateCompositeScore(verificationResults) {
    const weights = {
      api: 0.3,
      referrer: 0.2,
      timing: 0.2,
      behavior: 0.15,
      device: 0.15
    };
    if (!verificationResults || verificationResults.length < 5) {
      return 0;
    }
    const [apiResult, referrerResult, timingResult, behaviorResult, deviceResult] = verificationResults;
    // Ensure all scores are valid numbers
    const safeScore = (result, defaultScore = 0) => {
      const score = result && typeof result.score === 'number' ? result.score : defaultScore;
      return isNaN(score) ? defaultScore : score;
    };
    const compositeScore = (
      (safeScore(apiResult) * weights.api) +
      (safeScore(referrerResult) * weights.referrer) +
      (safeScore(timingResult) * weights.timing) +
      (safeScore(behaviorResult) * weights.behavior) +
      (safeScore(deviceResult) * weights.device)
    );
    // Ensure we never return NaN
    return isNaN(compositeScore) ? 0 : compositeScore;
  }
  determineVerificationStatus(verificationResults, compositeScore) {
    const [apiResult, referrerResult, timingResult, behaviorResult, deviceResult] = verificationResults;
    // Skip API verification if API secret is not configured (graceful degradation)
    if (apiResult.apiResponse && !apiResult.verified && this.apiSecret) {
      return false;
    }
    // Must have reasonable timing - more lenient for legitimate users
    if (timingResult.score < 0.3) {
      return false;
    }
    // More reasonable composite score threshold for legitimate users
    const threshold = 0.5; // Reduced to 50% to allow legitimate users
    const isVerified = compositeScore >= threshold;
    return isVerified;
  }
  calculateConfidence(verificationResults) {
    if (!verificationResults || verificationResults.length === 0) {
      return 0;
    }
    const successfulVerifications = verificationResults.filter(result => result && result.verified).length;
    const confidence = successfulVerifications / verificationResults.length;
    // Ensure we never return NaN
    return isNaN(confidence) ? 0 : confidence;
  }
  createAPISignature(data) {
    const payload = JSON.stringify(data);
    return crypto
      .createHmac('sha256', this.apiSecret)
      .update(payload)
      .digest('hex');
  }
  async logVerificationAttempt(sessionId, step, verificationData) {
    try {
      await supabase
        .from('lootlabs_security_violations')
        .insert({
          session_id: sessionId,
          violation_type: 'verification_attempt',
          severity: 'low',
          details: {
            step,
            verificationData,
            timestamp: new Date().toISOString()
          }
        });
    } catch (error) {
    }
  }
  async checkRapidAttempts(sessionId, step) {
    try {
      // Check for multiple verification attempts in the last 2 minutes
      const twoMinutesAgo = new Date(Date.now() - 2 * 60 * 1000).toISOString();

      const { data: recentAttempts, error } = await supabase
        .from('key_usage_logs')
        .select('created_at')
        .eq('action', 'lootlabs_step_completion')
        .gte('created_at', twoMinutesAgo)
        .like('details', `%"sessionId":"${sessionId}"%`)
        .like('details', `%"step":${step}%`);

      if (error) {
        // If we can't check, allow it but log the issue
        return { isValid: true, reason: 'Could not check rapid attempts' };
      }

      // Allow max 3 attempts per 2 minutes
      if (recentAttempts && recentAttempts.length >= 3) {
        return {
          isValid: false,
          reason: 'Too many verification attempts in short time',
          attemptCount: recentAttempts.length,
          timeWindow: '2 minutes'
        };
      }

      return { isValid: true, attemptCount: recentAttempts?.length || 0 };
    } catch (error) {
      // If check fails, allow it but log
      return { isValid: true, reason: 'Rapid attempt check failed', error: error.message };
    }
  }

  async logVerificationError(sessionId, step, error) {
    try {
      await supabase
        .from('lootlabs_security_violations')
        .insert({
          session_id: sessionId,
          violation_type: 'verification_error',
          severity: 'medium',
          details: {
            step,
            error: error.message,
            timestamp: new Date().toISOString()
          }
        });
    } catch (logError) {
    }
  }
  fallbackVerification(sessionId, step, verificationData) {
    // More reasonable fallback verification for legitimate users
    const hasReferrer = !!verificationData.referrer;
    const hasValidReferrer = verificationData.referrer &&
      (verificationData.referrer.includes('lootlabs') || verificationData.referrer.includes('lootdest'));
    const hasReasonableTiming = verificationData.startTime &&
      (Date.now() - verificationData.startTime) > 5000; // Reduced to 5s for legitimate users

    // More lenient - valid referrer OR reasonable timing
    const isVerified = hasValidReferrer || hasReasonableTiming;

    return {
      verified: isVerified,
      score: isVerified ? 0.6 : 0.2, // More reasonable scores
      reason: 'Fallback verification',
      fallback: true,
      details: {
        hasReferrer,
        hasValidReferrer,
        hasReasonableTiming,
        referrer: verificationData.referrer
      }
    };
  }
}
// Global instance
export const serverSideLootlabsVerifier = new ServerSideLootlabsVerifier();
// Export main verification function
export const verifyLootlabsCompletion = (sessionId, step, verificationData) => {
  return serverSideLootlabsVerifier.verifyStepCompletion(sessionId, step, verificationData);
};
