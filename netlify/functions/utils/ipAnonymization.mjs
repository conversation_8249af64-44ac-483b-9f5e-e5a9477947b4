/**
 * Server-side IP Address Anonymization Utility (ES Module)
 *
 * Provides GDPR-compliant IP address anonymization using salted SHA-256 hashing.
 * Maintains security functionality while protecting user privacy.
 */

import crypto from 'crypto';

class IPAnonymizer {
  constructor() {
    // Use environment variable with secure fallback
    this.salt = process.env.IP_ANONYMIZATION_SALT || 'MADARA-IP-SALT-2024-SECURE-SERVER';

    // Cache for performance optimization
    this.hashCache = new Map();
    this.maxCacheSize = 1000;

    // Validation patterns
    this.ipv4Pattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    this.ipv6Pattern = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;

    // Performance tracking
    this.cacheHits = 0;
    this.cacheMisses = 0;
  }

  /**
   * Extract IP address from request headers (Netlify/serverless environment)
   * @param {object} headers - Request headers
   * @returns {string} - Extracted IP address
   */
  extractIPFromHeaders(headers) {
    // Try various header sources in order of preference
    const ipSources = [
      headers['client-ip'],
      headers['x-forwarded-for'],
      headers['x-real-ip'],
      headers['x-client-ip'],
      headers['cf-connecting-ip'], // Cloudflare
      headers['x-forwarded'],
      headers['forwarded-for'],
      headers['forwarded']
    ];

    for (const source of ipSources) {
      if (source) {
        // Handle comma-separated IPs (x-forwarded-for can have multiple)
        const ip = source.split(',')[0].trim();
        if (this.isValidIP(ip)) {
          return ip;
        }
      }
    }

    return 'unknown';
  }

  /**
   * Anonymize an IP address using salted SHA-256 hashing
   * @param {string} ipAddress - The IP address to anonymize
   * @param {boolean} useCache - Whether to use caching (default: true)
   * @returns {string} - Anonymized IP hash or 'unknown' for invalid inputs
   */
  anonymizeIP(ipAddress, useCache = true) {
    // Handle edge cases
    if (!ipAddress || ipAddress === 'unknown' || ipAddress === 'localhost' || ipAddress === '127.0.0.1') {
      return 'unknown';
    }

    // Normalize IP address
    const normalizedIP = this.normalizeIP(ipAddress);
    if (!normalizedIP) {
      return 'unknown';
    }

    // Check cache first
    if (useCache && this.hashCache.has(normalizedIP)) {
      this.cacheHits++;
      return this.hashCache.get(normalizedIP);
    }

    this.cacheMisses++;

    // Create salted hash using Node.js crypto
    const saltedIP = normalizedIP + this.salt;
    const hash = crypto.createHash('sha256').update(saltedIP).digest('hex');

    // Cache the result
    if (useCache) {
      this.addToCache(normalizedIP, hash);
    }

    return hash;
  }

  /**
   * Process request and return anonymized IP
   * @param {object} event - Netlify function event
   * @returns {string} - Anonymized IP address
   */
  processRequest(event) {
    const rawIP = this.extractIPFromHeaders(event.headers || {});
    return this.anonymizeIP(rawIP);
  }

  /**
   * Validate if a string is a valid IP address
   * @param {string} ipAddress - IP address to validate
   * @returns {boolean} - Whether the IP is valid
   */
  isValidIP(ipAddress) {
    if (!ipAddress || typeof ipAddress !== 'string') {
      return false;
    }
    return this.isIPv4(ipAddress) || this.isIPv6(ipAddress);
  }

  /**
   * Check if IP is IPv4
   * @param {string} ip - IP address
   * @returns {boolean}
   */
  isIPv4(ip) {
    return this.ipv4Pattern.test(ip);
  }

  /**
   * Check if IP is IPv6
   * @param {string} ip - IP address
   * @returns {boolean}
   */
  isIPv6(ip) {
    return this.ipv6Pattern.test(ip);
  }

  /**
   * Normalize IP address (trim, lowercase, etc.)
   * @param {string} ipAddress - Raw IP address
   * @returns {string|null} - Normalized IP or null if invalid
   */
  normalizeIP(ipAddress) {
    if (!ipAddress || typeof ipAddress !== 'string') {
      return null;
    }

    const trimmed = ipAddress.trim().toLowerCase();

    // Handle IPv6 shorthand and IPv4-mapped IPv6
    if (trimmed.includes('::ffff:') && this.isIPv4(trimmed.split('::ffff:')[1])) {
      // IPv4-mapped IPv6, extract IPv4 part
      return trimmed.split('::ffff:')[1];
    }

    // Validate the IP
    if (!this.isValidIP(trimmed)) {
      return null;
    }

    return trimmed;
  }

  /**
   * Add hash to cache with size management
   * @param {string} ip - Original IP
   * @param {string} hash - Computed hash
   */
  addToCache(ip, hash) {
    // Manage cache size
    if (this.hashCache.size >= this.maxCacheSize) {
      // Remove oldest entries (simple FIFO)
      const firstKey = this.hashCache.keys().next().value;
      this.hashCache.delete(firstKey);
    }

    this.hashCache.set(ip, hash);
  }

  /**
   * Create a display-friendly version of anonymized IP
   * @param {string} hash - Anonymized IP hash
   * @param {boolean} truncate - Whether to truncate for display
   * @returns {string} - Display-friendly hash
   */
  formatHashForDisplay(hash, truncate = true) {
    if (!hash || hash === 'unknown') {
      return 'Unknown';
    }

    if (truncate) {
      // Show first 8 and last 4 characters with ellipsis
      return `${hash.substring(0, 8)}...${hash.substring(hash.length - 4)}`;
    }

    return hash;
  }
}

// Create singleton instance
const ipAnonymizer = new IPAnonymizer();

// Export both the class and singleton instance
export {
  IPAnonymizer,
  ipAnonymizer
};

// Convenience functions
export const anonymizeIP = (ip) => ipAnonymizer.anonymizeIP(ip);
export const processRequest = (event) => ipAnonymizer.processRequest(event);
export const extractIPFromHeaders = (headers) => ipAnonymizer.extractIPFromHeaders(headers);
export const isValidIP = (ip) => ipAnonymizer.isValidIP(ip);
export const formatHashForDisplay = (hash, truncate) => ipAnonymizer.formatHashForDisplay(hash, truncate);
