import { createClient } from '@supabase/supabase-js';
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);
class RateLimiter {
  constructor() {
    this.limits = {
      // Standard rate limits
      api_general: { requests: 100, window: 60 * 1000 }, // 100 requests per minute
      key_generation: { requests: 3, window: 60 * 1000 }, // 3 key generations per minute
      step_completion: { requests: 10, window: 60 * 1000 }, // 10 step completions per minute
      // Strict limits for suspicious behavior
      suspicious_high: { requests: 5, window: 60 * 1000 }, // 5 requests per minute for high-risk IPs
      suspicious_medium: { requests: 15, window: 60 * 1000 }, // 15 requests per minute for medium-risk IPs
      // CAPTCHA triggers
      captcha_trigger: { requests: 20, window: 60 * 1000 }, // Trigger CAPTCHA after 20 requests
      captcha_required: { requests: 5, window: 60 * 1000 }, // Require CAPTCHA for every 5 requests
    };
    this.cleanupInterval = 5 * 60 * 1000; // 5 minutes
    setInterval(() => this.cleanupExpiredEntries(), this.cleanupInterval);
  }
  async checkRateLimit(ipAddress, endpoint, userAgent = '', additionalData = {}) {
    const now = Date.now();
    const limitKey = this.getLimitKey(endpoint, additionalData);
    const limit = this.limits[limitKey];
    if (!limit) {
      return { allowed: true, remaining: Infinity };
    }
    try {
      // Get recent requests from database
      const windowStart = new Date(now - limit.window);
      const { data: recentRequests, error } = await supabase
        .from('rate_limit_entries')
        .select('*')
        .eq('ip_address', ipAddress)
        .eq('endpoint', endpoint)
        .gte('timestamp', windowStart.toISOString())
        .order('timestamp', { ascending: false });
      if (error) {
        return { allowed: true, remaining: limit.requests }; // Fail open
      }
      const requestCount = recentRequests ? recentRequests.length : 0;
      const remaining = Math.max(0, limit.requests - requestCount);
      if (requestCount >= limit.requests) {
        // Rate limit exceeded
        await this.logRateLimitViolation(ipAddress, endpoint, userAgent, requestCount, limit);
        return {
          allowed: false,
          remaining: 0,
          resetTime: now + limit.window,
          retryAfter: Math.ceil(limit.window / 1000)
        };
      }
      // Log this request
      await this.logRequest(ipAddress, endpoint, userAgent, additionalData);
      return {
        allowed: true,
        remaining: remaining - 1, // Account for current request
        resetTime: now + limit.window
      };
    } catch (error) {
      return { allowed: true, remaining: limit.requests }; // Fail open
    }
  }
  getLimitKey(endpoint, additionalData = {}) {
    // Determine which rate limit to apply based on endpoint and risk level
    const riskLevel = additionalData.riskLevel || 'normal';
    if (riskLevel === 'high') {
      return 'suspicious_high';
    } else if (riskLevel === 'medium') {
      return 'suspicious_medium';
    }
    // Endpoint-specific limits
    switch (endpoint) {
      case 'generate-key':
        return 'key_generation';
      case 'step-completion':
        return 'step_completion';
      default:
        return 'api_general';
    }
  }
  async logRequest(ipAddress, endpoint, userAgent, additionalData) {
    try {
      await supabase
        .from('rate_limit_entries')
        .insert({
          ip_address: ipAddress,
          endpoint: endpoint,
          user_agent: userAgent,
          additional_data: additionalData,
          timestamp: new Date().toISOString()
        });
    } catch (error) {
    }
  }
  async logRateLimitViolation(ipAddress, endpoint, userAgent, requestCount, limit) {
    try {
      await supabase
        .from('security_violations')
        .insert({
          violation_type: 'rate_limit_exceeded',
          violation_details: {
            endpoint,
            requestCount,
            limit: limit.requests,
            window: limit.window
          },
          severity: 'medium',
          ip_address: ipAddress,
          user_agent: userAgent,
          timestamp: new Date().toISOString()
        });
    } catch (error) {
    }
  }
  async shouldRequireCaptcha(ipAddress, endpoint) {
    try {
      const now = Date.now();
      const windowStart = new Date(now - this.limits.captcha_trigger.window);
      const { data: recentRequests, error } = await supabase
        .from('rate_limit_entries')
        .select('*')
        .eq('ip_address', ipAddress)
        .gte('timestamp', windowStart.toISOString());
      if (error) {
        return false;
      }
      const requestCount = recentRequests ? recentRequests.length : 0;
      return requestCount >= this.limits.captcha_trigger.requests;
    } catch (error) {
      return false;
    }
  }
  async cleanupExpiredEntries() {
    try {
      const cutoffTime = new Date(Date.now() - (24 * 60 * 60 * 1000)); // 24 hours ago
      const { error } = await supabase
        .from('rate_limit_entries')
        .delete()
        .lt('timestamp', cutoffTime.toISOString());
      if (error) {
      }
    } catch (error) {
    }
  }
  async getRateLimitStatus(ipAddress, endpoint) {
    try {
      const now = Date.now();
      const limitKey = this.getLimitKey(endpoint);
      const limit = this.limits[limitKey];
      if (!limit) {
        return { status: 'no_limit' };
      }
      const windowStart = new Date(now - limit.window);
      const { data: recentRequests, error } = await supabase
        .from('rate_limit_entries')
        .select('*')
        .eq('ip_address', ipAddress)
        .eq('endpoint', endpoint)
        .gte('timestamp', windowStart.toISOString());
      if (error) {
        return { status: 'error', error: error.message };
      }
      const requestCount = recentRequests ? recentRequests.length : 0;
      const remaining = Math.max(0, limit.requests - requestCount);
      return {
        status: 'active',
        requests: requestCount,
        limit: limit.requests,
        remaining,
        window: limit.window,
        resetTime: now + limit.window
      };
    } catch (error) {
      return { status: 'error', error: error.message };
    }
  }
  async adjustLimitsForRisk(ipAddress, riskScore) {
    // Dynamically adjust rate limits based on risk assessment
    if (riskScore > 0.8) {
      return 'suspicious_high';
    } else if (riskScore > 0.5) {
      return 'suspicious_medium';
    }
    return 'normal';
  }
}
// CAPTCHA verification system
class CaptchaManager {
  constructor() {
    this.pendingChallenges = new Map();
    this.challengeTimeout = 5 * 60 * 1000; // 5 minutes
  }
  generateChallenge(ipAddress) {
    const challengeId = this.generateChallengeId();
    const challenge = this.createMathChallenge();
    this.pendingChallenges.set(challengeId, {
      ipAddress,
      challenge,
      answer: challenge.answer,
      timestamp: Date.now(),
      attempts: 0
    });
    // Auto-cleanup after timeout
    setTimeout(() => {
      this.pendingChallenges.delete(challengeId);
    }, this.challengeTimeout);
    return {
      challengeId,
      question: challenge.question,
      type: challenge.type
    };
  }
  createMathChallenge() {
    const operations = ['+', '-', '*'];
    const operation = operations[Math.floor(Math.random() * operations.length)];
    let num1, num2, answer;
    switch (operation) {
      case '+':
        num1 = Math.floor(Math.random() * 50) + 1;
        num2 = Math.floor(Math.random() * 50) + 1;
        answer = num1 + num2;
        break;
      case '-':
        num1 = Math.floor(Math.random() * 50) + 25;
        num2 = Math.floor(Math.random() * 25) + 1;
        answer = num1 - num2;
        break;
      case '*':
        num1 = Math.floor(Math.random() * 12) + 1;
        num2 = Math.floor(Math.random() * 12) + 1;
        answer = num1 * num2;
        break;
    }
    return {
      type: 'math',
      question: `What is ${num1} ${operation} ${num2}?`,
      answer: answer.toString()
    };
  }
  generateChallengeId() {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }
  verifyChallenge(challengeId, userAnswer, ipAddress) {
    const challenge = this.pendingChallenges.get(challengeId);
    if (!challenge) {
      return { success: false, reason: 'Challenge not found or expired' };
    }
    if (challenge.ipAddress !== ipAddress) {
      return { success: false, reason: 'IP address mismatch' };
    }
    if (Date.now() - challenge.timestamp > this.challengeTimeout) {
      this.pendingChallenges.delete(challengeId);
      return { success: false, reason: 'Challenge expired' };
    }
    challenge.attempts++;
    if (challenge.attempts > 3) {
      this.pendingChallenges.delete(challengeId);
      return { success: false, reason: 'Too many attempts' };
    }
    if (userAnswer.toString().trim() === challenge.answer) {
      this.pendingChallenges.delete(challengeId);
      return { success: true };
    }
    return { success: false, reason: 'Incorrect answer', attemptsRemaining: 3 - challenge.attempts };
  }
}
// Global instances
export const rateLimiter = new RateLimiter();
export const captchaManager = new CaptchaManager();
// Convenience functions
export const checkRateLimit = (ipAddress, endpoint, userAgent, additionalData) => {
  return rateLimiter.checkRateLimit(ipAddress, endpoint, userAgent, additionalData);
};
export const shouldRequireCaptcha = (ipAddress, endpoint) => {
  return rateLimiter.shouldRequireCaptcha(ipAddress, endpoint);
};
export const generateCaptcha = (ipAddress) => {
  return captchaManager.generateChallenge(ipAddress);
};
export const verifyCaptcha = (challengeId, userAnswer, ipAddress) => {
  return captchaManager.verifyChallenge(challengeId, userAnswer, ipAddress);
};
