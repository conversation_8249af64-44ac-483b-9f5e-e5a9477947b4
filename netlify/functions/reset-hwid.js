import { createClient } from '@supabase/supabase-js';
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
if (!supabaseUrl || !supabaseServiceRoleKey) {
  throw new Error('Missing Supabase environment variables');
}
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Credentials': 'true'
};
export const handler = async (event, context) => {
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers: corsHeaders, body: '' };
  }
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }
  try {
    const { key_id } = JSON.parse(event.body);
    const userAgent = event.headers['user-agent'] || '';
    const ipAddress = event.headers['x-forwarded-for']?.split(',')[0]?.trim() || 'unknown';
    if (!key_id) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Key ID required' })
      };
    }
    // Get the key and verify ownership
    const { data: keyData, error: keyError } = await supabase
      .from('license_keys')
      .select('*')
      .eq('key_code', key_id)
      .eq('ip_address', ipAddress)
      .single();
    if (keyError || !keyData) {
      return {
        statusCode: 404,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Key not found or access denied' })
      };
    }
    // Check if key is active and not expired
    if (!keyData.is_active) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Cannot reset HWID for inactive key' })
      };
    }
    const now = new Date();
    const expiresAt = new Date(keyData.expires_at);
    if (expiresAt < now) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Cannot reset HWID for expired key' })
      };
    }
    // Check 24-hour cooldown
    if (keyData.last_hwid_reset) {
      const lastReset = new Date(keyData.last_hwid_reset);
      const timeDiff = now - lastReset;
      const hoursRemaining = 24 - (timeDiff / (1000 * 60 * 60));
      if (hoursRemaining > 0) {
        const hours = Math.floor(hoursRemaining);
        const minutes = Math.floor((hoursRemaining % 1) * 60);
        const timeRemaining = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
        return {
          statusCode: 429,
          headers: corsHeaders,
          body: JSON.stringify({ 
            error: `HWID reset is on cooldown. Try again in ${timeRemaining}`,
            timeRemaining: timeRemaining,
            hoursRemaining: hoursRemaining
          })
        };
      }
    }
    // Reset the HWID and Roblox HWID
    const { error: updateError } = await supabase
      .from('license_keys')
      .update({
        fingerprint_hash: null, // Reset HWID to null (Not Set)
        roblox_hwid: null, // Reset Roblox HWID to null (Not Set)
        last_hwid_reset: now.toISOString()
      })
      .eq('key_code', key_id);
    if (updateError) {
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Failed to reset HWID' })
      };
    }

    // Also reset any existing HWID bindings for this key
    await supabase
      .from('hwid_bindings')
      .update({
        is_active: false // Deactivate existing bindings instead of deleting
      })
      .eq('key_id', keyData.id);
    // Log the HWID reset action
    await supabase.from('user_analytics').insert({
      event_type: 'hwid_reset',
      event_data: {
        key_id: key_id,
        previous_hwid: keyData.fingerprint_hash,
        previous_roblox_hwid: keyData.roblox_hwid,
        reset_timestamp: now.toISOString()
      },
      occurred_at: now.toISOString(),
      ip_address: ipAddress,
      user_agent: userAgent
    });
    // Log usage for monitoring
    await supabase.from('usage_logs').insert({
      endpoint: 'reset-hwid',
      ip_address: ipAddress,
      user_agent: userAgent,
      success: true,
      timestamp: now.toISOString(),
      details: {
        key_id: key_id,
        action: 'hwid_reset'
      }
    });
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        message: 'HWID and Roblox HWID reset successfully',
        next_reset_available: new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString()
      })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
