import { createClient } from '@supabase/supabase-js';
import { processRequest, anonymizeIP, formatHashForDisplay } from './utils/ipAnonymization.mjs';
// Enhanced security and rate limiting
const rateLimitStore = new Map();
const sessionStore = new Map();

// Security configuration
const SECURITY_CONFIG = {
  MAX_ATTEMPTS_PER_IP: 10,
  RATE_LIMIT_WINDOW: 5 * 60 * 1000, // 5 minutes
  MAX_ATTEMPTS_PER_HWID: 10,
  HWID_COOLDOWN: 10 * 60 * 1000, // 10 minutes
  SESSION_TIMEOUT: 60 * 60 * 1000, // 1 hour
  TIMESTAMP_TOLERANCE: 5 * 60 * 1000 // 5 minutes
};

// Security validation functions
const validateKeyFormat = (key) => {
  if (!key || typeof key !== 'string') return false;
  const cleanKey = key.replace(/\s+/g, '');
  if (cleanKey.length !== 21) return false;
  if (!cleanKey.startsWith('MADARA-')) return false;

  const segments = [
    cleanKey.substring(7, 11),
    cleanKey.substring(12, 16),
    cleanKey.substring(17, 21)
  ];

  return segments.every(segment =>
    segment.length === 4 && /^[A-Za-z0-9]+$/.test(segment)
  );
};

const validateHWID = (hwid) => {
  if (!hwid || typeof hwid !== 'string') return false;
  if (hwid.length < 20 || hwid.length > 200) return false;
  return /^[A-Za-z0-9_-]+$/.test(hwid);
};

const generateSecureToken = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let token = '';
  for (let i = 0; i < 32; i++) {
    token += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return token + '_' + Date.now();
};

const checkRateLimit = (clientIP, hwid) => {
  const currentTime = Date.now();

  // Check IP rate limit
  const ipKey = `ip_${clientIP}`;
  const ipData = rateLimitStore.get(ipKey) || { attempts: 0, windowStart: currentTime };

  if (currentTime - ipData.windowStart > SECURITY_CONFIG.RATE_LIMIT_WINDOW) {
    ipData.attempts = 0;
    ipData.windowStart = currentTime;
  }

  if (ipData.attempts >= SECURITY_CONFIG.MAX_ATTEMPTS_PER_IP) {
    return { allowed: false, reason: 'Rate limit exceeded' };
  }

  // Check HWID rate limit
  const hwidKey = `hwid_${hwid}`;
  const hwidData = rateLimitStore.get(hwidKey) || { attempts: 0, lastAttempt: 0 };

  if (currentTime - hwidData.lastAttempt < SECURITY_CONFIG.HWID_COOLDOWN &&
      hwidData.attempts >= SECURITY_CONFIG.MAX_ATTEMPTS_PER_HWID) {
    return { allowed: false, reason: 'Too many attempts from this device' };
  }

  if (currentTime - hwidData.lastAttempt > SECURITY_CONFIG.HWID_COOLDOWN) {
    hwidData.attempts = 0;
  }

  // Update counters
  ipData.attempts++;
  hwidData.attempts++;
  hwidData.lastAttempt = currentTime;

  rateLimitStore.set(ipKey, ipData);
  rateLimitStore.set(hwidKey, hwidData);

  return { allowed: true };
};

const allowCors = (handler) => {
  return async (request, context) => {
    // Roblox-optimized headers
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept, Origin, User-Agent',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Credentials': 'false',
      'Access-Control-Max-Age': '86400',
      'Content-Type': 'application/json; charset=utf-8',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY'
    };

    if (request.method === 'OPTIONS') {
      return new Response('', { status: 200, headers });
    }

    try {
      // Add timeout for Roblox compatibility (max 25 seconds to stay under Roblox's 30s limit)
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), 25000);
      });

      const result = await Promise.race([
        handler(request, context, headers),
        timeoutPromise
      ]);

      return result;
    } catch (error) {
      console.error('Validate-key error:', error);

      // Roblox-friendly error response
      const errorResponse = {
        success: false,
        valid: false,
        error: error.message === 'Request timeout' ? 'Request timeout' : 'Authentication service unavailable',
        timestamp: new Date().toISOString()
      };

      return new Response(JSON.stringify(errorResponse), {
        status: error.message === 'Request timeout' ? 408 : 500,
        headers,
      });
    }
  };
};
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);
const validateKeyHandler = async (request, context, corsHeaders) => {
  // Get client IP for rate limiting
  const clientIP = request.headers.get('x-forwarded-for') ||
                  request.headers.get('x-real-ip') ||
                  'unknown';

  if (request.method === 'GET') {
    const url = new URL(request.url);
    const action = url.searchParams.get('action');
    const ip = url.searchParams.get('ip');
    const keyCode = url.searchParams.get('key');
    const hwid = url.searchParams.get('hwid');
    if (action === 'check-ip' && ip) {
      const since = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      // Anonymize the IP for database lookup
      const ipHash = anonymizeIP(ip);
      const { data, error } = await supabase
        .from('license_keys')
        .select('key_code, created_at')
        .eq('ip_hash', ipHash)
        .gte('created_at', since)
        .order('created_at', { ascending: false })
        .limit(1);
      if (error) {
        return new Response(JSON.stringify({ key: null, error: error.message }), {
          status: 200,
          headers: corsHeaders,
        });
      }
      if (data && data.length > 0) {
        return new Response(JSON.stringify({ key: data[0].key_code }), {
          status: 200,
          headers: corsHeaders,
        });
      }
      return new Response(JSON.stringify({ key: null }), {
        status: 200,
        headers: corsHeaders,
      });
    }
    if (action === 'heartbeat' && keyCode && hwid) {
      try {
        const { data: key, error: keyError } = await supabase
          .from('license_keys')
          .select(`*, hwid_bindings(*)`)
          .eq('key_code', keyCode)
          .eq('is_active', true)
          .eq('is_revoked', false)
          .single();
        if (keyError || !key) {
          return new Response(JSON.stringify({ valid: false, error: 'Key not found or inactive' }), {
            status: 404,
            headers: corsHeaders,
          });
        }
        if (new Date(key.expires_at) < new Date()) {
          return new Response(JSON.stringify({ valid: false, error: 'Key has expired' }), {
            status: 403,
            headers: corsHeaders,
          });
        }
        const binding = key.hwid_bindings && key.hwid_bindings.length > 0 ? key.hwid_bindings[0] : null;
        if (!binding || binding.hwid_hash !== hwid) {
          return new Response(JSON.stringify({ valid: false, error: 'HWID mismatch' }), {
            status: 403,
            headers: corsHeaders,
          });
        }
        await supabase
          .from('license_keys')
          .update({ last_heartbeat: new Date().toISOString() })
          .eq('id', key.id);
        return new Response(JSON.stringify({
          valid: true,
          expires_at: key.expires_at,
          usage_count: key.usage_count
        }), {
          status: 200,
          headers: corsHeaders,
        });
      } catch (error) {
        return new Response(JSON.stringify({ valid: false, error: 'Validation failed' }), {
          status: 500,
          headers: corsHeaders,
        });
      }
    }
    // Simple GET validation for Roblox compatibility (like your original system)
    const requestUrl = new URL(request.url);
    const getKeyCode = requestUrl.searchParams.get('key') || requestUrl.searchParams.get('mnemonic');
    const getHwid = requestUrl.searchParams.get('hwid');
    const getPassword = requestUrl.searchParams.get('password');

    // If no key provided, return service info
    if (!getKeyCode) {
      return new Response(JSON.stringify({
        success: true,
        message: 'Key validation service is online',
        timestamp: new Date().toISOString(),
        usage: 'GET /?key=MADARA-XXXX-XXXX-XXXX&hwid=your-hwid',
        example: 'https://projectmadara.com/.netlify/functions/validate-key?key=MADARA-B8DB-9130-18F1&hwid=test-hwid'
      }), {
        status: 200,
        headers: corsHeaders,
      });
    }

    // Simple GET-based key validation
    if (getKeyCode && getHwid) {
      try {
        // Validate key format
        if (!validateKeyFormat(getKeyCode)) {
          return new Response('warn("Key validation failed: Invalid key format")', {
            status: 200,
            headers: { ...corsHeaders, 'Content-Type': 'text/plain' },
          });
        }

        // Check key in database
        const { data: key, error: keyError } = await supabase
          .from('license_keys')
          .select(`*, hwid_bindings(*)`)
          .eq('key_code', getKeyCode)
          .single();

        if (keyError || !key) {
          return new Response('warn("Key validation failed: No Key found or File Key is Incorrect / doesn\'t exist")', {
            status: 200,
            headers: { ...corsHeaders, 'Content-Type': 'text/plain' },
          });
        }

        if (!key.is_active || key.is_revoked) {
          return new Response('warn("Key validation failed: Key is inactive or revoked")', {
            status: 200,
            headers: { ...corsHeaders, 'Content-Type': 'text/plain' },
          });
        }

        if (new Date(key.expires_at) < new Date()) {
          return new Response('warn("Key validation failed: Key has expired")', {
            status: 200,
            headers: { ...corsHeaders, 'Content-Type': 'text/plain' },
          });
        }

        // Check or create HWID binding
        const existingBinding = key.hwid_bindings && key.hwid_bindings.length > 0 ? key.hwid_bindings[0] : null;

        if (existingBinding) {
          // Key is already bound to a device
          if (existingBinding.hwid_hash === getHwid) {
            // Same HWID - allow access
            console.log('Same HWID detected, allowing access');
          } else {
            // Different HWID - deny access
            return new Response('warn("Key validation failed: Key is already in use by another device")', {
              status: 200,
              headers: { ...corsHeaders, 'Content-Type': 'text/plain' },
            });
          }
        }

        // Update usage - but don't create HWID binding for test key
        if (!existingBinding && getKeyCode !== 'MADARA-B8DB-9130-18F1') {
          await supabase
            .from('hwid_bindings')
            .insert({
              key_id: key.id,
              hwid_hash: getHwid,
              roblox_username: 'Unknown'
            });
        }

        await supabase
          .from('license_keys')
          .update({
            last_used_at: new Date().toISOString(),
            usage_count: key.usage_count + 1
          })
          .eq('id', key.id);

        // Return success script (like your original system)
        return new Response(`
-- Key validation successful
print("✅ Key validated successfully!")
print("⏰ Expires: ${key.expires_at}")
print("📊 Usage count: ${key.usage_count + 1}")
return true
        `.trim(), {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'text/plain' },
        });

      } catch (error) {
        console.error('GET validation error:', error);
        return new Response('warn("Key validation failed: Server error")', {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'text/plain' },
        });
      }
    }
  }


  if (request.method !== 'POST') {
    return new Response(JSON.stringify({
      success: false,
      error: 'Method not allowed. Use POST for key validation.',
      valid: false
    }), {
      status: 405,
      headers: corsHeaders,
    });
  }

  try {
    const body = await request.json();
    const { keyCode, hwid, sessionToken, action, timestamp, clientInfo, placeId, heartbeat } = body;

    // Enhanced validation - check required fields
    if (!keyCode || !hwid || !clientInfo || !placeId) {
      return new Response(JSON.stringify({
        valid: false,
        error: 'Missing required fields'
      }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    // Enhanced format validation
    if (!validateKeyFormat(keyCode)) {
      return new Response(JSON.stringify({
        valid: false,
        error: 'Invalid key format'
      }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    if (!validateHWID(hwid)) {
      return new Response(JSON.stringify({
        valid: false,
        error: 'Invalid hardware identifier'
      }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    // Enhanced rate limiting
    const rateLimitCheck = checkRateLimit(clientIP, hwid);
    if (!rateLimitCheck.allowed) {
      return new Response(JSON.stringify({
        valid: false,
        error: rateLimitCheck.reason
      }), {
        status: 429,
        headers: corsHeaders,
      });
    }

    // Map fields to database schema
    const hwidHash = hwid; // Use hwid as hwidHash for database queries
    const robloxUsername = clientInfo.username || 'Unknown';
    const fingerprint = hwid;
    const headers = request.headers;
    // Extract raw IP for processing
    let rawIP = headers.get('x-forwarded-for') || headers.get('x-real-ip') || 'unknown';
    if (rawIP && rawIP.includes(',')) {
      rawIP = rawIP.split(',')[0].trim();
    }

    // Anonymize IP for storage and security operations
    const ipHash = anonymizeIP(rawIP);
    const ipAddress = rawIP; // Keep for legacy compatibility during transition
    const userAgent = headers.get('user-agent') || 'unknown';
    const rateLimitWindow = 5 * 60 * 1000; // 5 minutes
    const maxAttempts = 10; // Max 10 attempts per 5 minutes per IP
    const since = new Date(Date.now() - rateLimitWindow).toISOString();
    const { data: recentAttempts, error: rateLimitError } = await supabase
      .from('key_usage_logs')
      .select('id')
      .eq('ip_hash', ipHash)
      .gte('created_at', since)
      .eq('action', 'validate');
    if (!rateLimitError && recentAttempts && recentAttempts.length >= maxAttempts) {
      await logUsage(null, ipAddress, userAgent, 'rate_limit_exceeded', false, `Too many validation attempts: ${recentAttempts.length}`);
      return new Response(JSON.stringify({
        error: 'Too many validation attempts. Please wait 5 minutes before trying again.',
        code: 'RATE_LIMIT_EXCEEDED'
      }), {
        status: 429,
        headers: corsHeaders,
      });
    }
    const { data: ipBan, error: banError } = await supabase
      .from('ip_bans')
      .select('*')
      .eq('ip_hash', ipHash)
      .eq('is_active', true)
      .single();
    if (!banError && ipBan) {
      const isExpired = ipBan.expires_at && new Date(ipBan.expires_at) < new Date();
      if (!isExpired) {
        await logSecurityEvent('ip_ban_access_attempt', 'high', ipAddress, userAgent, null, {
          ban_reason: ipBan.reason,
          ban_expires: ipBan.expires_at
        });
        return new Response(JSON.stringify({ 
          error: 'Access denied. Your IP address has been banned.',
          code: 'IP_BANNED'
        }), {
          status: 403,
          headers: corsHeaders,
        });
      } else {
        await supabase
          .from('ip_bans')
          .update({ is_active: false })
          .eq('id', ipBan.id);
      }
    }
    if (!keyCode || !hwidHash) {
      await logUsage(null, ipAddress, userAgent, 'validation_failed', false, 'Missing key code or HWID');
      return new Response(JSON.stringify({ error: 'Key code and HWID are required' }), {
        status: 400,
        headers: corsHeaders,
      });
    }
    const keyPattern = /^MADARA-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}$/;
    if (!keyPattern.test(keyCode)) {
      await logUsage(null, ipAddress, userAgent, 'validation_failed', false, 'Invalid key format');
      return new Response(JSON.stringify({
        error: 'Invalid key format. Expected: MADARA-XXXX-XXXX-XXXX',
        code: 'INVALID_KEY_FORMAT'
      }), {
        status: 400,
        headers: corsHeaders,
      });
    }
    if (hwidHash.length < 10 || hwidHash.length > 255) {
      await logUsage(null, ipAddress, userAgent, 'validation_failed', false, 'Invalid HWID format');
      return new Response(JSON.stringify({
        error: 'Invalid hardware ID format',
        code: 'INVALID_HWID'
      }), {
        status: 400,
        headers: corsHeaders,
      });
    }
    const suspiciousPatterns = [
      /^0+$/, // All zeros
      /^1+$/, // All ones
      /test|debug|vm|virtual|emulator/i, // Common VM keywords
      /^(.)\1{9,}/, // Repeated characters
    ];
    if (suspiciousPatterns.some(pattern => pattern.test(hwidHash))) {
      await logUsage(null, ipAddress, userAgent, 'validation_failed', false, 'Suspicious HWID detected');
      return new Response(JSON.stringify({
        error: 'Invalid hardware configuration detected',
        code: 'SUSPICIOUS_HWID'
      }), {
        status: 403,
        headers: corsHeaders,
      });
    }
    const { data: key, error: keyError } = await supabase
      .from('license_keys')
      .select(`*, hwid_bindings(*)`)
      .eq('key_code', keyCode)
      .single();
    if (!keyError && key && heartbeat) {
      const heartbeatInterval = 5 * 60 * 1000; // 5 minutes
      const now = new Date();
      if (key.last_heartbeat) {
        const timeSinceLastHeartbeat = now - new Date(key.last_heartbeat);
        if (timeSinceLastHeartbeat > heartbeatInterval * 2) {
          await logUsage(key.id, ipAddress, userAgent, 'heartbeat_timeout', false, 'Heartbeat timeout detected');
          return new Response(JSON.stringify({ 
            error: 'Session timeout. Please restart your script.',
            code: 'HEARTBEAT_TIMEOUT'
          }), {
            status: 403,
            headers: corsHeaders,
          });
        }
      }
      await supabase
        .from('license_keys')
        .update({ 
          last_heartbeat: now.toISOString(),
          last_used_at: now.toISOString()
        })
        .eq('id', key.id);
      return new Response(JSON.stringify({
        success: true,
        valid: true,
        heartbeat: true,
        message: 'Heartbeat updated',
        expires_at: key.expires_at
      }), {
        status: 200,
        headers: corsHeaders,
      });
    }
    if (keyError || !key) {
      await logUsage(null, ipAddress, userAgent, 'validation_failed', false, 'Invalid key code');
      return new Response(JSON.stringify({ error: 'Invalid key code' }), {
        status: 404,
        headers: corsHeaders,
      });
    }
    const recentValidations = await supabase
      .from('key_usage_logs')
      .select('ip_hash, ip_address, created_at')
      .eq('key_id', key.id)
      .eq('action', 'validation_success')
      .gte('created_at', new Date(Date.now() - 5 * 60 * 1000).toISOString()) // Last 5 minutes
      .order('created_at', { ascending: false });
    if (recentValidations.data && recentValidations.data.length > 0) {
      // Use anonymized IPs for comparison, fallback to raw IPs during transition
      const uniqueIPs = new Set(recentValidations.data.map(log => log.ip_hash || log.ip_address));
      if (uniqueIPs.size > 1) {
        await logUsage(key.id, ipAddress, userAgent, 'concurrent_sessions', false, 'Multiple concurrent sessions detected');
        await logSecurityEvent('concurrent_sessions_detected', 'critical', ipAddress, userAgent, key.id, {
          unique_ips: Array.from(uniqueIPs),
          key_code: key.key_code
        });
        await supabase
          .from('license_keys')
          .update({ 
            bypass_attempts: (key.bypass_attempts || 0) + 1,
            is_revoked: true
          })
          .eq('id', key.id);
        return new Response(JSON.stringify({ 
          error: 'Key revoked due to suspicious activity. Multiple concurrent sessions detected.',
          code: 'KEY_REVOKED'
        }), {
          status: 403,
          headers: corsHeaders,
        });
      }
    }
    const recentUsage = await supabase
      .from('key_usage_logs')
      .select('created_at')
      .eq('key_id', key.id)
      .eq('action', 'validation_success')
      .gte('created_at', new Date(Date.now() - 60 * 1000).toISOString()); // Last minute
    if (recentUsage.data && recentUsage.data.length > 10) {
      await logUsage(key.id, ipAddress, userAgent, 'key_rate_limit', false, 'Key validation rate limit exceeded');
      await logSecurityEvent('rate_limit_exceeded', 'high', ipAddress, userAgent, key.id, {
        request_count: recentUsage.data.length,
        key_code: key.key_code
      });
      return new Response(JSON.stringify({ 
        error: 'Too many validation requests. Please slow down.',
        code: 'RATE_LIMITED'
      }), {
        status: 429,
        headers: corsHeaders,
      });
    }
    if (key.fingerprint_hash && fingerprint && key.fingerprint_hash !== fingerprint) {
      await logUsage(key.id, ipAddress, userAgent, 'validation_failed', false, 'Fingerprint mismatch');
      await logSecurityEvent('fingerprint_mismatch', 'high', ipAddress, userAgent, key.id, {
        expected_fingerprint: key.fingerprint_hash,
        provided_fingerprint: fingerprint,
        key_code: key.key_code
      });
      return new Response(JSON.stringify({ error: 'Device fingerprint does not match. Key cannot be used on this device.' }), {
        status: 403,
        headers: corsHeaders,
      });
    }
    if (!key.is_active || key.is_revoked) {
      await logUsage(key.id, ipAddress, userAgent, 'validation_failed', false, 'Key is inactive or revoked');
      return new Response(JSON.stringify({ error: 'Key is inactive or revoked' }), {
        status: 403,
        headers: corsHeaders,
      });
    }
    if (new Date(key.expires_at) < new Date()) {
      await logUsage(key.id, ipAddress, userAgent, 'validation_failed', false, 'Key has expired');
      return new Response(JSON.stringify({ error: 'Key has expired' }), {
        status: 403,
        headers: corsHeaders,
      });
    }
    const existingBinding = key.hwid_bindings && key.hwid_bindings.length > 0 ? key.hwid_bindings[0] : null;
    if (existingBinding) {
      if (existingBinding.hwid_hash === hwidHash) {
        await supabase
          .from('hwid_bindings')
          .update({ last_used_at: new Date().toISOString() })
          .eq('id', existingBinding.id);
        await supabase
          .from('license_keys')
          .update({
            last_used_at: new Date().toISOString(),
            usage_count: key.usage_count + 1,
            roblox_hwid: hwidHash // Update the Roblox HWID in the license_keys table
          })
          .eq('id', key.id);
        await logUsage(key.id, ipAddress, userAgent, 'validation_success', true);
        return new Response(JSON.stringify({
          success: true,
          valid: true,
          message: 'Key is valid',
          expires_at: key.expires_at,
          usage_count: key.usage_count + 1
        }), {
          status: 200,
          headers: corsHeaders,
        });
      } else {
        await logUsage(key.id, ipAddress, userAgent, 'validation_failed', false, 'Key already bound to different HWID');
        return new Response(JSON.stringify({ 
          error: 'Key is already in use by another device',
          code: 'KEY_ALREADY_BOUND'
        }), {
          status: 403,
          headers: corsHeaders,
        });
      }
    } else {
      const { data: binding, error: bindingError } = await supabase
        .from('hwid_bindings')
        .insert({
          key_id: key.id,
          hwid_hash: hwidHash,
          roblox_username: robloxUsername || null
        })
        .select()
        .single();
      if (bindingError) {
        await logUsage(key.id, ipAddress, userAgent, 'validation_failed', false, 'Failed to bind HWID');
        return new Response(JSON.stringify({ error: 'Failed to bind device' }), {
          status: 500,
          headers: corsHeaders,
        });
      }
      await supabase
        .from('license_keys')
        .update({
          last_used_at: new Date().toISOString(),
          usage_count: key.usage_count + 1,
          roblox_hwid: hwidHash // Store the Roblox HWID in the license_keys table
        })
        .eq('id', key.id);
      await logUsage(key.id, ipAddress, userAgent, 'validation_success', true);
      return new Response(JSON.stringify({
        success: true,
        valid: true,
        message: 'Key is valid and device has been bound',
        expires_at: key.expires_at,
        usage_count: key.usage_count + 1,
        newly_bound: true
      }), {
        status: 200,
        headers: corsHeaders,
      });
    }
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};
const logUsage = async (keyId, ipAddress, userAgent, action, success, errorMessage = null) => {
  try {
    // Anonymize IP for storage
    const ipHash = anonymizeIP(ipAddress);

    await supabase
      .from('key_usage_logs')
      .insert({
        key_id: keyId,
        ip_address: ipAddress, // Keep during transition
        ip_hash: ipHash, // New anonymized field
        user_agent: userAgent,
        action,
        success,
        error_message: errorMessage
      });
  } catch (error) {
  }
};
const logSecurityEvent = async (eventType, severity, ipAddress, userAgent, keyId, details = {}) => {
  try {
    // Anonymize IP for storage
    const ipHash = anonymizeIP(ipAddress);

    await supabase
      .from('security_events')
      .insert({
        event_type: eventType,
        severity: severity,
        ip_address: ipAddress, // Keep during transition
        ip_hash: ipHash, // New anonymized field
        user_agent: userAgent,
        key_id: keyId,
        details: details,
        created_at: new Date().toISOString()
      });
  } catch (error) {
  }
};
export default allowCors(validateKeyHandler); 