import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

const allowCors = (fn) => async (request, context) => {
  const origin = request.headers.get('origin') || '*';
  const headers = {
    'Access-Control-Allow-Origin': origin,
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, x-admin-username, x-admin-password, x-session-token',
    'Access-Control-Allow-Credentials': 'true',
  };
  
  if (request.method === 'OPTIONS') {
    return new Response('', { status: 204, headers });
  }
  
  const result = await fn(request, context, headers);
  return result;
};

// Verify admin authentication and authorization
const verifyAdminAuth = async (request) => {
  const sessionToken = request.headers.get('x-session-token');
  
  if (!sessionToken) {
    return { error: 'Missing session token', status: 401 };
  }

  try {
    const sessionData = JSON.parse(Buffer.from(sessionToken, 'base64').toString());
    
    if (!sessionData.username || !sessionData.timestamp) {
      return { error: 'Invalid token data', status: 401 };
    }

    // Check if session is expired (24 hours)
    const sessionAge = Date.now() - sessionData.timestamp;
    if (sessionAge > 24 * 60 * 60 * 1000) {
      return { error: 'Session expired', status: 401 };
    }

    // Verify user still exists and is active
    const { data: adminUser, error: adminError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('username', sessionData.username)
      .eq('is_active', true)
      .single();

    if (adminError || !adminUser) {
      return { error: 'User not found or inactive', status: 401 };
    }

    return { 
      admin: { 
        id: adminUser.id,
        username: adminUser.username, 
        role: adminUser.role, 
        permissions: adminUser.permissions || {} 
      } 
    };
  } catch (error) {
    return { error: 'Token validation failed', status: 500 };
  }
};

// Log admin security actions
const logSecurityAction = async (adminUsername, actionType, targetIdentifier, details, ipAddress) => {
  try {
    await supabase
      .from('admin_security_actions')
      .insert({
        admin_username: adminUsername,
        action_type: actionType,
        target_identifier: targetIdentifier,
        details: details,
        ip_address: ipAddress || 'unknown'
      });
  } catch (error) {
    console.error('Failed to log security action:', error);
  }
};

// Get all admin users (owner only)
const handleGetAdminUsers = async (corsHeaders, requestingAdmin) => {
  if (requestingAdmin.role !== 'owner') {
    return new Response(JSON.stringify({ error: 'Owner role required' }), {
      status: 403,
      headers: corsHeaders,
    });
  }

  try {
    const { data: adminUsers, error } = await supabase
      .from('admin_users')
      .select('id, username, role, permissions, created_at, last_login, is_active')
      .order('created_at', { ascending: false });

    if (error) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: corsHeaders,
      });
    }

    return new Response(JSON.stringify({ data: adminUsers }), {
      status: 200,
      headers: corsHeaders,
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};

// Create new admin user (owner only)
const handleCreateAdminUser = async (request, corsHeaders, requestingAdmin, ipAddress) => {
  if (requestingAdmin.role !== 'owner') {
    return new Response(JSON.stringify({ error: 'Owner role required' }), {
      status: 403,
      headers: corsHeaders,
    });
  }

  try {
    const { username, password, role, permissions } = await request.json();

    if (!username || !password || !role) {
      return new Response(JSON.stringify({ error: 'Username, password, and role are required' }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    // Validate role
    const validRoles = ['admin', 'ml_security'];
    if (!validRoles.includes(role)) {
      return new Response(JSON.stringify({ error: 'Invalid role. Must be admin or ml_security' }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user
    const { data: newUser, error } = await supabase
      .from('admin_users')
      .insert({
        username,
        password_hash: hashedPassword,
        role,
        permissions: permissions || {},
        is_active: true
      })
      .select('id, username, role, permissions, created_at, is_active')
      .single();

    if (error) {
      if (error.code === '23505') { // Unique constraint violation
        return new Response(JSON.stringify({ error: 'Username already exists' }), {
          status: 409,
          headers: corsHeaders,
        });
      }
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: corsHeaders,
      });
    }

    // Log security action
    await logSecurityAction(
      requestingAdmin.username,
      'admin_user_created',
      username,
      { role, permissions },
      ipAddress
    );

    return new Response(JSON.stringify({ data: newUser }), {
      status: 201,
      headers: corsHeaders,
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};

// Delete admin user (owner only, with strict protections)
const handleDeleteAdminUser = async (request, corsHeaders, requestingAdmin, ipAddress) => {
  if (requestingAdmin.role !== 'owner') {
    return new Response(JSON.stringify({ error: 'Owner role required' }), {
      status: 403,
      headers: corsHeaders,
    });
  }

  try {
    const { id } = await request.json();

    if (!id) {
      return new Response(JSON.stringify({ error: 'User ID is required' }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    // Get the target user
    const { data: targetUser, error: fetchError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError || !targetUser) {
      return new Response(JSON.stringify({ error: 'User not found' }), {
        status: 404,
        headers: corsHeaders,
      });
    }

    // SECURITY: Prevent self-deletion
    if (targetUser.username === requestingAdmin.username) {
      return new Response(JSON.stringify({
        error: 'Cannot delete your own account. This action must be performed by another owner.'
      }), {
        status: 403,
        headers: corsHeaders,
      });
    }

    // SECURITY: Prevent deleting other owners
    if (targetUser.role === 'owner') {
      return new Response(JSON.stringify({
        error: 'Cannot delete owner accounts for security reasons.'
      }), {
        status: 403,
        headers: corsHeaders,
      });
    }

    // Delete the user
    const { error: deleteError } = await supabase
      .from('admin_users')
      .delete()
      .eq('id', id);

    if (deleteError) {
      return new Response(JSON.stringify({ error: deleteError.message }), {
        status: 500,
        headers: corsHeaders,
      });
    }

    // Log security action
    await logSecurityAction(
      requestingAdmin.username,
      'admin_user_deleted',
      targetUser.username,
      {
        deleted_user_id: id,
        deleted_user_role: targetUser.role,
        deleted_user_username: targetUser.username
      },
      ipAddress
    );

    return new Response(JSON.stringify({
      message: `Admin user '${targetUser.username}' has been deleted successfully.`,
      deleted_user: {
        id: targetUser.id,
        username: targetUser.username,
        role: targetUser.role
      }
    }), {
      status: 200,
      headers: corsHeaders,
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};

// Update admin user (owner only, with self-modification protection)
const handleUpdateAdminUser = async (request, corsHeaders, requestingAdmin, ipAddress) => {
  if (requestingAdmin.role !== 'owner') {
    return new Response(JSON.stringify({ error: 'Owner role required' }), {
      status: 403,
      headers: corsHeaders,
    });
  }

  try {
    const { id, username, role, permissions, is_active } = await request.json();

    if (!id) {
      return new Response(JSON.stringify({ error: 'User ID is required' }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    // Get the target user
    const { data: targetUser, error: fetchError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError || !targetUser) {
      return new Response(JSON.stringify({ error: 'User not found' }), {
        status: 404,
        headers: corsHeaders,
      });
    }

    // SECURITY: Prevent self-role modification
    if (targetUser.username === requestingAdmin.username && role && role !== targetUser.role) {
      return new Response(JSON.stringify({ 
        error: 'Cannot modify your own role. This action must be performed by another owner.' 
      }), {
        status: 403,
        headers: corsHeaders,
      });
    }

    // SECURITY: Prevent modifying other owners (only one owner should exist)
    if (targetUser.role === 'owner' && targetUser.username !== requestingAdmin.username) {
      return new Response(JSON.stringify({ 
        error: 'Cannot modify other owner accounts' 
      }), {
        status: 403,
        headers: corsHeaders,
      });
    }

    // Validate role if provided
    if (role) {
      const validRoles = ['admin', 'ml_security'];
      if (!validRoles.includes(role)) {
        return new Response(JSON.stringify({ error: 'Invalid role. Must be admin or ml_security' }), {
          status: 400,
          headers: corsHeaders,
        });
      }
    }

    // Build update object
    const updateData = {};
    if (username && username !== targetUser.username) updateData.username = username;
    if (role && role !== targetUser.role) updateData.role = role;
    if (permissions !== undefined) updateData.permissions = permissions;
    if (is_active !== undefined) updateData.is_active = is_active;

    if (Object.keys(updateData).length === 0) {
      return new Response(JSON.stringify({ error: 'No changes provided' }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    // Update user
    const { data: updatedUser, error } = await supabase
      .from('admin_users')
      .update(updateData)
      .eq('id', id)
      .select('id, username, role, permissions, created_at, last_login, is_active')
      .single();

    if (error) {
      if (error.code === '23505') { // Unique constraint violation
        return new Response(JSON.stringify({ error: 'Username already exists' }), {
          status: 409,
          headers: corsHeaders,
        });
      }
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: corsHeaders,
      });
    }

    // Log security action
    await logSecurityAction(
      requestingAdmin.username,
      'admin_user_updated',
      targetUser.username,
      { changes: updateData, target_id: id },
      ipAddress
    );

    return new Response(JSON.stringify({ data: updatedUser }), {
      status: 200,
      headers: corsHeaders,
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};

const adminUsersHandler = async (request, context, corsHeaders) => {
  const ipAddress = request.headers.get('client-ip') || 
                   request.headers.get('x-forwarded-for') || 
                   'unknown';

  // Verify authentication
  const authResult = await verifyAdminAuth(request);
  if (authResult.error) {
    return new Response(JSON.stringify({ error: authResult.error }), {
      status: authResult.status,
      headers: corsHeaders,
    });
  }

  const requestingAdmin = authResult.admin;

  try {
    switch (request.method) {
      case 'GET':
        return await handleGetAdminUsers(corsHeaders, requestingAdmin);
      
      case 'POST':
        return await handleCreateAdminUser(request, corsHeaders, requestingAdmin, ipAddress);
      
      case 'PUT':
        return await handleUpdateAdminUser(request, corsHeaders, requestingAdmin, ipAddress);

      case 'DELETE':
        return await handleDeleteAdminUser(request, corsHeaders, requestingAdmin, ipAddress);

      default:
        return new Response(JSON.stringify({ error: 'Method not allowed' }), {
          status: 405,
          headers: corsHeaders,
        });
    }
  } catch (error) {
    console.error('Admin users handler error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};

export default allowCors(adminUsersHandler);
