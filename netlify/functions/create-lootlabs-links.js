import { createClient } from '@supabase/supabase-js';
import { createCampaignLinks } from './utils/lootlabs-service.js';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Credentials': 'true'
};
export const handler = async (event) => {
  try {
    if (event.httpMethod === 'OPTIONS') {
      return { statusCode: 200, headers: corsHeaders };
    }

    if (event.httpMethod !== 'POST') {
      return {
        statusCode: 405,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Initialize Supabase
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (!supabaseUrl || !supabaseKey) {
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing Supabase environment variables' })
      };
    }
    const supabase = createClient(supabaseUrl, supabaseKey);
    // Check if Supabase is initialized
    if (!supabase) {
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({
          error: 'Database connection failed',
          details: 'Supabase client not initialized'
        })
      };
    }
    let body;
    try {
      body = JSON.parse(event.body || '{}');
    } catch (parseError) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({
          error: 'Invalid JSON in request body',
          details: parseError.message
        })
      };
    }
    const { campaignId, sessionId, returnUrl } = body;
    // Enhanced parameter validation
    if (!campaignId) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({
          error: 'Missing required parameter: campaignId',
          details: 'campaignId is required to identify which campaign to use for link generation'
        })
      };
    }
    if (!sessionId) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({
          error: 'Missing required parameter: sessionId',
          details: 'sessionId is required for tracking and security purposes'
        })
      };
    }
    // Validate sessionId format (should be UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(sessionId)) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({
          error: 'Invalid sessionId format',
          details: 'sessionId must be a valid UUID'
        })
      };
    }
    // Get campaign details with enhanced error handling
    const { data: campaign, error: campaignError } = await supabase
      .from('lootlabs_campaigns')
      .select('*')
      .eq('id', campaignId)
      .eq('is_active', true)
      .single();
    if (campaignError) {
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({
          error: 'Database error while fetching campaign',
          details: campaignError.message,
          code: campaignError.code
        })
      };
    }
    if (!campaign) {
      return {
        statusCode: 404,
        headers: corsHeaders,
        body: JSON.stringify({
          error: 'Campaign not found or inactive',
          details: `No active campaign found with ID: ${campaignId}`
        })
      };
    }
    // Validate campaign configuration
    if (!campaign.tier_id || campaign.tier_id < 1 || campaign.tier_id > 3) {
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({
          error: 'Invalid campaign configuration',
          details: 'Campaign tier_id must be between 1 and 3'
        })
      };
    }
    if (!campaign.number_of_tasks || campaign.number_of_tasks < 1 || campaign.number_of_tasks > 5) {
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({
          error: 'Invalid campaign configuration',
          details: 'Campaign number_of_tasks must be between 1 and 5'
        })
      };
    }
    // Use the createCampaignLinks function to generate both step 1 and step 2 links
    const { step1, step2, campaign_data } = await createCampaignLinks(campaign, sessionId, returnUrl);
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        step1_url: step1.url,
        step2_url: step2.url,
        campaign: {
          name: campaign_data.name,
          tier_id: campaign_data.tier_id,
          number_of_tasks: campaign_data.number_of_tasks,
          theme: campaign_data.theme
        },
        debug: {
          step1_short: step1.short,
          step2_short: step2.short,
          request_times: {
            step1: campaign_data.request_time_step1,
            step2: campaign_data.request_time_step2
          }
        }
      })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Function execution failed',
        details: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      })
    };
  }
};
export default handler;
