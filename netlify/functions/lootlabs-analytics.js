import { createClient } from '@supabase/supabase-js';
// CORS headers for all responses
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Credentials': 'true'
};
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);
export const handler = async (event) => {
  try {
    if (event.httpMethod === 'OPTIONS') {
      return { statusCode: 200, headers: corsHeaders };
    }
    if (event.httpMethod === 'GET') {
      return await getLootlabsAnalytics(event);
    } else if (event.httpMethod === 'POST') {
      return await trackLootlabsEvent(event);
    } else {
      return {
        statusCode: 405,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error', details: error.message })
    };
  }
};
/**
 * Get comprehensive Lootlabs analytics
 */
async function getLootlabsAnalytics(event) {
  try {
    const params = event.queryStringParameters || {};
    const period = params.period || '7d'; // 1d, 7d, 30d
    const campaignId = params.campaignId;
    const analytics = await generateLootlabsAnalytics(period, campaignId);
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify(analytics)
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Failed to generate analytics' })
    };
  }
}
/**
 * Track Lootlabs events for analytics
 */
async function trackLootlabsEvent(event) {
  try {
    const body = JSON.parse(event.body || '{}');
    const { eventType, sessionId, campaignId, step, data } = body;
    if (!eventType || !sessionId) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'eventType and sessionId are required' })
      };
    }
    // Prepare the insert data - handle session_id type conversion
    const insertData = {
      event_type: eventType,
      session_id: sessionId, // Keep as string since key_sessions uses TEXT for session_id
      campaign_id: campaignId || null,
      step: step || null,
      event_data: data || {},
      created_at: new Date().toISOString()
    };
    // Store event in analytics table
    const { error } = await supabase
      .from('lootlabs_analytics')
      .insert(insertData);
    if (error) {
      throw error;
    }
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ success: true })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Failed to track event',
        details: error.message
      })
    };
  }
}
/**
 * Generate comprehensive Lootlabs analytics
 */
async function generateLootlabsAnalytics(period, campaignId) {
  const now = new Date();
  const startDate = getStartDate(period, now);
  const startDateISO = startDate.toISOString();
  // Base query conditions
  let baseQuery = supabase
    .from('lootlabs_analytics')
    .select('*')
    .gte('created_at', startDateISO);
  if (campaignId) {
    baseQuery = baseQuery.eq('campaign_id', campaignId);
  }
  const { data: events } = await baseQuery;
  // Get session data
  let sessionQuery = supabase
    .from('key_sessions')
    .select('*')
    .gte('created_at', startDateISO);
  const { data: sessions } = await sessionQuery;
  // Get campaign data
  const { data: campaigns } = await supabase
    .from('lootlabs_campaigns')
    .select('*')
    .eq('is_active', true);
  return {
    period,
    overview: generateOverviewStats(events, sessions),
    conversion: generateConversionStats(events, sessions),
    performance: generatePerformanceStats(events, campaigns),
    userBehavior: generateUserBehaviorStats(events),
    campaigns: generateCampaignStats(events, campaigns),
    trends: generateTrendStats(events, period),
    errors: generateErrorStats(events)
  };
}
/**
 * Generate overview statistics
 */
function generateOverviewStats(events, sessions) {
  const totalSessions = sessions?.length || 0;
  const completedSessions = sessions?.filter(s => s.step1 && s.step2).length || 0;
  const step1Completions = sessions?.filter(s => s.step1).length || 0;
  const step2Completions = sessions?.filter(s => s.step2).length || 0;
  const linkClicks = events?.filter(e => e.event_type === 'link_click').length || 0;
  const linkCreations = events?.filter(e => e.event_type === 'link_created').length || 0;
  const verificationAttempts = events?.filter(e => e.event_type === 'verification_attempt').length || 0;
  return {
    totalSessions,
    completedSessions,
    step1Completions,
    step2Completions,
    linkClicks,
    linkCreations,
    verificationAttempts,
    completionRate: totalSessions > 0 ? (completedSessions / totalSessions * 100).toFixed(2) : 0,
    step1ConversionRate: linkClicks > 0 ? (step1Completions / linkClicks * 100).toFixed(2) : 0,
    step2ConversionRate: step1Completions > 0 ? (step2Completions / step1Completions * 100).toFixed(2) : 0
  };
}
/**
 * Generate conversion funnel statistics
 */
function generateConversionStats(events, sessions) {
  const funnel = {
    sessionStarted: sessions?.length || 0,
    linksCreated: events?.filter(e => e.event_type === 'link_created').length || 0,
    step1Clicked: events?.filter(e => e.event_type === 'link_click' && e.step === 1).length || 0,
    step1Completed: sessions?.filter(s => s.step1).length || 0,
    step2Clicked: events?.filter(e => e.event_type === 'link_click' && e.step === 2).length || 0,
    step2Completed: sessions?.filter(s => s.step2).length || 0,
    keyGenerated: sessions?.filter(s => s.step1 && s.step2).length || 0
  };
  // Calculate drop-off rates
  const dropOffRates = {};
  const stages = Object.keys(funnel);
  for (let i = 1; i < stages.length; i++) {
    const current = funnel[stages[i]];
    const previous = funnel[stages[i - 1]];
    dropOffRates[`${stages[i - 1]}_to_${stages[i]}`] = 
      previous > 0 ? ((previous - current) / previous * 100).toFixed(2) : 0;
  }
  return { funnel, dropOffRates };
}
/**
 * Generate performance statistics
 */
function generatePerformanceStats(events, campaigns) {
  const performanceByTier = {};
  const performanceByTheme = {};
  campaigns?.forEach(campaign => {
    const campaignEvents = events?.filter(e => e.campaign_id === campaign.id) || [];
    const clicks = campaignEvents.filter(e => e.event_type === 'link_click').length;
    const completions = campaignEvents.filter(e => e.event_type === 'step_completed').length;
    // Group by tier
    if (!performanceByTier[campaign.tier_id]) {
      performanceByTier[campaign.tier_id] = { clicks: 0, completions: 0, campaigns: 0 };
    }
    performanceByTier[campaign.tier_id].clicks += clicks;
    performanceByTier[campaign.tier_id].completions += completions;
    performanceByTier[campaign.tier_id].campaigns += 1;
    // Group by theme
    if (!performanceByTheme[campaign.theme]) {
      performanceByTheme[campaign.theme] = { clicks: 0, completions: 0, campaigns: 0 };
    }
    performanceByTheme[campaign.theme].clicks += clicks;
    performanceByTheme[campaign.theme].completions += completions;
    performanceByTheme[campaign.theme].campaigns += 1;
  });
  return { performanceByTier, performanceByTheme };
}
/**
 * Generate user behavior statistics
 */
function generateUserBehaviorStats(events) {
  const verificationEvents = events?.filter(e => e.event_type === 'verification_attempt') || [];
  let totalScore = 0;
  let scoreCount = 0;
  const behaviorFactors = {
    mouseMovement: [],
    keyboardActivity: [],
    scrollActivity: [],
    focusEvents: []
  };
  verificationEvents.forEach(event => {
    if (event.event_data?.score) {
      totalScore += event.event_data.score;
      scoreCount++;
    }
    if (event.event_data?.details?.behaviorVerification?.factors) {
      const factors = event.event_data.details.behaviorVerification.factors;
      Object.keys(behaviorFactors).forEach(key => {
        if (factors[key] !== undefined) {
          behaviorFactors[key].push(factors[key]);
        }
      });
    }
  });
  const averageScore = scoreCount > 0 ? (totalScore / scoreCount).toFixed(3) : 0;
  // Calculate averages for behavior factors
  const behaviorAverages = {};
  Object.keys(behaviorFactors).forEach(key => {
    const values = behaviorFactors[key];
    behaviorAverages[key] = values.length > 0 
      ? (values.reduce((a, b) => a + b, 0) / values.length).toFixed(2)
      : 0;
  });
  return {
    averageVerificationScore: averageScore,
    totalVerificationAttempts: verificationEvents.length,
    behaviorAverages,
    suspiciousAttempts: verificationEvents.filter(e => e.event_data?.score < 0.5).length
  };
}
/**
 * Generate campaign-specific statistics
 */
function generateCampaignStats(events, campaigns) {
  return campaigns?.map(campaign => {
    const campaignEvents = events?.filter(e => e.campaign_id === campaign.id) || [];
    const clicks = campaignEvents.filter(e => e.event_type === 'link_click').length;
    const completions = campaignEvents.filter(e => e.event_type === 'step_completed').length;
    const errors = campaignEvents.filter(e => e.event_type === 'error').length;
    return {
      id: campaign.id,
      name: campaign.name,
      tier_id: campaign.tier_id,
      theme: campaign.theme,
      clicks,
      completions,
      errors,
      conversionRate: clicks > 0 ? (completions / clicks * 100).toFixed(2) : 0,
      errorRate: (clicks + completions) > 0 ? (errors / (clicks + completions) * 100).toFixed(2) : 0
    };
  }) || [];
}
/**
 * Generate trend statistics
 */
function generateTrendStats(events, period) {
  // Group events by time intervals based on period
  const intervals = period === '1d' ? 24 : period === '7d' ? 7 : 30;
  const intervalSize = period === '1d' ? 'hour' : 'day';
  const trends = [];
  const now = new Date();
  for (let i = intervals - 1; i >= 0; i--) {
    const intervalStart = new Date(now);
    if (intervalSize === 'hour') {
      intervalStart.setHours(now.getHours() - i, 0, 0, 0);
    } else {
      intervalStart.setDate(now.getDate() - i);
      intervalStart.setHours(0, 0, 0, 0);
    }
    const intervalEnd = new Date(intervalStart);
    if (intervalSize === 'hour') {
      intervalEnd.setHours(intervalStart.getHours() + 1);
    } else {
      intervalEnd.setDate(intervalStart.getDate() + 1);
    }
    const intervalEvents = events?.filter(e => {
      const eventTime = new Date(e.created_at);
      return eventTime >= intervalStart && eventTime < intervalEnd;
    }) || [];
    trends.push({
      interval: intervalStart.toISOString(),
      clicks: intervalEvents.filter(e => e.event_type === 'link_click').length,
      completions: intervalEvents.filter(e => e.event_type === 'step_completed').length,
      errors: intervalEvents.filter(e => e.event_type === 'error').length
    });
  }
  return trends;
}
/**
 * Generate error statistics
 */
function generateErrorStats(events) {
  const errorEvents = events?.filter(e => e.event_type === 'error') || [];
  const errorTypes = {};
  errorEvents.forEach(event => {
    const errorType = event.event_data?.type || 'unknown';
    if (!errorTypes[errorType]) {
      errorTypes[errorType] = 0;
    }
    errorTypes[errorType]++;
  });
  return {
    totalErrors: errorEvents.length,
    errorTypes,
    recentErrors: errorEvents.slice(-10).map(e => ({
      timestamp: e.created_at,
      type: e.event_data?.type || 'unknown',
      message: e.event_data?.message || 'No message',
      sessionId: e.session_id
    }))
  };
}
/**
 * Get start date based on period
 */
function getStartDate(period, now) {
  const startDate = new Date(now);
  switch (period) {
    case '1d':
      startDate.setDate(now.getDate() - 1);
      break;
    case '7d':
      startDate.setDate(now.getDate() - 7);
      break;
    case '30d':
      startDate.setDate(now.getDate() - 30);
      break;
    default:
      startDate.setDate(now.getDate() - 7);
  }
  return startDate;
}
