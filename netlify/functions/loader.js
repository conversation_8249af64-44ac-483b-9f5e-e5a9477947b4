/**
 * Centralized Loader Endpoint v3.0
 * Provides a clean, short URL for the main loader script
 * URL: https://projectmadara.com/loader
 *
 * Flow: Game Support Check → In-Game Key Validation → Script Loading
 * Features:
 * 1. Checks game support before showing key validation
 * 2. In-game key validation UI (no website redirects)
 * 3. Direct API calls (no configuration obfuscation)
 * 4. Proper error handling and user feedback
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Enhanced CORS headers for Roblox compatibility
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept, Origin, User-Agent',
    'Access-Control-Allow-Credentials': 'false',
    'Access-Control-Max-Age': '86400',
    'Content-Type': 'text/plain',
    'Cache-Control': 'public, max-age=300',
    'Pragma': 'cache',
    'Expires': new Date(Date.now() + 300000).toUTCString() // 5 minutes
};

export default async (request) => {
    // Handle CORS preflight
    if (request.method === 'OPTIONS') {
        return new Response('', { status: 204, headers: corsHeaders });
    }
    
    // Only allow GET requests
    if (request.method !== 'GET') {
        return new Response('Method not allowed', { 
            status: 405, 
            headers: corsHeaders 
        });
    }
    
    try {
        // Validate environment variables
        if (!supabaseUrl || !supabaseServiceKey) {
            throw new Error('Server configuration error');
        }
        
        const supabase = createClient(supabaseUrl, supabaseServiceKey);
        
        // Get the main loader script from database
        const { data: script, error } = await supabase
            .from('scripts')
            .select('content, updated_at, version')
            .eq('script_type', 'main_loader')
            .eq('is_main_script', true)
            .eq('is_active', true)
            .single();
            
        if (error || !script) {
            // Fallback error message
            const fallbackScript = `
-- Project Madara Loader Error
print("❌ Main loader temporarily unavailable")
print("🌐 Please visit: https://projectmadara.com/generate-key")
print("📞 Contact support if this issue persists")
`;
            
            return new Response(fallbackScript, {
                status: 200, // Return 200 to prevent executor errors
                headers: { 
                    ...corsHeaders, 
                    'Content-Type': 'text/plain',
                    'Cache-Control': 'no-cache, no-store, must-revalidate'
                }
            });
        }
        
        // Return the main loader script
        return new Response(script.content, {
            status: 200,
            headers: { 
                ...corsHeaders, 
                'Content-Type': 'text/plain',
                'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
                'X-Script-Version': script.version || '1.0.0',
                'X-Last-Updated': script.updated_at || new Date().toISOString()
            }
        });
        
    } catch (error) {
        console.error('Loader endpoint error:', error);
        
        // Return a user-friendly error script instead of HTTP error
        const errorScript = `
-- Project Madara Loader Error
print("❌ Unable to load main script")
print("🌐 Please visit: https://projectmadara.com/generate-key")
print("📞 Error: ${error.message}")
print("🔄 Please try again in a few moments")
`;
        
        return new Response(errorScript, {
            status: 200, // Return 200 to prevent executor errors
            headers: { 
                ...corsHeaders, 
                'Content-Type': 'text/plain',
                'Cache-Control': 'no-cache, no-store, must-revalidate'
            }
        });
    }
};
