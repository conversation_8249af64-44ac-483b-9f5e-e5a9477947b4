import { createClient } from '@supabase/supabase-js';
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Credentials': 'true'
};
export const handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }
  try {
    const body = JSON.parse(event.body);
    const { sessionId, step, token, expiresAt } = body;
    if (!sessionId || !step || !token || !expiresAt) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Missing required parameters' })
      };
    }
    const stepNum = parseInt(step);
    if (stepNum !== 1 && stepNum !== 2) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid step number' })
      };
    }
    // Validate session exists
    const { data: sessionData, error: sessionError } = await supabase
      .from('key_sessions')
      .select('*')
      .eq('session_id', sessionId)
      .single();
    if (sessionError || !sessionData) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid session' })
      };
    }
    // Check if step is already completed
    const field = stepNum === 1 ? 'step1' : 'step2';
    if (sessionData[field] === true) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Step already completed' })
      };
    }
    // Get IP and user agent
    const ipAddress = event.headers['x-forwarded-for']?.split(',')[0]?.trim() || 'unknown';
    const userAgent = event.headers['user-agent'] || '';
    // Store token in database
    const { data: tokenData, error: tokenError } = await supabase
      .from('lootlabs_tokens')
      .insert({
        session_id: sessionId,
        step: stepNum,
        token: token,
        expires_at: expiresAt,
        ip_address: ipAddress,
        user_agent: userAgent
      })
      .select()
      .single();
    if (tokenError) {
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Failed to store verification token' })
      };
    }
    // Log token generation
    await supabase.from('key_usage_logs').insert({
      key_id: null,
      hwid_hash: null,
      ip_address: ipAddress,
      user_agent: userAgent,
      action: 'verification_token_generated',
      success: true,
      error_message: null,
      details: JSON.stringify({ 
        sessionId, 
        step: stepNum, 
        tokenId: tokenData.id,
        expiresAt
      })
    });
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        tokenId: tokenData.id,
        message: 'Verification token stored successfully'
      })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
