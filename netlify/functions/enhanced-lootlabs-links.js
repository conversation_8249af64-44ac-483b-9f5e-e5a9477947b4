import { createClient } from '@supabase/supabase-js';
import { createEnhancedLootlabsLinks } from './utils/lootlabs-service.js';
const allowCors = (handler) => {
  return async (request, context) => {
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    };
    if (request.method === 'OPTIONS') {
      return new Response(null, { status: 200, headers });
    }
    try {
      return await handler(request, context, headers);
    } catch (error) {
      return new Response(JSON.stringify({ error: 'Internal server error' }), {
        status: 500,
        headers,
      });
    }
  };
};
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);
const handler = async (request, _context, corsHeaders) => {
  if (request.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: corsHeaders,
    });
  }
  try {
    const body = await request.json();
    const {
      campaignId,
      sessionId,
      useEncryption = false,
      encryptionPassword = null,
      customTheme = null,
      dynamicTier = false,
      userLocation = null,
      deviceType = null
    } = body;
    if (!campaignId || !sessionId) {
      return new Response(JSON.stringify({ error: 'campaignId and sessionId are required' }), {
        status: 400,
        headers: corsHeaders,
      });
    }
    // Get campaign details with enhanced features
    const { data: campaign, error: campaignError } = await supabase
      .from('lootlabs_campaigns')
      .select('*')
      .eq('id', campaignId)
      .eq('is_active', true)
      .single();
    if (campaignError || !campaign) {
      return new Response(JSON.stringify({ error: 'Campaign not found', details: campaignError?.message }), {
        status: 404,
        headers: corsHeaders,
      });
    }
    // Detect user characteristics if not provided
    const detectedLocation = userLocation || await detectUserLocation(request);
    const detectedDevice = deviceType || detectDeviceType(request);
    // Create enhanced Lootlabs links
    const enhancedResult = await createEnhancedLootlabsLinks({
      campaignId,
      sessionId,
      useEncryption,
      encryptionPassword,
      customTheme,
      dynamicTier,
      userLocation: detectedLocation,
      deviceType: detectedDevice
    });
    // Store enhanced link creation in analytics
    await supabase
      .from('lootlabs_analytics')
      .insert({
        event_type: 'enhanced_links_created',
        session_id: sessionId,
        campaign_id: campaignId,
        event_data: {
          encryption_enabled: useEncryption,
          dynamic_tier: dynamicTier,
          user_location: detectedLocation,
          device_type: detectedDevice,
          custom_theme: customTheme,
          tier_id: enhancedResult.campaign.tier_id,
          number_of_tasks: enhancedResult.campaign.number_of_tasks
        },
        created_at: new Date().toISOString()
      });
    return new Response(JSON.stringify(enhancedResult), {
      status: 200,
      headers: corsHeaders,
    });
  } catch (error) {
    return new Response(JSON.stringify({
      error: 'Failed to create enhanced Lootlabs links',
      details: error.message
    }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};
/**
 * Detect user location from request headers
 */
async function detectUserLocation(request) {
  try {
    // Try to get location from CloudFlare headers
    const cfCountry = request.headers.get('cf-ipcountry');
    if (cfCountry && cfCountry !== 'XX') {
      return cfCountry;
    }
    // Try to get location from other headers
    const xCountry = request.headers.get('x-country-code');
    if (xCountry) {
      return xCountry;
    }
    // Fallback to IP geolocation (would require external service)
    const clientIP = request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
                     request.headers.get('x-real-ip') ||
                     'unknown';
    if (clientIP !== 'unknown') {
      // In a real implementation, you would use a geolocation service here
      // For now, return null to use default settings
      return null;
    }
    return null;
  } catch (error) {
    return null;
  }
}
/**
 * Detect device type from user agent
 */
function detectDeviceType(request) {
  try {
    const userAgent = request.headers.get('user-agent') || '';
    // Mobile detection
    const mobileRegex = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
    if (mobileRegex.test(userAgent)) {
      return 'mobile';
    }
    // Tablet detection
    const tabletRegex = /Tablet|iPad/i;
    if (tabletRegex.test(userAgent)) {
      return 'tablet';
    }
    // Desktop by default
    return 'desktop';
  } catch (error) {
    return 'desktop';
  }
}
export default allowCors(handler);
