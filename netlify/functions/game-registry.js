import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Enhanced CORS headers for Roblox compatibility
const allowCors = (fn) => async (request, context) => {
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept, Origin, User-Agent',
        'Access-Control-Allow-Credentials': 'false', // Set to false for wildcard origin
        'Access-Control-Max-Age': '86400', // Cache preflight for 24 hours
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
    };

    if (request.method === 'OPTIONS') {
        return new Response('', { status: 200, headers }); // Use 200 instead of 204 for better compatibility
    }

    const result = await fn(request, context, headers);
    return result;
};

const handler = async (request, context, corsHeaders) => {
    const url = new URL(request.url);
    const { searchParams } = url;
    
    try {
        if (request.method === 'GET') {
            const placeId = searchParams.get('place_id');
            const supported = searchParams.get('supported');
            const supportLevel = searchParams.get('support_level');
            
            let query = supabase
                .from('game_registry')
                .select(`
                    *,
                    scripts:script_id (
                        id,
                        name,
                        version,
                        file_size_bytes,
                        is_active,
                        updated_at
                    )
                `)
                .order('game_name');
            
            // Apply filters
            if (placeId) {
                query = query.eq('place_id', placeId);
            }
            
            if (supported !== null) {
                query = query.eq('is_supported', supported === 'true');
            }
            
            if (supportLevel) {
                query = query.eq('support_level', supportLevel);
            }
            
            const { data, error } = await query;
            
            if (error) {
                return new Response(JSON.stringify({ error: error.message }), {
                    status: 500,
                    headers: corsHeaders
                });
            }
            
            return new Response(JSON.stringify(data || []), {
                status: 200,
                headers: corsHeaders
            });
            
        } else if (request.method === 'POST') {
            // Add new game to registry
            const body = await request.json();
            
            const { data, error } = await supabase
                .from('game_registry')
                .insert({
                    place_id: body.place_id,
                    game_name: body.game_name,
                    game_description: body.game_description,
                    developer_name: body.developer_name,
                    game_url: body.game_url,
                    thumbnail_url: body.thumbnail_url,
                    script_id: body.script_id,
                    is_supported: body.is_supported || true,
                    support_level: body.support_level || 'full',
                    genre: body.genre,
                    max_players: body.max_players,
                    created_date: body.created_date
                })
                .select()
                .single();
                
            if (error) {
                return new Response(JSON.stringify({ error: error.message }), {
                    status: 400,
                    headers: corsHeaders
                });
            }
            
            return new Response(JSON.stringify(data), {
                status: 201,
                headers: corsHeaders
            });
            
        } else if (request.method === 'PUT') {
            // Update game registry entry
            const body = await request.json();
            const gameId = searchParams.get('id');
            
            if (!gameId) {
                return new Response(JSON.stringify({ error: 'Game ID required' }), {
                    status: 400,
                    headers: corsHeaders
                });
            }
            
            const { data, error } = await supabase
                .from('game_registry')
                .update({
                    game_name: body.game_name,
                    game_description: body.game_description,
                    developer_name: body.developer_name,
                    game_url: body.game_url,
                    thumbnail_url: body.thumbnail_url,
                    script_id: body.script_id,
                    is_supported: body.is_supported,
                    support_level: body.support_level,
                    genre: body.genre,
                    max_players: body.max_players,
                    last_updated: new Date().toISOString()
                })
                .eq('id', gameId)
                .select()
                .single();
                
            if (error) {
                return new Response(JSON.stringify({ error: error.message }), {
                    status: 400,
                    headers: corsHeaders
                });
            }
            
            return new Response(JSON.stringify(data), {
                status: 200,
                headers: corsHeaders
            });
            
        } else if (request.method === 'DELETE') {
            // Delete game registry entry
            const gameId = searchParams.get('id');
            
            if (!gameId) {
                return new Response(JSON.stringify({ error: 'Game ID required' }), {
                    status: 400,
                    headers: corsHeaders
                });
            }
            
            const { error } = await supabase
                .from('game_registry')
                .delete()
                .eq('id', gameId);
                
            if (error) {
                return new Response(JSON.stringify({ error: error.message }), {
                    status: 400,
                    headers: corsHeaders
                });
            }
            
            return new Response(JSON.stringify({ success: true }), {
                status: 200,
                headers: corsHeaders
            });
            
        } else {
            return new Response(JSON.stringify({ error: 'Method not allowed' }), {
                status: 405,
                headers: corsHeaders
            });
        }
        
    } catch (error) {
        console.error('Game registry error:', error);
        return new Response(JSON.stringify({ error: 'Internal server error' }), {
            status: 500,
            headers: corsHeaders
        });
    }
};

export default allowCors(handler);
