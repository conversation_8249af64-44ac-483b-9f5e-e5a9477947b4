import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
const allowCors = (handler) => {
  return async (request, context) => {
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-session-token, x-admin-username, x-admin-password',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    };
    if (request.method === 'OPTIONS') {
      return new Response('', { status: 200, headers });
    }
    try {
      const result = await handler(request, context, headers);
      return result;
    } catch (error) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers,
      });
    }
  };
};
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Session-based authentication
const verifyAdminSession = async (request) => {
  const sessionToken = request.headers.get('x-session-token');

  if (!sessionToken) {
    return { error: 'Missing session token' };
  }

  try {
    const sessionData = JSON.parse(Buffer.from(sessionToken, 'base64').toString());

    if (!sessionData.username || !sessionData.timestamp) {
      return { error: 'Invalid token data' };
    }

    // Check if session is expired (24 hours)
    const sessionAge = Date.now() - sessionData.timestamp;
    if (sessionAge > 24 * 60 * 60 * 1000) {
      return { error: 'Session expired' };
    }

    // Verify user still exists and is active
    const { data: adminUser, error: adminError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('username', sessionData.username)
      .eq('is_active', true)
      .single();

    if (adminError || !adminUser) {
      return { error: 'User not found or inactive' };
    }

    return {
      admin: {
        id: adminUser.id,
        username: adminUser.username,
        role: adminUser.role,
        permissions: adminUser.permissions || {}
      }
    };
  } catch (error) {
    return { error: 'Token validation failed' };
  }
};
const verifySimpleAdmin = async (request) => {
  const username = request.headers.get('x-admin-username');
  const password = request.headers.get('x-admin-password');

  if (!username || !password) {
    return { error: 'Missing credentials' };
  }

  try {
    // Check database for admin users only
    const { data: adminUser, error: dbError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('username', username)
      .eq('is_active', true)
      .single();

    if (!dbError && adminUser) {
      const isPasswordValid = await bcrypt.compare(password, adminUser.password_hash);
      if (isPasswordValid) {
        return {
          admin: {
            username: adminUser.username,
            role: adminUser.role,
            permissions: adminUser.permissions || {}
          }
        };
      }
    }

    return { error: 'Unauthorized' };
  } catch (error) {
    console.error('Admin verification error:', error);
    return { error: 'Authentication failed' };
  }
};
const adminKeysHandler = async (request, context, corsHeaders) => {
  const url = new URL(request.url);
  const queryStringParameters = Object.fromEntries(url.searchParams.entries());
  let body = null;
  if (request.method !== 'GET' && request.method !== 'OPTIONS') {
    body = await request.json();
  }
  // Try session authentication first, then fall back to legacy
  let authResult = await verifyAdminSession(request);
  if (authResult.error) {
    authResult = await verifySimpleAdmin(request);
  }

  if (authResult.error) {
    return new Response(JSON.stringify({ error: authResult.error }), {
      status: 401,
      headers: corsHeaders,
    });
  }
  try {
    switch (request.method) {
      case 'GET':
        if (queryStringParameters?.stats === 'true') {
          return await handleGetKeyStats(corsHeaders);
        }
        if (queryStringParameters?.activity === 'true') {
          return await handleGetRecentActivity(corsHeaders);
        }
        if (queryStringParameters?.health === 'true') {
          return await handleGetSystemHealth(corsHeaders);
        }
        if (queryStringParameters?.action === 'security-events') {
          return await handleSecurityEvents(corsHeaders);
        }
        if (queryStringParameters?.action === 'behavior-profiles') {
          return await handleBehaviorProfiles(corsHeaders);
        }
        return await handleGetKeys(queryStringParameters, corsHeaders);
      case 'POST':
        return await handleCreateKey(body, corsHeaders, authResult.admin);
      case 'PUT':
        return await handleUpdateKey(body, corsHeaders, authResult.admin);
      case 'DELETE':
        return await handleDeleteKey(queryStringParameters?.id, corsHeaders, authResult.admin);
      default:
        return new Response(JSON.stringify({ error: 'Method not allowed' }), {
          status: 405,
          headers: corsHeaders,
        });
    }
  } catch (error) {
    console.error('Admin keys error:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: error.message,
      method: request.method,
      url: request.url
    }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};
const handleGetKeys = async (queryParams, corsHeaders) => {
  const page = parseInt(queryParams?.page) || 1;
  const limit = parseInt(queryParams?.limit) || 50;
  const offset = (page - 1) * limit;
  const status = queryParams?.status; // 'active', 'expired', 'revoked', 'all'
  const search = queryParams?.search;
  // Simplified query to avoid issues with missing tables
  let query = supabase
    .from('license_keys')
    .select(`*, hwid_bindings(hwid_hash, roblox_username, bound_at)`)
    .order('created_at', { ascending: false });
  if (status && status !== 'all') {
    switch (status) {
      case 'active':
        query = query.eq('is_active', true).gt('expires_at', new Date().toISOString());
        break;
      case 'expired':
        query = query.lt('expires_at', new Date().toISOString());
        break;
      case 'revoked':
        query = query.eq('is_revoked', true);
        break;
    }
  }
  if (search) {
    query = query.or(`key_code.ilike.%${search}%,ip_address.ilike.%${search}%`);
  }
  query = query.range(offset, offset + limit - 1);
  const { data: keys, error } = await query;
  if (error) {
    console.error('Error fetching keys:', error);
    return new Response(JSON.stringify({
      error: 'Failed to fetch keys',
      details: error.message
    }), {
      status: 500,
      headers: corsHeaders,
    });
  }

  // Safely get total count
  const { count: totalCount, error: countError } = await supabase
    .from('license_keys')
    .select('*', { count: 'exact', head: true });

  if (countError) {
    console.warn('Error getting count:', countError);
  }
  return new Response(JSON.stringify({
    keys: keys || [],
    pagination: {
      page,
      limit,
      total: totalCount || 0,
      pages: Math.ceil((totalCount || 0) / limit)
    }
  }), {
    status: 200,
    headers: corsHeaders,
  });
};
const logKeyAction = async (keyId, action, adminUsername, details = null) => {
  if (!keyId || !action || !adminUsername) return;
  await supabase.from('key_usage_logs').insert({
    key_id: keyId,
    action,
    admin_username: adminUsername,
    details,
    created_at: new Date().toISOString(),
  });
};
const handleCreateKey = async (body, corsHeaders, admin) => {
  const { expiresInHours = 24, note } = body;
  const { data: keyCode, error: keyError } = await supabase.rpc('generate_key_code');
  if (keyError) {
    return new Response(JSON.stringify({ error: 'Failed to generate key' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
  const expiresAt = new Date(Date.now() + expiresInHours * 60 * 60 * 1000);
  const { data: key, error: insertError } = await supabase
    .from('license_keys')
    .insert({
      key_code: keyCode,
      expires_at: expiresAt.toISOString(),
      created_by_admin: true,
      is_active: true
    })
    .select()
    .single();
  if (insertError) {
    return new Response(JSON.stringify({ error: 'Failed to create key' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
  await logKeyAction(key.id, 'created', admin.username);
  return new Response(JSON.stringify({
    success: true,
    key: key.key_code,
    expires_at: key.expires_at,
    message: 'Key created successfully!'
  }), {
    status: 201,
    headers: corsHeaders,
  });
};
const handleUpdateKey = async (body, corsHeaders, admin) => {
  const { id, is_active, is_revoked, note } = body;
  if (!id) {
    return new Response(JSON.stringify({ error: 'Key ID is required' }), {
      status: 400,
      headers: corsHeaders,
    });
  }
  const updateFields = {};
  if (is_active !== undefined) updateFields.is_active = is_active;
  if (is_revoked !== undefined) updateFields.is_revoked = is_revoked;
  if (note !== undefined) updateFields.note = note;
  const { data, error } = await supabase
    .from('license_keys')
    .update(updateFields)
    .eq('id', id)
    .select();
  if (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: corsHeaders,
    });
  }
  let action = 'updated';
  if (is_revoked) action = 'revoked';
  await logKeyAction(id, action, admin.username);
  return new Response(JSON.stringify({
    success: true,
    key: data[0],
    message: 'Key updated successfully!'
  }), {
    status: 200,
    headers: corsHeaders,
  });
};
const handleDeleteKey = async (keyId, corsHeaders, admin) => {
  if (!keyId) {
    return new Response(JSON.stringify({ error: 'Key ID is required' }), {
      status: 400,
      headers: corsHeaders,
    });
  }
  const { error } = await supabase
    .from('license_keys')
    .delete()
    .eq('id', keyId);
  if (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: corsHeaders,
    });
  }
  await logKeyAction(keyId, 'deleted', admin.username);
  return new Response(JSON.stringify({
    success: true,
    message: 'Key deleted successfully!'
  }), {
    status: 200,
    headers: corsHeaders,
  });
};
const handleGetKeyStats = async (corsHeaders) => {
  try {
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const todayISO = todayStart.toISOString();

    const { count: totalKeys, error: totalError } = await supabase
      .from('license_keys')
      .select('*', { count: 'exact', head: true });

    const { count: activeKeys, error: activeError } = await supabase
      .from('license_keys')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)
      .gt('expires_at', now.toISOString());

    const { count: expiredKeys, error: expiredError } = await supabase
      .from('license_keys')
      .select('*', { count: 'exact', head: true })
      .lt('expires_at', now.toISOString());

    const { count: revokedKeys, error: revokedError } = await supabase
      .from('license_keys')
      .select('*', { count: 'exact', head: true })
      .eq('is_revoked', true);

    if (totalError || activeError || expiredError || revokedError) {
      console.warn('Error in key stats queries:', { totalError, activeError, expiredError, revokedError });
    }
    const { count: todaysKeys, error: todayError } = await supabase
      .from('license_keys')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', todayISO);

    if (todayError) {
      console.warn('Error in today keys query:', todayError);
    }

    return new Response(JSON.stringify({
      totalKeys: totalKeys || 0,
      activeKeys: activeKeys || 0,
      expiredKeys: expiredKeys || 0,
      revokedKeys: revokedKeys || 0,
      todaysKeys: todaysKeys || 0
    }), {
      status: 200,
      headers: corsHeaders,
    });
  } catch (error) {
    console.error('Error in handleGetKeyStats:', error);
    return new Response(JSON.stringify({
      error: 'Failed to fetch key statistics',
      details: error.message
    }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};
const handleGetRecentActivity = async (corsHeaders) => {
  try {
    const { data, error } = await supabase
      .from('key_usage_logs')
      .select('id, action, admin_username, details, created_at, key_id, license_keys(key_code)')
      .order('created_at', { ascending: false })
      .limit(20);

    if (error) {
      console.warn('Error fetching recent activity (table may not exist):', error);
      // Return empty activity if table doesn't exist
      return new Response(JSON.stringify({ activity: [] }), {
        status: 200,
        headers: corsHeaders,
      });
    }

    return new Response(JSON.stringify({ activity: data || [] }), {
      status: 200,
      headers: corsHeaders,
    });
  } catch (error) {
    console.error('Error in handleGetRecentActivity:', error);
    return new Response(JSON.stringify({
      error: 'Failed to fetch recent activity',
      activity: []
    }), {
      status: 200, // Return 200 with empty data instead of 500
      headers: corsHeaders,
    });
  }
};
const serverStartTime = Date.now();
const handleGetSystemHealth = async (corsHeaders) => {
  const uptimeMs = Date.now() - serverStartTime;
  const hours = Math.floor(uptimeMs / (1000 * 60 * 60));
  const minutes = Math.floor((uptimeMs % (1000 * 60 * 60)) / (1000 * 60));
  const uptime = `${hours}h ${minutes}m`;
  const { count: totalAdmins } = await supabase
    .from('admin_users')
    .select('*', { count: 'exact', head: true });
  const now = new Date();
  const { count: expiredKeys } = await supabase
    .from('license_keys')
    .select('*', { count: 'exact', head: true })
    .lt('expires_at', now.toISOString());
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const todayISO = todayStart.toISOString();
  const { count: todaysKeys } = await supabase
    .from('license_keys')
    .select('*', { count: 'exact', head: true })
    .gte('created_at', todayISO);
  return new Response(JSON.stringify({
    uptime,
    totalAdmins,
    expiredKeys,
    todaysKeys
  }), {
    status: 200,
    headers: corsHeaders,
  });
};
const handleSecurityEvents = async (corsHeaders) => {
  try {
    const { data: securityEvents, error: eventsError } = await supabase
      .from('security_events')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1000);
    const { data: behaviorProfiles, error: behaviorError } = await supabase
      .from('user_behavior_profiles')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1000);
    if (eventsError || behaviorError) {
    }
    return new Response(JSON.stringify({
      securityEvents: securityEvents || [],
      behaviorProfiles: behaviorProfiles || []
    }), {
      status: 200,
      headers: corsHeaders,
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Failed to fetch security events' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};
const handleBehaviorProfiles = async (corsHeaders) => {
  try {
    const { data: profiles, error } = await supabase
      .from('user_behavior_profiles')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(50);
    if (error) {
      return new Response(JSON.stringify({ error: 'Failed to fetch behavior profiles' }), {
        status: 500,
        headers: corsHeaders,
      });
    }
    const enhancedProfiles = (profiles || []).map(profile => ({
      id: profile.id,
      session_duration: profile.session_duration || 0,
      mouse_movement_count: profile.mouse_movements?.length || 0,
      click_count: profile.click_patterns?.length || 0,
      typing_speed_wpm: profile.typing_metrics?.wpm || 0,
      anomaly_score: profile.anomaly_score || 0,
      created_at: profile.created_at,
      fingerprint_id: profile.fingerprint_id,
      risk_level: (profile.anomaly_score || 0) > 0.7 ? 'high' :
                 (profile.anomaly_score || 0) > 0.4 ? 'medium' : 'low'
    }));
    return new Response(JSON.stringify(enhancedProfiles), {
      status: 200,
      headers: corsHeaders,
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Failed to fetch behavior profiles' }), {
      status: 500,
      headers: corsHeaders,
    });
  }
};
export default allowCors(adminKeysHandler);