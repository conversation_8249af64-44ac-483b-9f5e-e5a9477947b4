import { createClient } from '@supabase/supabase-js';
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
if (!supabaseUrl || !supabaseServiceRoleKey) {
  throw new Error('Missing Supabase environment variables');
}
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Credentials': 'true'
};
export const handler = async (event, context) => {
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers: corsHeaders, body: '' };
  }
  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }
  try {
    const userAgent = event.headers['user-agent'] || '';
    const ipAddress = event.headers['x-forwarded-for']?.split(',')[0]?.trim() || 'unknown';
    // Get user's key history based on IP address (simple approach)
    const { data: keys, error: keysError } = await supabase
      .from('license_keys')
      .select(`
        id,
        key_code,
        campaign_id,
        created_at,
        expires_at,
        last_used_at,
        usage_count,
        is_active,
        is_revoked,
        fingerprint_hash,
        last_hwid_reset,
        ip_address,
        user_agent
      `)
      .eq('ip_address', ipAddress)
      .order('created_at', { ascending: false });
    if (keysError) {
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({
          error: 'Failed to fetch key history',
          details: keysError.message || 'Unknown database error'
        })
      };
    }
    // Function to determine key status
    const determineKeyStatus = (key) => {
      if (!key.is_active || key.is_revoked) return 'revoked';
      const now = new Date();
      const expiresAt = new Date(key.expires_at);
      if (expiresAt < now) return 'expired';
      return 'active';
    };
    // Transform the data for the frontend
    const transformedKeys = (keys || []).map(key => ({
      id: key.id,
      key_id: key.key_code,
      game_name: 'Project Madara', // Default game name
      script_name: 'Universal Script', // Default script name
      generated_at: key.created_at,
      expires_at: key.expires_at,
      last_used: key.last_used_at,
      usage_count: key.usage_count || 0,
      download_count: 0, // Not tracked in current schema
      status: determineKeyStatus(key),
      hwid_hash: key.fingerprint_hash, // Use fingerprint_hash as hwid_hash
      last_hwid_reset: key.last_hwid_reset,
      metadata: {}
    }));
    // Calculate simple statistics
    const activeKeys = transformedKeys.filter(key => key.status === 'active');
    const stats = {
      totalKeys: transformedKeys.length,
      activeKeys: activeKeys.length,
      expiredKeys: transformedKeys.filter(key => key.status === 'expired').length,
      revokedKeys: transformedKeys.filter(key => key.status === 'revoked').length
    };
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        keys: transformedKeys,
        stats: stats
      })
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Internal server error',
        details: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
