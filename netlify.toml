[build]
  command = "npm run build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "18"
  NPM_FLAGS = "--legacy-peer-deps"

[functions]
  directory = "netlify/functions"

[functions."*"]
  node_bundler = "esbuild"

[dev]
  targetPort = 3000

# Clean loader URL redirect
[[redirects]]
  from = "/loader"
  to = "/.netlify/functions/loader"
  status = 200

# Alternative short URLs for loader
[[redirects]]
  from = "/script"
  to = "/.netlify/functions/loader"
  status = 200

[[redirects]]
  from = "/main"
  to = "/.netlify/functions/loader"
  status = 200

# Ensure Netlify functions are accessible (must come before SPA redirect)
[[redirects]]
  from = "/.netlify/functions/*"
  to = "/.netlify/functions/:splat"
  status = 200

# Redirect all other routes to React app (SPA) - this must be last
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200