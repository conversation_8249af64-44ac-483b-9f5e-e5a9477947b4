-- PROJECT MADARA - SECURE LOADER
-- This is the entry point that securely loads the main system
-- Usage: loadstring(game:HttpGet("https://projectmadara.com/.netlify/functions/secure-script?action=get_main_loader"))()

print("🔐 Project Madara Secure Loader - Initializing...")

-- Security checks
local function validateEnvironment()
    local requiredServices = {
        "Players", "CoreGui", "HttpService", "UserInputService", 
        "TweenService", "RbxAnalyticsService", "RunService"
    }
    
    for _, serviceName in ipairs(requiredServices) do
        local success, service = pcall(function()
            return game:GetService(serviceName)
        end)
        
        if not success then
            warn("❌ Required service not available: " .. serviceName)
            return false
        end
    end
    
    -- Check for required functions
    local requiredFunctions = {"loadstring", "pcall", "spawn", "wait"}
    for _, funcName in ipairs(requiredFunctions) do
        if not _G[funcName] then
            warn("❌ Required function not available: " .. funcName)
            return false
        end
    end
    
    return true
end

-- Anti-debug measures
local function antiDebug()
    local startTime = tick()
    wait(0.05)
    if tick() - startTime > 0.2 then
        warn("❌ Execution environment compromised")
        return false
    end
    return true
end

-- Main loader function
local function loadMainScript()
    print("🚀 Loading main Project Madara system...")
    
    local mainScriptUrl = "https://projectmadara.com/.netlify/functions/secure-script?action=get_main_loader"
    
    local success, mainScript = pcall(function()
        return game:HttpGet(mainScriptUrl)
    end)
    
    if not success then
        warn("❌ Failed to fetch main script: " .. tostring(mainScript))
        return false
    end
    
    if not mainScript or mainScript == "" then
        warn("❌ Empty response from server")
        return false
    end
    
    if string.find(mainScript, "error") or string.find(mainScript, "Error") then
        warn("❌ Server returned error: " .. mainScript)
        return false
    end
    
    -- Execute main script
    local loadSuccess, loadResult = pcall(loadstring(mainScript))
    
    if not loadSuccess then
        warn("❌ Failed to execute main script: " .. tostring(loadResult))
        return false
    end
    
    print("✅ Main script loaded successfully!")
    return true
end

-- Execute security checks and load main script
if validateEnvironment() and antiDebug() then
    spawn(function()
        if not loadMainScript() then
            warn("❌ Failed to load Project Madara")
        end
    end)
else
    warn("❌ Security validation failed")
end
