return function(correctFunction)
    local CONFIG = {
        API_BASE_URL = "https://projectmadara.com/.netlify/functions",
        VALIDATION_ENDPOINT = "/validate-key",
        KEY_FILE_NAME = "madara_key.txt",
        MAX_RETRY_ATTEMPTS = 6,
        RETRY_DELAY = 2,
        HEARTBEAT_INTERVAL = 300,
        SESSION_TIMEOUT = 7200
    }

    local SecurityUtils = {}

    function SecurityUtils.getHWID()
        local hwid = "UNKNOWN_HWID"
        local userId = game.Players.LocalPlayer.UserId
        local displayName = game.Players.LocalPlayer.DisplayName

        if gethwid then
            hwid = gethwid()
        elseif syn and syn.request then
            hwid = "SYN_" .. tostring(userId) .. "_" .. game.JobId
        elseif getgenv().identifyexecutor then
            local executor = getgenv().identifyexecutor()
            hwid = executor .. "_" .. tostring(userId) .. "_" .. game.JobId
        elseif game:GetService("RbxAnalyticsService") then
            local success, clientId = pcall(function()
                return game:GetService("RbxAnalyticsService"):GetClientId()
            end)
            if success and clientId then
                hwid = "RBX_" .. clientId
            else
                hwid = "FALLBACK_" .. tostring(userId) .. "_" .. game.JobId .. "_" .. tostring(tick())
            end
        else
            hwid = "GENERIC_" .. tostring(userId) .. "_" .. game.JobId .. "_" .. tostring(os.time())
        end
        local fingerprint = hwid .. "_" .. userId .. "_" .. displayName .. "_" .. game.PlaceId

        local additionalData = {
            game.JobId,
            tostring(workspace.DistributedGameTime),
            tostring(game.Players.LocalPlayer.AccountAge)
        }

        for _, data in ipairs(additionalData) do
            fingerprint = fingerprint .. "_" .. data
        end

        return fingerprint
    end

    function SecurityUtils.validateKeyFormat(key)
        if not key or type(key) ~= "string" then
            return false
        end

        -- Remove any whitespace
        key = string.gsub(key, "%s+", "")

        -- Check exact length: MADARA-XXXX-XXXX-XXXX = 21 characters
        if #key ~= 21 then
            return false
        end

        -- Check if it starts with "MADARA-"
        if string.sub(key, 1, 7) ~= "MADARA-" then
            return false
        end

        -- Check dash positions (should be at positions 7, 12, 17)
        if string.sub(key, 7, 7) ~= "-" or
           string.sub(key, 12, 12) ~= "-" or
           string.sub(key, 17, 17) ~= "-" then
            return false
        end

        -- Extract the three 4-character segments
        local segment1 = string.sub(key, 8, 11)   -- Characters 8-11
        local segment2 = string.sub(key, 13, 16)  -- Characters 13-16
        local segment3 = string.sub(key, 18, 21)  -- Characters 18-21

        -- Validate each segment is exactly 4 alphanumeric characters
        local function isValidSegment(segment)
            if #segment ~= 4 then
                return false
            end

            for i = 1, 4 do
                local char = string.sub(segment, i, i)
                local byte = string.byte(char)
                -- Check if character is 0-9, A-Z, or a-z
                if not ((byte >= 48 and byte <= 57) or    -- 0-9
                        (byte >= 65 and byte <= 90) or    -- A-Z
                        (byte >= 97 and byte <= 122)) then -- a-z
                    return false
                end
            end
            return true
        end

        if not isValidSegment(segment1) or not isValidSegment(segment2) or not isValidSegment(segment3) then
            return false
        end

        return true
    end

    function SecurityUtils.sanitizeInput(input)
        if not input then return "" end
        return string.gsub(tostring(input), "[^%w%-_]", "")
    end

    -- Main key validation function
    local function validateKey(keyCode, hwid, retryCount)
        retryCount = retryCount or 0

        if not SecurityUtils.validateKeyFormat(keyCode) then
            warn("Invalid key format. Expected: MADARA-XXXX-XXXX-XXXX")
            return false, "Invalid key format"
        end

        local sanitizedKey = SecurityUtils.sanitizeInput(keyCode)
        local sanitizedHwid = SecurityUtils.sanitizeInput(hwid)

        local url = CONFIG.API_BASE_URL .. CONFIG.VALIDATION_ENDPOINT
        local requestBody = game:GetService("HttpService"):JSONEncode({
            keyCode = sanitizedKey,
            hwidHash = sanitizedHwid,
            robloxUsername = game.Players.LocalPlayer.Name,
            robloxUserId = tostring(game.Players.LocalPlayer.UserId),
            fingerprint = hwid,
            heartbeat = false,
            clientInfo = {
                executor = getgenv().identifyexecutor and getgenv().identifyexecutor() or "Unknown",
                placeId = tostring(game.PlaceId),
                jobId = game.JobId,
                accountAge = game.Players.LocalPlayer.AccountAge
            }
        })

        local success, result = pcall(function()
            return game:GetService("HttpService"):PostAsync(url, requestBody, Enum.HttpContentType.ApplicationJson)
        end)

        if not success then
            warn("Network error during key validation: " .. tostring(result))

            -- Retry logic
            if retryCount < CONFIG.MAX_RETRY_ATTEMPTS then
                warn("Retrying validation in " .. CONFIG.RETRY_DELAY .. " seconds... (Attempt " .. (retryCount + 1) .. "/" .. CONFIG.MAX_RETRY_ATTEMPTS .. ")")
                wait(CONFIG.RETRY_DELAY)
                return validateKey(keyCode, hwid, retryCount + 1)
            end

            return false, "Network connection failed after " .. CONFIG.MAX_RETRY_ATTEMPTS .. " attempts"
        end

        local parseSuccess, responseData = pcall(function()
            return game:GetService("HttpService"):JSONDecode(result)
        end)

        if not parseSuccess then
            warn("Invalid response format from validation server")
            return false, "Server response error"
        end

        if responseData.success and responseData.valid then
            print("✅ Key validation successful! Access granted.")
            print("📅 Key expires at: " .. (responseData.expires_at or "Unknown"))
            print("📊 Usage count: " .. (responseData.usage_count or 0))

            if responseData.newly_bound then
                print("🔗 Device successfully bound to key")
            end

            if responseData.roblox_hwid_updated then
                print("🎮 Roblox HWID updated in database")
            end

            return true, responseData
        else
            local errorMsg = responseData.error or "Key validation failed"

            -- Provide specific error messages for common issues
            if string.find(errorMsg:lower(), "hwid") then
                errorMsg = "🔒 " .. errorMsg .. "\n💡 Use the dashboard to reset your HWID if needed"
            elseif string.find(errorMsg:lower(), "expired") then
                errorMsg = "⏰ " .. errorMsg .. "\n💡 Please generate a new key"
            elseif string.find(errorMsg:lower(), "invalid") then
                errorMsg = "❌ " .. errorMsg .. "\n💡 Check your key format: MADARA-XXXX-XXXX-XXXX"
            else
                errorMsg = "⚠️ " .. errorMsg
            end

            warn("Key validation failed: " .. errorMsg)
            return false, errorMsg
        end
    end

    -- Heartbeat validation function for ongoing session validation
    local function startHeartbeat(keyCode, hwid)
        spawn(function()
            while true do
                wait(CONFIG.HEARTBEAT_INTERVAL)

                local url = CONFIG.API_BASE_URL .. CONFIG.VALIDATION_ENDPOINT
                local heartbeatBody = game:GetService("HttpService"):JSONEncode({
                    keyCode = SecurityUtils.sanitizeInput(keyCode),
                    hwidHash = SecurityUtils.sanitizeInput(hwid),
                    robloxUsername = game.Players.LocalPlayer.Name,
                    robloxUserId = tostring(game.Players.LocalPlayer.UserId),
                    fingerprint = hwid,
                    heartbeat = true,
                    clientInfo = {
                        executor = getgenv().identifyexecutor and getgenv().identifyexecutor() or "Unknown",
                        placeId = tostring(game.PlaceId),
                        jobId = game.JobId,
                        accountAge = game.Players.LocalPlayer.AccountAge,
                        sessionTime = tostring(tick())
                    }
                })

                local success, result = pcall(function()
                    return game:GetService("HttpService"):PostAsync(url, heartbeatBody, Enum.HttpContentType.ApplicationJson)
                end)

                if success then
                    local parseSuccess, responseData = pcall(function()
                        return game:GetService("HttpService"):JSONDecode(result)
                    end)

                    if parseSuccess and responseData.success and responseData.valid then
                        print("Heartbeat: Session valid")
                    else
                        warn("Heartbeat failed: " .. (responseData.error or "Unknown error"))
                        warn("Session expired. Please restart the script.")
                        break
                    end
                else
                    warn("Heartbeat network error: " .. tostring(result))
                end
            end
        end)
    end

    -- Main execution logic
    local function executeKeySystem()
        local hwid = SecurityUtils.getHWID()
        local keySystemEnabled = true
        local validationResult = false
        local validationMessage = ""

        print("🚀 Project Madara Key System v2.1")
        print("🔧 Enhanced HWID & Roblox HWID Support")
        print("🔍 HWID: " .. string.sub(hwid, 1, 20) .. "...")

        -- Check for saved key file first
        if isfile(CONFIG.KEY_FILE_NAME) then
            local savedKey = readfile(CONFIG.KEY_FILE_NAME)
            if savedKey and #savedKey > 0 then
                print("📁 Found saved key, validating...")
                local success, result = validateKey(savedKey, hwid)
                if success then
                    validationResult = true
                    print("✅ Saved key is valid! Access granted.")

                    -- Start heartbeat validation for ongoing session monitoring
                    startHeartbeat(savedKey, hwid)

                    correctFunction()
                    return
                else
                    warn("❌ Saved key is invalid: " .. result)
                    -- Delete invalid key file
                    delfile(CONFIG.KEY_FILE_NAME)
                    print("🗑️ Removed invalid saved key")
                end
            end
        end

        local globalKey = getgenv().madara_key or getgenv().key
        if globalKey then
            print("🌐 Found global key, validating...")
            local success, result = validateKey(globalKey, hwid)
            if success then
                validationResult = true
                print("✅ Global key is valid! Access granted.")
                -- Save valid key for future use
                writefile(CONFIG.KEY_FILE_NAME, globalKey)
                print("💾 Key saved for future use")

                -- Start heartbeat validation for ongoing session monitoring
                startHeartbeat(globalKey, hwid)

                correctFunction()
                return
            else
                warn("❌ Global key is invalid: " .. result)
            end
        end

        -- If no valid key found, show key system UI
        if not validationResult and keySystemEnabled then
            showKeySystemUI(hwid)
        elseif not keySystemEnabled then
            print(" Key system is temporarily disabled. Access granted.")
            correctFunction()
        else
            warn(" No valid key found. Access denied.")
        end
    end

    -- Enhanced Key System UI
    local function showKeySystemUI(hwid)
        -- Create secure UI with modern design
        local MadaraUI = Instance.new("ScreenGui")
        MadaraUI.Name = "MadaraKeySystem"
        MadaraUI.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
        MadaraUI.ResetOnSpawn = false

        -- Protect GUI from detection
        local function protectGUI(gui)
            if gethui then
                gui.Parent = gethui()
            elseif syn and syn.protect_gui then
                syn.protect_gui(gui)
                gui.Parent = game.CoreGui
            else
                gui.Parent = game.CoreGui
            end
        end
        protectGUI(MadaraUI)

        -- Main container with glassmorphism effect
        local MainFrame = Instance.new("Frame")
        MainFrame.Name = "MainFrame"
        MainFrame.AnchorPoint = Vector2.new(0.5, 0.5)
        MainFrame.Position = UDim2.new(0.5, 0, 0.5, 0)
        MainFrame.Size = UDim2.new(0, 420, 0, 280)
        MainFrame.BackgroundColor3 = Color3.fromRGB(15, 15, 20)
        MainFrame.BackgroundTransparency = 0.1
        MainFrame.BorderSizePixel = 0
        MainFrame.Parent = MadaraUI

        local MainCorner = Instance.new("UICorner")
        MainCorner.CornerRadius = UDim.new(0, 12)
        MainCorner.Parent = MainFrame

        local MainStroke = Instance.new("UIStroke")
        MainStroke.Color = Color3.fromRGB(60, 60, 80)
        MainStroke.Thickness = 1
        MainStroke.Transparency = 0.5
        MainStroke.Parent = MainFrame

        -- Header section
        local HeaderFrame = Instance.new("Frame")
        HeaderFrame.Name = "Header"
        HeaderFrame.Size = UDim2.new(1, 0, 0, 60)
        HeaderFrame.BackgroundColor3 = Color3.fromRGB(25, 25, 35)
        HeaderFrame.BorderSizePixel = 0
        HeaderFrame.Parent = MainFrame

        local HeaderCorner = Instance.new("UICorner")
        HeaderCorner.CornerRadius = UDim.new(0, 12)
        HeaderCorner.Parent = HeaderFrame

        -- Fix corner clipping
        local HeaderMask = Instance.new("Frame")
        HeaderMask.Size = UDim2.new(1, 0, 0, 30)
        HeaderMask.Position = UDim2.new(0, 0, 1, -30)
        HeaderMask.BackgroundColor3 = Color3.fromRGB(25, 25, 35)
        HeaderMask.BorderSizePixel = 0
        HeaderMask.Parent = HeaderFrame

        -- Title and close button
        local TitleLabel = Instance.new("TextLabel")
        TitleLabel.Name = "Title"
        TitleLabel.Position = UDim2.new(0, 20, 0, 0)
        TitleLabel.Size = UDim2.new(1, -80, 1, 0)
        TitleLabel.BackgroundTransparency = 1
        TitleLabel.Text = "🔐 Project Madara - Key System"
        TitleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        TitleLabel.TextSize = 18
        TitleLabel.TextXAlignment = Enum.TextXAlignment.Left
        TitleLabel.Font = Enum.Font.GothamBold
        TitleLabel.Parent = HeaderFrame

        local CloseButton = Instance.new("TextButton")
        CloseButton.Name = "CloseButton"
        CloseButton.Position = UDim2.new(1, -50, 0, 10)
        CloseButton.Size = UDim2.new(0, 40, 0, 40)
        CloseButton.BackgroundColor3 = Color3.fromRGB(40, 40, 50)
        CloseButton.BorderSizePixel = 0
        CloseButton.Text = "✕"
        CloseButton.TextColor3 = Color3.fromRGB(200, 200, 200)
        CloseButton.TextSize = 16
        CloseButton.Font = Enum.Font.GothamBold
        CloseButton.Parent = HeaderFrame

        local CloseCorner = Instance.new("UICorner")
        CloseCorner.CornerRadius = UDim.new(0, 8)
        CloseCorner.Parent = CloseButton

        -- Status label
        local StatusLabel = Instance.new("TextLabel")
        StatusLabel.Name = "Status"
        StatusLabel.Position = UDim2.new(0, 20, 0, 80)
        StatusLabel.Size = UDim2.new(1, -40, 0, 30)
        StatusLabel.BackgroundTransparency = 1
        StatusLabel.Text = "Enter your key to access Project Madara"
        StatusLabel.TextColor3 = Color3.fromRGB(180, 180, 180)
        StatusLabel.TextSize = 14
        StatusLabel.TextXAlignment = Enum.TextXAlignment.Left
        StatusLabel.Font = Enum.Font.Gotham
        StatusLabel.Parent = MainFrame

        -- Key input field
        local KeyInput = Instance.new("TextBox")
        KeyInput.Name = "KeyInput"
        KeyInput.Position = UDim2.new(0, 20, 0, 120)
        KeyInput.Size = UDim2.new(1, -40, 0, 45)
        KeyInput.BackgroundColor3 = Color3.fromRGB(30, 30, 40)
        KeyInput.BorderSizePixel = 0
        KeyInput.Text = ""
        KeyInput.PlaceholderText = "Enter your key (MADARA-XXXX-XXXX-XXXX)"
        KeyInput.PlaceholderColor3 = Color3.fromRGB(120, 120, 120)
        KeyInput.TextColor3 = Color3.fromRGB(255, 255, 255)
        KeyInput.TextSize = 14
        KeyInput.Font = Enum.Font.Gotham
        KeyInput.TextXAlignment = Enum.TextXAlignment.Left
        KeyInput.ClearTextOnFocus = false
        KeyInput.Parent = MainFrame

        local KeyInputCorner = Instance.new("UICorner")
        KeyInputCorner.CornerRadius = UDim.new(0, 8)
        KeyInputCorner.Parent = KeyInput

        local KeyInputStroke = Instance.new("UIStroke")
        KeyInputStroke.Color = Color3.fromRGB(60, 60, 80)
        KeyInputStroke.Thickness = 1
        KeyInputStroke.Transparency = 0.5
        KeyInputStroke.Parent = KeyInput

        local KeyInputPadding = Instance.new("UIPadding")
        KeyInputPadding.PaddingLeft = UDim.new(0, 15)
        KeyInputPadding.PaddingRight = UDim.new(0, 15)
        KeyInputPadding.Parent = KeyInput

        -- Button container
        local ButtonContainer = Instance.new("Frame")
        ButtonContainer.Name = "ButtonContainer"
        ButtonContainer.Position = UDim2.new(0, 20, 0, 180)
        ButtonContainer.Size = UDim2.new(1, -40, 0, 40)
        ButtonContainer.BackgroundTransparency = 1
        ButtonContainer.Parent = MainFrame

        -- Validate Key Button
        local ValidateButton = Instance.new("TextButton")
        ValidateButton.Name = "ValidateButton"
        ValidateButton.Position = UDim2.new(0, 0, 0, 0)
        ValidateButton.Size = UDim2.new(0.48, 0, 1, 0)
        ValidateButton.BackgroundColor3 = Color3.fromRGB(50, 150, 80)
        ValidateButton.BorderSizePixel = 0
        ValidateButton.Text = "🔑 Validate Key"
        ValidateButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        ValidateButton.TextSize = 14
        ValidateButton.Font = Enum.Font.GothamBold
        ValidateButton.Parent = ButtonContainer

        local ValidateCorner = Instance.new("UICorner")
        ValidateCorner.CornerRadius = UDim.new(0, 8)
        ValidateCorner.Parent = ValidateButton

        -- Get Key Button
        local GetKeyButton = Instance.new("TextButton")
        GetKeyButton.Name = "GetKeyButton"
        GetKeyButton.Position = UDim2.new(0.52, 0, 0, 0)
        GetKeyButton.Size = UDim2.new(0.48, 0, 1, 0)
        GetKeyButton.BackgroundColor3 = Color3.fromRGB(80, 120, 200)
        GetKeyButton.BorderSizePixel = 0
        GetKeyButton.Text = "🌐 Get Key"
        GetKeyButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        GetKeyButton.TextSize = 14
        GetKeyButton.Font = Enum.Font.GothamBold
        GetKeyButton.Parent = ButtonContainer

        local GetKeyCorner = Instance.new("UICorner")
        GetKeyCorner.CornerRadius = UDim.new(0, 8)
        GetKeyCorner.Parent = GetKeyButton

        -- Discord button
        local DiscordButton = Instance.new("TextButton")
        DiscordButton.Name = "DiscordButton"
        DiscordButton.Position = UDim2.new(0, 20, 0, 235)
        DiscordButton.Size = UDim2.new(1, -40, 0, 30)
        DiscordButton.BackgroundColor3 = Color3.fromRGB(88, 101, 242)
        DiscordButton.BorderSizePixel = 0
        DiscordButton.Text = "Join Discord for Support"
        DiscordButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        DiscordButton.TextSize = 12
        DiscordButton.Font = Enum.Font.Gotham
        DiscordButton.Parent = MainFrame

        local DiscordCorner = Instance.new("UICorner")
        DiscordCorner.CornerRadius = UDim.new(0, 6)
        DiscordCorner.Parent = DiscordButton

        -- Simple notification system
        local function showNotification(message, duration, isError)
            duration = duration or 3
            local color = isError and Color3.fromRGB(220, 50, 50) or Color3.fromRGB(50, 200, 50)

            StatusLabel.Text = message
            StatusLabel.TextColor3 = color

            -- Reset after duration
            task.wait(duration)
            StatusLabel.Text = "Enter your key to access Project Madara"
            StatusLabel.TextColor3 = Color3.fromRGB(180, 180, 180)
        end

        -- Event handlers
        local function handleValidateKey()
            local keyText = KeyInput.Text:gsub("%s+", "") -- Remove whitespace

            if not keyText or #keyText == 0 then
                spawn(function() showNotification("Please enter a key", 3, true) end)
                return
            end

            if not SecurityUtils.validateKeyFormat(keyText) then
                spawn(function() showNotification("Invalid key format. Use: MADARA-XXXX-XXXX-XXXX", 3, true) end)
                return
            end

            ValidateButton.Text = "Validating..."
            ValidateButton.BackgroundColor3 = Color3.fromRGB(100, 100, 100)

            spawn(function()
                local success, result = validateKey(keyText, hwid)

                if success then
                    showNotification("✅ Key validated successfully! Access granted.", 2, false)
                    writefile(CONFIG.KEY_FILE_NAME, keyText)
                    print("💾 Key saved for future use")

                    -- Start heartbeat validation for ongoing session monitoring
                    startHeartbeat(keyText, hwid)

                    wait(1)
                    MadaraUI:Destroy()
                    correctFunction()
                else
                    -- Clean up the error message for UI display
                    local cleanError = string.gsub(result, "\n.*", "") -- Remove additional lines for UI
                    showNotification("❌ " .. cleanError, 4, true)
                    ValidateButton.Text = "🔑 Validate Key"
                    ValidateButton.BackgroundColor3 = Color3.fromRGB(50, 150, 80)
                end
            end)
        end

        local function handleGetKey()
            setclipboard("https://projectmadara.com/generate-key")
            spawn(function() showNotification("📋 Key website copied to clipboard!", 3, false) end)
        end

        local function handleDiscord()
            setclipboard("https://discord.gg/madara") -- Replace with actual Discord invite
            spawn(function() showNotification("📋 Discord invite copied to clipboard!", 3, false) end)
        end

        local function handleClose()
            MadaraUI:Destroy()
            warn("Key system closed by user. Access denied.")
        end

        -- Connect events
        ValidateButton.MouseButton1Click:Connect(handleValidateKey)
        GetKeyButton.MouseButton1Click:Connect(handleGetKey)
        DiscordButton.MouseButton1Click:Connect(handleDiscord)
        CloseButton.MouseButton1Click:Connect(handleClose)

        -- Enter key support
        KeyInput.FocusLost:Connect(function(enterPressed)
            if enterPressed then
                handleValidateKey()
            end
        end)

        -- Make UI draggable
        local function makeDraggable(frame)
            local UserInputService = game:GetService("UserInputService")
            local dragging = false
            local dragInput, dragStart, startPos

            local function update(input)
                local delta = input.Position - dragStart
                frame.Position = UDim2.new(startPos.X.Scale, startPos.X.Offset + delta.X, startPos.Y.Scale, startPos.Y.Offset + delta.Y)
            end

            HeaderFrame.InputBegan:Connect(function(input)
                if input.UserInputType == Enum.UserInputType.MouseButton1 or input.UserInputType == Enum.UserInputType.Touch then
                    dragging = true
                    dragStart = input.Position
                    startPos = frame.Position

                    input.Changed:Connect(function()
                        if input.UserInputState == Enum.UserInputState.End then
                            dragging = false
                        end
                    end)
                end
            end)

            HeaderFrame.InputChanged:Connect(function(input)
                if input.UserInputType == Enum.UserInputType.MouseMovement or input.UserInputType == Enum.UserInputType.Touch then
                    dragInput = input
                end
            end)

            UserInputService.InputChanged:Connect(function(input)
                if input == dragInput and dragging then
                    update(input)
                end
            end)
        end

        makeDraggable(MainFrame)
    end

    -- Execute the key system
    executeKeySystem()
end

--[[
USAGE:
   getgenv().madara_key = "MADARA-XXXX-XXXX-XXXX"
   local keyValidator = loadstring(game:HttpGet("https://projectmadara.com/roblox_key_validator.lua"))()
   keyValidator(function()
       print("✅ Access granted! Loading script...")
       -- Your script code here
   end)
]]