import { MadaraEmbedBuilder } from './embedBuilder.js';
import { logError, logDiscordEvent } from './logger.js';

/**
 * Global error handler for Discord bot
 */
export class ErrorHandler {
  /**
   * Handle command execution errors
   */
  static async handleCommandError(error, message, commandName) {
    logError(error, {
      context: 'command_execution',
      command: commandName,
      userId: message.author.id,
      guildId: message.guild?.id,
      channelId: message.channel.id
    });

    // Determine error type and response
    let embed;
    
    if (error.name === 'DiscordAPIError') {
      embed = this.handleDiscordAPIError(error);
    } else if (error.message.includes('Missing Permissions')) {
      embed = this.handlePermissionError(error);
    } else if (error.message.includes('Rate limit')) {
      embed = this.handleRateLimitError(error);
    } else {
      embed = this.handleGenericError(error);
    }

    try {
      await message.reply({ embeds: [embed] });
    } catch (replyError) {
      // If we can't reply, try to send to channel
      try {
        await message.channel.send({ embeds: [embed] });
      } catch (channelError) {
        logError(channelError, { context: 'error_response_failed' });
      }
    }
  }

  /**
   * Handle Discord API errors
   */
  static handleDiscordAPIError(error) {
    const errorMessages = {
      50001: 'Missing access to perform this action.',
      50013: 'Missing permissions to perform this action.',
      50035: 'Invalid form body or message content.',
      10008: 'Unknown message.',
      10003: 'Unknown channel.',
      30007: 'Maximum number of webhooks reached.',
      30005: 'Maximum number of reactions reached.'
    };

    const message = errorMessages[error.code] || 'Discord API error occurred.';
    
    return MadaraEmbedBuilder.createErrorEmbed(
      'Discord API Error',
      `${message}\n\nError Code: ${error.code}`
    );
  }

  /**
   * Handle permission errors
   */
  static handlePermissionError(error) {
    return MadaraEmbedBuilder.createErrorEmbed(
      'Permission Error',
      'The bot lacks the necessary permissions to perform this action. Please contact a server administrator.'
    );
  }

  /**
   * Handle rate limit errors
   */
  static handleRateLimitError(error) {
    return MadaraEmbedBuilder.createWarningEmbed(
      'Rate Limited',
      'The bot is being rate limited by Discord. Please try again in a few moments.'
    );
  }

  /**
   * Handle generic errors
   */
  static handleGenericError(error) {
    // Don't expose internal error details to users
    return MadaraEmbedBuilder.createErrorEmbed(
      'Unexpected Error',
      'An unexpected error occurred. The issue has been logged and will be investigated.'
    );
  }

  /**
   * Handle AI service errors
   */
  static handleAIError(error, context = {}) {
    logError(error, {
      context: 'ai_service_error',
      ...context
    });

    if (error.message.includes('API key')) {
      return MadaraEmbedBuilder.createErrorEmbed(
        'AI Service Configuration Error',
        'The AI service is not properly configured. Please contact PROJECT MADARA support.'
      );
    }

    if (error.message.includes('quota') || error.message.includes('rate limit')) {
      return MadaraEmbedBuilder.createWarningEmbed(
        'AI Service Busy',
        'The AI service is currently busy. Please try again in a few moments.'
      );
    }

    if (error.message.includes('timeout')) {
      return MadaraEmbedBuilder.createWarningEmbed(
        'AI Service Timeout',
        'The AI service took too long to respond. Please try again with a shorter message.'
      );
    }

    return MadaraEmbedBuilder.createErrorEmbed(
      'AI Service Error',
      'The AI service encountered an error. Please try again later.'
    );
  }

  /**
   * Handle validation errors
   */
  static handleValidationError(error, field) {
    return MadaraEmbedBuilder.createErrorEmbed(
      'Validation Error',
      `Invalid ${field}: ${error.message}`
    );
  }

  /**
   * Log and handle uncaught exceptions
   */
  static setupGlobalErrorHandlers() {
    process.on('uncaughtException', (error) => {
      logError(error, { context: 'uncaught_exception' });
      
      // Don't exit immediately, give time for logging
      setTimeout(() => {
        process.exit(1);
      }, 1000);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logError(new Error(`Unhandled Rejection: ${reason}`), {
        context: 'unhandled_rejection',
        promise: promise.toString()
      });
    });
  }

  /**
   * Create maintenance mode embed
   */
  static createMaintenanceEmbed() {
    return MadaraEmbedBuilder.createWarningEmbed(
      'Maintenance Mode',
      'PROJECT MADARA Bot is currently undergoing maintenance. Some features may be temporarily unavailable.'
    );
  }

  /**
   * Create service unavailable embed
   */
  static createServiceUnavailableEmbed(service) {
    return MadaraEmbedBuilder.createErrorEmbed(
      'Service Unavailable',
      `The ${service} service is currently unavailable. Please try again later.`
    );
  }
}

// Initialize global error handlers
ErrorHandler.setupGlobalErrorHandlers();

export default ErrorHandler;
