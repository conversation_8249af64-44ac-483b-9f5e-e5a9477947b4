import { EmbedBuilder } from 'discord.js';
import { config } from './config.js';

/**
 * PROJECT MADARA themed embed builder utility
 */
export class MadaraEmbedBuilder {
  /**
   * Create a standard PROJECT MADARA embed
   */
  static createEmbed(options = {}) {
    const embed = new EmbedBuilder()
      .setColor(options.color || config.bot.embedColor)
      .setFooter({ 
        text: options.footerText || config.bot.footerText,
        iconURL: options.footerIcon || config.bot.iconUrl
      })
      .setTimestamp();

    if (options.title) embed.setTitle(options.title);
    if (options.description) embed.setDescription(options.description);
    if (options.thumbnail) embed.setThumbnail(options.thumbnail);
    if (options.image) embed.setImage(options.image);
    if (options.author) embed.setAuthor(options.author);
    if (options.url) embed.setURL(options.url);

    if (options.fields && Array.isArray(options.fields)) {
      embed.addFields(options.fields);
    }

    return embed;
  }

  /**
   * Create success embed
   */
  static createSuccessEmbed(title, description) {
    return this.createEmbed({
      title: `✅ ${title}`,
      description,
      color: '#00FF00'
    });
  }

  /**
   * Create error embed with enhanced user experience
   */
  static createErrorEmbed(title, description, options = {}) {
    const fields = [];
    const isSerious = options.errorType === 'auth' || options.errorType === 'model' || options.errorType === 'network';

    // Add retry information if provided
    if (options.retryAfter && options.showRetryTime) {
      const retryTime = Math.ceil(options.retryAfter / 60);
      const timeUnit = retryTime === 1 ? 'minute' : 'minutes';
      fields.push({
        name: isSerious ? 'Retry Information' : '⏰ Try Again',
        value: `Please wait about ${retryTime} ${timeUnit} before trying again.`,
        inline: false
      });
    }

    // Add helpful tips based on error type
    if (options.errorType) {
      const tips = this.getErrorTips(options.errorType);
      if (tips) {
        fields.push({
          name: isSerious ? 'Information' : '💡 Helpful Tips',
          value: tips,
          inline: false
        });
      }
    }

    return this.createEmbed({
      title: title,
      description: description,
      color: '#FF6B6B', // Softer red color
      fields: fields
    });
  }

  /**
   * Create success embed for positive feedback
   */
  static createSuccessEmbed(title, description, options = {}) {
    const fields = [];

    if (options.nextSteps) {
      fields.push({
        name: '🎯 Next Steps',
        value: options.nextSteps,
        inline: false
      });
    }

    return this.createEmbed({
      title: `✅ ${title}`,
      description: description,
      color: '#00D26A', // Green for success
      fields: fields,
      thumbnail: options.thumbnail || config.bot.iconUrl
    });
  }

  /**
   * Create info embed for general information
   */
  static createInfoEmbed(title, description, options = {}) {
    return this.createEmbed({
      title: `ℹ️ ${title}`,
      description: description,
      color: '#3498DB', // Blue for info
      fields: options.fields || [],
      thumbnail: options.thumbnail || config.bot.iconUrl
    });
  }

  /**
   * Get helpful tips based on error type
   */
  static getErrorTips(errorType) {
    const tips = {
      'rate_limit': '• Try asking shorter questions\n• Wait a few minutes between requests\n• Use simple language',
      'credits': '• This is temporary - Sabin will refill credits soon!\n• Try again in an hour\n• Check back later',
      'auth': '• This is a configuration issue on our end\n• Sabin will fix this soon\n• No action needed from you',
      'model': '• The AI model needs updating\n• This will be fixed automatically\n• Try again later',
      'timeout': '• Try asking shorter questions\n• Break complex questions into parts\n• Use simpler language',
      'network': '• Check your internet connection\n• Try again in a moment\n• The issue might be temporary',
      'content': '• Keep messages family-friendly\n• Avoid inappropriate language\n• Focus on coding and helpful topics'
    };

    return tips[errorType] || null;
  }

  /**
   * Create warning embed
   */
  static createWarningEmbed(title, description) {
    return this.createEmbed({
      title: `⚠️ ${title}`,
      description,
      color: '#FFA500'
    });
  }

  /**
   * Create info embed
   */
  static createInfoEmbed(title, description) {
    return this.createEmbed({
      title: `ℹ️ ${title}`,
      description,
      color: '#0099FF'
    });
  }

  /**
   * Create FAQ response embed
   */
  static createFAQEmbed(prompt, faqMatch) {
    const embed = this.createEmbed({
      title: '📚 FAQ Answer',
      description: faqMatch.response,
      fields: [
        {
          name: '❓ Your Question',
          value: prompt.length > 150 ? `${prompt.substring(0, 150)}...` : prompt,
          inline: false
        },
        {
          name: '🎯 Matched FAQ',
          value: faqMatch.question,
          inline: false
        },
        {
          name: '📊 Match Details',
          value: `**Confidence:** ${Math.round(faqMatch.confidence * 100)}%\n**Category:** ${faqMatch.category}\n**Type:** ${faqMatch.matchType}`,
          inline: true
        }
      ],
      color: '#00FF00', // Green for FAQ matches
      thumbnail: config.bot.iconUrl
    });

    return embed;
  }

  /**
   * Create AI response embed
   */
  static createAIEmbed(prompt, response, model, codeAnalysis = null, faqMatch = null, suggestions = [], contentType = 'neutral') {
    // Determine the title and styling based on content type and context
    let title = 'MADARA AI';
    let color = '#FF6B35'; // PROJECT MADARA orange
    let emoji = '';

    // Only add emojis for casual/fun content
    const isCasualContent = contentType === 'casual' || contentType === 'media' || contentType === 'greeting';

    if (codeAnalysis && codeAnalysis.hasCode) {
      if (codeAnalysis.errors && codeAnalysis.errors.length > 0) {
        title = 'MADARA AI • Code Fix';
        color = '#00D26A'; // Green for fixes
        emoji = isCasualContent ? '� ' : '';
      } else if (codeAnalysis.language === 'lua') {
        title = 'MADARA AI • Lua Help';
        color = '#0099FF'; // Blue for Lua
        emoji = isCasualContent ? '� ' : '';
      } else {
        title = 'MADARA AI • Code Help';
        color = '#9932CC'; // Purple for general coding
        emoji = isCasualContent ? '💻 ' : '';
      }
    } else if (faqMatch) {
      title = 'MADARA AI • Quick Answer';
      color = '#FFD700'; // Gold for FAQ
      emoji = isCasualContent ? '💡 ' : '';
    } else if (isCasualContent) {
      title = '😊 MADARA AI';
      emoji = '💬 ';
    }

    title = emoji + title;

    // Add some flair to the response
    const responseWithFlair = this.addResponseFlair(response, codeAnalysis);

    const fields = [
      {
        name: isCasualContent ? '💭 Your Question' : 'Your Question',
        value: prompt.length > 150 ? `${prompt.substring(0, 150)}...` : prompt,
        inline: false
      }
    ];

    const embed = this.createEmbed({
      title,
      description: responseWithFlair,
      fields,
      thumbnail: config.bot.iconUrl,
      color,
      author: {
        name: 'PROJECT MADARA',
        iconURL: config.bot.iconUrl,
        url: 'https://your-website.com' // Add your website URL here
      }
    });

    // Add code analysis info if available
    if (codeAnalysis && codeAnalysis.hasCode) {
      let analysisText = `**Language:** ${codeAnalysis.language.toUpperCase()}`;

      if (codeAnalysis.errors && codeAnalysis.errors.length > 0) {
        analysisText += `\n**Errors Fixed:** ${codeAnalysis.errors.length}`;
      }

      if (codeAnalysis.codeBlocks && codeAnalysis.codeBlocks.length > 0) {
        analysisText += `\n**Code Blocks:** ${codeAnalysis.codeBlocks.length}`;
      }

      embed.addFields([{
        name: '🔍 Code Analysis',
        value: analysisText,
        inline: true
      }]);
    }

    // Add smart suggestions if available
    if (suggestions && suggestions.length > 0) {
      embed.addFields([{
        name: '💡 Smart Suggestions',
        value: suggestions.map(suggestion => `• ${suggestion}`).join('\n'),
        inline: false
      }]);
    }

    // Add helpful footer with PROJECT MADARA branding (context-aware)
    let footerTexts;

    if (isCasualContent) {
      footerTexts = [
        '� PROJECT MADARA • Your friendly assistant!',
        '🎉 PROJECT MADARA • Here to help and chat!',
        '� PROJECT MADARA • Always happy to help!'
      ];
    } else {
      footerTexts = [
        'PROJECT MADARA • Professional coding assistance',
        'PROJECT MADARA • Reliable Roblox scripting help',
        'PROJECT MADARA • Quality code solutions',
        'PROJECT MADARA • Expert programming support'
      ];
    }

    const randomFooter = footerTexts[Math.floor(Math.random() * footerTexts.length)];

    embed.setFooter({
      text: randomFooter,
      iconURL: config.bot.iconUrl
    });

    return embed;
  }

  /**
   * Format code blocks with proper syntax highlighting
   */
  static formatCodeBlock(code, language = '') {
    // Clean up the code
    const cleanCode = code.trim();

    // Detect language if not provided
    if (!language) {
      language = this.detectCodeLanguage(cleanCode);
    }

    // Format with syntax highlighting
    return `\`\`\`${language}\n${cleanCode}\n\`\`\``;
  }

  /**
   * Detect programming language from code content
   */
  static detectCodeLanguage(code) {
    const lowerCode = code.toLowerCase();

    // Lua/Luau detection
    if (lowerCode.includes('local ') || lowerCode.includes('function ') ||
        lowerCode.includes('end') || lowerCode.includes('game:') ||
        lowerCode.includes('workspace') || lowerCode.includes('script.')) {
      return 'lua';
    }

    // JavaScript detection
    if (lowerCode.includes('const ') || lowerCode.includes('let ') ||
        lowerCode.includes('var ') || lowerCode.includes('function(') ||
        lowerCode.includes('=>') || lowerCode.includes('console.log')) {
      return 'javascript';
    }

    // Python detection
    if (lowerCode.includes('def ') || lowerCode.includes('import ') ||
        lowerCode.includes('print(') || lowerCode.includes('if __name__')) {
      return 'python';
    }

    // C++ detection
    if (lowerCode.includes('#include') || lowerCode.includes('std::') ||
        lowerCode.includes('cout') || lowerCode.includes('int main')) {
      return 'cpp';
    }

    // Java detection
    if (lowerCode.includes('public class') || lowerCode.includes('public static void main') ||
        lowerCode.includes('system.out.println')) {
      return 'java';
    }

    // Default to generic code highlighting
    return 'text';
  }

  /**
   * Enhanced AI response formatting with code highlighting
   */
  static formatAIResponse(response) {
    // Split response into parts (text and code blocks)
    const parts = [];
    let currentText = '';
    let inCodeBlock = false;
    let codeLanguage = '';
    let codeContent = '';

    const lines = response.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Check for code block start
      if (line.trim().startsWith('```')) {
        if (!inCodeBlock) {
          // Starting a code block
          if (currentText.trim()) {
            parts.push({ type: 'text', content: currentText.trim() });
            currentText = '';
          }
          inCodeBlock = true;
          codeLanguage = line.replace('```', '').trim();
          codeContent = '';
        } else {
          // Ending a code block
          if (codeContent.trim()) {
            parts.push({
              type: 'code',
              content: codeContent.trim(),
              language: codeLanguage || this.detectCodeLanguage(codeContent)
            });
          }
          inCodeBlock = false;
          codeLanguage = '';
          codeContent = '';
        }
      } else if (inCodeBlock) {
        codeContent += line + '\n';
      } else {
        currentText += line + '\n';
      }
    }

    // Add remaining text
    if (currentText.trim()) {
      parts.push({ type: 'text', content: currentText.trim() });
    }

    // Add remaining code if block wasn't closed
    if (inCodeBlock && codeContent.trim()) {
      parts.push({
        type: 'code',
        content: codeContent.trim(),
        language: codeLanguage || this.detectCodeLanguage(codeContent)
      });
    }

    // Reconstruct response with proper formatting
    let formattedResponse = '';
    for (const part of parts) {
      if (part.type === 'text') {
        formattedResponse += part.content + '\n\n';
      } else if (part.type === 'code') {
        formattedResponse += this.formatCodeBlock(part.content, part.language) + '\n\n';
      }
    }

    return formattedResponse.trim();
  }

  /**
   * Add some flair to AI responses
   */
  static addResponseFlair(response, codeAnalysis = null) {
    // Format the response with enhanced code highlighting
    const formattedResponse = this.formatAIResponse(response);

    // Don't modify if response already has emojis or is very short
    if (formattedResponse.includes('🔥') || formattedResponse.includes('⚡') || formattedResponse.length < 50) {
      return formattedResponse;
    }

    // Keep responses clean and simple
    return formattedResponse;
  }

  /**
   * Create help embed
   */
  static createHelpEmbed(commands) {
    const fields = commands.map(cmd => ({
      name: `${config.discord.prefix}${cmd.name}`,
      value: cmd.description,
      inline: true
    }));

    return this.createEmbed({
      title: '🔧 PROJECT MADARA Bot Commands',
      description: 'Here are all available commands for the PROJECT MADARA Discord bot:',
      fields,
      thumbnail: config.bot.iconUrl
    });
  }

  /**
   * Create rate limit embed
   */
  static createRateLimitEmbed(resetIn) {
    return this.createWarningEmbed(
      'Rate Limit Exceeded',
      `You're sending requests too quickly! Please wait ${resetIn} seconds before trying again.\n\n` +
      `**Rate Limits:**\n` +
      `• ${config.security.rateLimitRequests} requests per ${config.security.rateLimitWindow / 1000} seconds\n` +
      `• This helps keep the bot responsive for everyone!`
    );
  }

  /**
   * Create unauthorized access embed
   */
  static createUnauthorizedEmbed(type = 'general') {
    const messages = {
      guild: 'This bot is restricted to PROJECT MADARA servers only.',
      channel: 'AI commands are not allowed in this channel.',
      general: 'You do not have permission to use this command.'
    };

    return this.createErrorEmbed(
      'Access Denied',
      messages[type] || messages.general
    );
  }

  /**
   * Create bot status embed
   */
  static createStatusEmbed(stats) {
    return this.createEmbed({
      title: '📊 PROJECT MADARA Bot Status',
      fields: [
        {
          name: '🏓 Ping',
          value: `${stats.ping}ms`,
          inline: true
        },
        {
          name: '⏱️ Uptime',
          value: stats.uptime,
          inline: true
        },
        {
          name: '🏠 Servers',
          value: stats.guilds.toString(),
          inline: true
        },
        {
          name: '👥 Users',
          value: stats.users.toString(),
          inline: true
        },
        {
          name: '🧠 AI Model',
          value: config.ai.model,
          inline: true
        },
        {
          name: '💾 Memory Usage',
          value: stats.memory,
          inline: true
        }
      ],
      thumbnail: config.bot.iconUrl
    });
  }
}

export default MadaraEmbedBuilder;
