import { MadaraEmbedBuilder } from './embedBuilder.js';

export const supportCommand = {
  name: 'support',
  description: 'Get PROJECT MADARA support information',
  aliases: ['help-support', 'contact'],
  
  async execute(message, args) {
    const embed = MadaraEmbedBuilder.createEmbed({
      title: '🆘 PROJECT MADARA Support',
      description: 'Need help? Here are all the ways to get support for PROJECT MADARA.',
      fields: [
        {
          name: '🤖 AI Assistant',
          value: `Use \`!madara ask <question>\` to get instant help from MADARA AI for common questions and issues.`,
          inline: false
        },
        {
          name: '💬 Discord Support',
          value: 'Join our Discord server for community support and direct help from our team.',
          inline: true
        },
        {
          name: '🌐 Website Support',
          value: 'Visit our website for detailed guides, FAQs, and documentation.',
          inline: true
        },
        {
          name: '🔧 Common Issues',
          value: '• **Key not working?** Try regenerating your key\n• **Script not loading?** Check if you have the latest version\n• **Getting detected?** Ensure you\'re using our latest scripts',
          inline: false
        },
        {
          name: '📋 Before Asking for Help',
          value: '• Check if your key is valid and not expired\n• Make sure you\'re using the correct executor\n• Try restarting Roblox and your executor\n• Check our Discord announcements for known issues',
          inline: false
        },
        {
          name: '🔗 Support Links',
          value: '[💬 Discord Server](https://discord.gg/madara)\n[🌐 Website](https://checkingbefore.netlify.app)\n[🔑 Key System](https://checkingbefore.netlify.app/generate-key)',
          inline: false
        },
        {
          name: '⏰ Support Hours',
          value: 'Our team is available 24/7 through Discord and AI assistant. Response times may vary.',
          inline: false
        }
      ],
      color: '#00FF00'
    });

    await message.reply({ embeds: [embed] });
  }
};

export default supportCommand;
