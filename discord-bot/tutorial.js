import { MadaraEmbedBuilder } from './embedBuilder.js';
import { SecurityManager } from './security.js';
import { FAQMatcher } from './faqMatcher.js';
import { logDiscordEvent } from './logger.js';

/**
 * Interactive Tutorial Command for PROJECT MADARA Discord bot
 */
export const tutorialCommand = {
  name: 'tutorial',
  description: 'Interactive tutorials and step-by-step guides',
  aliases: ['guide', 'learn', 'howto'],
  
  async execute(message, args, client) {
    // Check if channel is allowed
    if (!SecurityManager.isChannelAllowed(message.channel.id)) {
      const embed = MadaraEmbedBuilder.createUnauthorizedEmbed('channel');
      return await message.reply({ embeds: [embed] });
    }

    const faqMatcher = new FAQMatcher();
    
    // If no arguments, show tutorial categories
    if (!args || args.length === 0) {
      return await this.showTutorialCategories(message, faqMatcher);
    }

    const topic = args.join(' ').toLowerCase();
    
    // Handle specific tutorial topics
    switch (topic) {
      case 'scripting':
      case 'lua':
        return await this.showScriptingTutorial(message);
      
      case 'executor':
      case 'executors':
        return await this.showExecutorTutorial(message);
      
      case 'troubleshooting':
      case 'problems':
        return await this.showTroubleshootingTutorial(message);
      
      case 'security':
      case 'safety':
        return await this.showSecurityTutorial(message);
      
      default:
        // Try to find FAQ match
        const faqMatch = faqMatcher.findBestMatch(topic);
        if (faqMatch) {
          return await this.showFAQTutorial(message, faqMatch);
        }
        
        return await this.showTutorialNotFound(message, topic);
    }
  },

  /**
   * Show available tutorial categories
   */
  async showTutorialCategories(message, faqMatcher) {
    const categories = faqMatcher.getCategories();
    
    const embed = MadaraEmbedBuilder.createEmbed({
      title: '📚 PROJECT MADARA Tutorials',
      description: 'Choose a tutorial category to get started with step-by-step guides:',
      fields: [
        {
          name: '🎯 Quick Start Tutorials',
          value: '• `!madara tutorial scripting` - Learn Lua scripting basics\n' +
                 '• `!madara tutorial executor` - How to use script executors\n' +
                 '• `!madara tutorial security` - Stay safe while scripting',
          inline: false
        },
        {
          name: '🔧 Troubleshooting',
          value: '• `!madara tutorial troubleshooting` - Fix common issues\n' +
                 '• `!madara tutorial problems` - Solve script problems',
          inline: false
        },
        {
          name: '❓ FAQ Categories',
          value: categories.map(cat => 
            `• **${cat.name}** (${cat.count} topics)`
          ).join('\n'),
          inline: false
        },
        {
          name: '💡 Pro Tips',
          value: '• Ask specific questions for personalized help\n' +
                 '• Use `!madara ask` for AI-powered assistance\n' +
                 '• Check our FAQ for instant answers',
          inline: false
        }
      ],
      color: '#0099FF'
    });

    await message.reply({ embeds: [embed] });
    
    logDiscordEvent('tutorial_categories_viewed', {
      userId: message.author.id,
      username: message.author.username
    });
  },

  /**
   * Show Lua scripting tutorial
   */
  async showScriptingTutorial(message) {
    const embed = MadaraEmbedBuilder.createEmbed({
      title: '🎯 Lua Scripting Tutorial',
      description: 'Learn the basics of Lua scripting for Roblox:',
      fields: [
        {
          name: '📖 Step 1: Understanding Variables',
          value: '```lua\n-- Local variables (recommended)\nlocal playerName = "YourName"\nlocal health = 100\n\n-- Global variables (avoid these)\nplayerName = "YourName"\n```',
          inline: false
        },
        {
          name: '🔧 Step 2: Functions',
          value: '```lua\n-- Define a function\nlocal function greetPlayer(name)\n    print("Hello, " .. name .. "!")\nend\n\n-- Call the function\ngreetPlayer("Sabin07")\n```',
          inline: false
        },
        {
          name: '🎮 Step 3: Roblox Services',
          value: '```lua\n-- Get services\nlocal Players = game:GetService("Players")\nlocal Workspace = game:GetService("Workspace")\n\n-- Get local player\nlocal player = Players.LocalPlayer\n```',
          inline: false
        },
        {
          name: '⚡ Step 4: Events and Connections',
          value: '```lua\n-- Connect to events\nplayer.CharacterAdded:Connect(function(character)\n    print("Character spawned!")\nend)\n```',
          inline: false
        },
        {
          name: '🚀 Next Steps',
          value: '• Practice with simple scripts\n• Join our Discord for help\n• Check PROJECT MADARA scripts for examples\n• Ask specific questions with `!madara ask`',
          inline: false
        }
      ],
      color: '#9932CC'
    });

    await message.reply({ embeds: [embed] });
    
    logDiscordEvent('scripting_tutorial_viewed', {
      userId: message.author.id,
      username: message.author.username
    });
  },

  /**
   * Show executor tutorial
   */
  async showExecutorTutorial(message) {
    const embed = MadaraEmbedBuilder.createEmbed({
      title: '⚙️ Script Executor Tutorial',
      description: 'Learn how to safely use script executors:',
      fields: [
        {
          name: '📋 Step 1: Choose a Reliable Executor',
          value: '• Research well-known executors\n• Check community reviews\n• Avoid suspicious downloads\n• Use official websites only',
          inline: false
        },
        {
          name: '🛡️ Step 2: Safety First',
          value: '• Use antivirus software\n• Create a separate Windows account\n• Never share personal information\n• Backup important files',
          inline: false
        },
        {
          name: '📥 Step 3: Installation',
          value: '• Download from official source\n• Disable antivirus temporarily (if needed)\n• Follow installation instructions\n• Re-enable antivirus after installation',
          inline: false
        },
        {
          name: '🎯 Step 4: Using Scripts',
          value: '• Copy script from PROJECT MADARA\n• Paste into executor\n• Click "Execute" or "Inject"\n• Follow any script-specific instructions',
          inline: false
        },
        {
          name: '⚠️ Important Warnings',
          value: '• Use scripts responsibly\n• Respect game rules and other players\n• Don\'t use in competitive games\n• Report malicious scripts',
          inline: false
        }
      ],
      color: '#FF6B35'
    });

    await message.reply({ embeds: [embed] });
    
    logDiscordEvent('executor_tutorial_viewed', {
      userId: message.author.id,
      username: message.author.username
    });
  },

  /**
   * Show troubleshooting tutorial
   */
  async showTroubleshootingTutorial(message) {
    const embed = MadaraEmbedBuilder.createEmbed({
      title: '🔧 Troubleshooting Guide',
      description: 'Common issues and their solutions:',
      fields: [
        {
          name: '❌ Script Not Working',
          value: '**Possible causes:**\n• Outdated script version\n• Incompatible executor\n• Game updates\n\n**Solutions:**\n• Check for script updates\n• Try a different executor\n• Contact support',
          inline: false
        },
        {
          name: '🚫 Executor Won\'t Inject',
          value: '**Possible causes:**\n• Antivirus blocking\n• Roblox updates\n• Executor outdated\n\n**Solutions:**\n• Whitelist executor in antivirus\n• Update executor\n• Restart Roblox',
          inline: false
        },
        {
          name: '⚠️ Error Messages',
          value: '**Common errors:**\n• "Attempt to index nil" - Missing object\n• "Syntax error" - Code formatting issue\n• "Access denied" - Insufficient permissions\n\n**Solution:** Share error with support',
          inline: false
        },
        {
          name: '🆘 Getting Help',
          value: '• Use `!madara ask` for AI assistance\n• Join our Discord server\n• Provide detailed error descriptions\n• Include screenshots if possible',
          inline: false
        }
      ],
      color: '#FFA500'
    });

    await message.reply({ embeds: [embed] });
    
    logDiscordEvent('troubleshooting_tutorial_viewed', {
      userId: message.author.id,
      username: message.author.username
    });
  },

  /**
   * Show security tutorial
   */
  async showSecurityTutorial(message) {
    const embed = MadaraEmbedBuilder.createEmbed({
      title: '🛡️ Security & Safety Guide',
      description: 'Stay safe while using Roblox scripts:',
      fields: [
        {
          name: '✅ Safe Practices',
          value: '• Only download from PROJECT MADARA\n• Use reputable executors\n• Keep antivirus enabled\n• Create separate user accounts\n• Regular system backups',
          inline: false
        },
        {
          name: '🚨 Red Flags to Avoid',
          value: '• Scripts asking for passwords\n• Executors from unknown sources\n• Scripts requiring personal info\n• Suspicious file downloads\n• Unverified Discord links',
          inline: false
        },
        {
          name: '🔒 Account Security',
          value: '• Use strong, unique passwords\n• Enable 2FA on Roblox\n• Never share account details\n• Log out from shared computers\n• Monitor account activity',
          inline: false
        },
        {
          name: '📞 Report Issues',
          value: '• Suspicious scripts or users\n• Malware or viruses\n• Account compromise\n• Scam attempts\n\nContact us immediately!',
          inline: false
        }
      ],
      color: '#00FF00'
    });

    await message.reply({ embeds: [embed] });
    
    logDiscordEvent('security_tutorial_viewed', {
      userId: message.author.id,
      username: message.author.username
    });
  },

  /**
   * Show FAQ-based tutorial
   */
  async showFAQTutorial(message, faqMatch) {
    const embed = MadaraEmbedBuilder.createEmbed({
      title: `📚 Tutorial: ${faqMatch.question}`,
      description: faqMatch.answer,
      fields: [
        {
          name: '📊 Tutorial Info',
          value: `**Category:** ${faqMatch.category}\n**Confidence:** ${Math.round(faqMatch.confidence * 100)}%`,
          inline: true
        },
        {
          name: '🔗 Related Help',
          value: '• Use `!madara ask` for more details\n• Check other tutorials\n• Join our Discord for support',
          inline: true
        }
      ],
      color: '#0099FF'
    });

    await message.reply({ embeds: [embed] });
    
    logDiscordEvent('faq_tutorial_viewed', {
      userId: message.author.id,
      username: message.author.username,
      faqId: faqMatch.id
    });
  },

  /**
   * Show tutorial not found message
   */
  async showTutorialNotFound(message, topic) {
    const embed = MadaraEmbedBuilder.createEmbed({
      title: '❓ Tutorial Not Found',
      description: `Sorry, I couldn't find a tutorial for "${topic}".`,
      fields: [
        {
          name: '💡 Suggestions',
          value: '• Use `!madara tutorial` to see all categories\n• Try `!madara ask ${topic}` for AI help\n• Check our FAQ with specific questions\n• Join our Discord for community support',
          inline: false
        },
        {
          name: '📚 Available Tutorials',
          value: '• `scripting` - Lua basics\n• `executor` - How to use executors\n• `troubleshooting` - Fix problems\n• `security` - Stay safe',
          inline: false
        }
      ],
      color: '#FFA500'
    });

    await message.reply({ embeds: [embed] });
  }
};

export default tutorialCommand;
