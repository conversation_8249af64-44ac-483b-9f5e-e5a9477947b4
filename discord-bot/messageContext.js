/**
 * Simple Message Context System for PROJECT MADARA Discord bot
 * Tracks message replies and recent conversation flow
 */
export class MessageContext {
  constructor() {
    // Store recent messages per channel
    this.channelMessages = new Map();
    
    // Store bot's recent responses
    this.botResponses = new Map();
    
    // Settings
    this.maxMessagesPerChannel = 10;
    this.maxAge = 10 * 60 * 1000; // 10 minutes
    
    // Start cleanup
    this.startCleanup();
  }

  /**
   * Add a message to context
   */
  addMessage(message) {
    const channelId = message.channel.id;
    
    if (!this.channelMessages.has(channelId)) {
      this.channelMessages.set(channelId, []);
    }
    
    const messages = this.channelMessages.get(channelId);
    
    const messageData = {
      id: message.id,
      content: message.content,
      author: {
        id: message.author.id,
        username: message.author.username,
        isBot: message.author.bot
      },
      timestamp: Date.now(),
      referencedMessage: message.reference ? message.reference.messageId : null
    };
    
    messages.push(messageData);
    
    // Keep only recent messages
    if (messages.length > this.maxMessagesPerChannel) {
      messages.shift();
    }
  }

  /**
   * Add bot response to context
   */
  addBotResponse(originalMessage, botResponse, aiResponse) {
    const responseData = {
      originalMessageId: originalMessage.id,
      originalContent: originalMessage.content,
      response: botResponse,
      aiData: aiResponse,
      timestamp: Date.now(),
      channelId: originalMessage.channel.id,
      userId: originalMessage.author.id
    };
    
    this.botResponses.set(botResponse.id, responseData);
  }

  /**
   * Get context for a message (including replied-to messages)
   */
  async getMessageContext(message) {
    let context = '';
    
    // Check if this is a reply to a message
    if (message.reference && message.reference.messageId) {
      try {
        const repliedMessage = await message.channel.messages.fetch(message.reference.messageId);
        
        // If replying to bot message, get the original context
        if (repliedMessage.author.bot && this.botResponses.has(repliedMessage.id)) {
          const botResponseData = this.botResponses.get(repliedMessage.id);
          context += `PREVIOUS CONVERSATION:\n`;
          context += `User asked: "${botResponseData.originalContent}"\n`;
          context += `I responded with: "${this.truncateText(botResponseData.response.content || botResponseData.response, 200)}"\n\n`;
          context += `USER NOW REPLIES: "${message.content}"\n`;
        } else {
          // Replying to user message
          context += `REPLYING TO MESSAGE:\n`;
          context += `${repliedMessage.author.username}: "${repliedMessage.content}"\n\n`;
          context += `USER REPLIES: "${message.content}"\n`;
        }
      } catch (error) {
        // Couldn't fetch the replied message, continue without context
      }
    }
    
    // Add recent channel context if no reply context
    if (!context) {
      const recentContext = this.getRecentChannelContext(message.channel.id, message.author.id);
      if (recentContext) {
        context = recentContext;
      }
    }
    
    return context;
  }

  /**
   * Get recent conversation context from channel
   */
  getRecentChannelContext(channelId, userId) {
    const messages = this.channelMessages.get(channelId);
    if (!messages || messages.length < 2) {
      return '';
    }
    
    // Get last few messages involving this user or bot
    const relevantMessages = messages
      .filter(msg => 
        msg.author.id === userId || 
        msg.author.isBot ||
        Date.now() - msg.timestamp < 5 * 60 * 1000 // Last 5 minutes
      )
      .slice(-3); // Last 3 relevant messages
    
    if (relevantMessages.length < 2) {
      return '';
    }
    
    let context = 'RECENT CONVERSATION:\n';
    relevantMessages.forEach(msg => {
      const timeAgo = this.getTimeAgo(msg.timestamp);
      const role = msg.author.isBot ? 'MADARA AI' : msg.author.username;
      const content = this.truncateText(msg.content, 150);
      context += `[${timeAgo}] ${role}: "${content}"\n`;
    });
    
    return context + '\n';
  }

  /**
   * Check if message is continuing a conversation
   */
  isContinuingConversation(message) {
    // Check if replying to a message
    if (message.reference) {
      return true;
    }
    
    // Check if user has recent messages in channel
    const messages = this.channelMessages.get(message.channel.id);
    if (!messages) {
      return false;
    }
    
    const recentUserMessages = messages.filter(msg => 
      msg.author.id === message.author.id &&
      Date.now() - msg.timestamp < 2 * 60 * 1000 // Last 2 minutes
    );
    
    return recentUserMessages.length > 0;
  }

  /**
   * Get conversation thread for a message
   */
  getConversationThread(channelId, messageId, maxDepth = 5) {
    const messages = this.channelMessages.get(channelId) || [];
    const thread = [];
    let currentMessageId = messageId;
    let depth = 0;
    
    while (currentMessageId && depth < maxDepth) {
      const message = messages.find(m => m.id === currentMessageId);
      if (!message) break;
      
      thread.unshift(message);
      currentMessageId = message.referencedMessage;
      depth++;
    }
    
    return thread;
  }

  /**
   * Format conversation thread for AI context
   */
  formatConversationThread(thread) {
    if (thread.length === 0) {
      return '';
    }
    
    let context = 'CONVERSATION THREAD:\n';
    thread.forEach((msg, index) => {
      const role = msg.author.isBot ? 'MADARA AI' : msg.author.username;
      const content = this.truncateText(msg.content, 200);
      const arrow = index < thread.length - 1 ? ' ↓' : '';
      context += `${role}: "${content}"${arrow}\n`;
    });
    
    return context + '\n';
  }

  /**
   * Clean up old messages
   */
  startCleanup() {
    setInterval(() => {
      this.cleanupOldMessages();
    }, 2 * 60 * 1000); // Every 2 minutes
  }

  /**
   * Remove old messages and responses
   */
  cleanupOldMessages() {
    const now = Date.now();
    
    // Clean channel messages
    for (const [channelId, messages] of this.channelMessages.entries()) {
      const filtered = messages.filter(msg => now - msg.timestamp < this.maxAge);
      if (filtered.length === 0) {
        this.channelMessages.delete(channelId);
      } else {
        this.channelMessages.set(channelId, filtered);
      }
    }
    
    // Clean bot responses
    for (const [responseId, data] of this.botResponses.entries()) {
      if (now - data.timestamp > this.maxAge) {
        this.botResponses.delete(responseId);
      }
    }
  }

  /**
   * Get statistics
   */
  getStats() {
    const totalMessages = Array.from(this.channelMessages.values())
      .reduce((sum, messages) => sum + messages.length, 0);
    
    return {
      channels: this.channelMessages.size,
      totalMessages,
      botResponses: this.botResponses.size,
      memoryUsage: this.estimateMemoryUsage()
    };
  }

  /**
   * Helper methods
   */
  truncateText(text, maxLength) {
    if (!text || text.length <= maxLength) {
      return text;
    }
    return text.substring(0, maxLength - 3) + '...';
  }

  getTimeAgo(timestamp) {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m`;
    return `${Math.floor(seconds / 3600)}h`;
  }

  estimateMemoryUsage() {
    const messageCount = Array.from(this.channelMessages.values())
      .reduce((sum, messages) => sum + messages.length, 0);
    const avgMessageSize = 200; // bytes
    return `${Math.round(messageCount * avgMessageSize / 1024)}KB`;
  }

  /**
   * Clear context for a channel
   */
  clearChannelContext(channelId) {
    this.channelMessages.delete(channelId);
    
    // Remove bot responses for this channel
    for (const [responseId, data] of this.botResponses.entries()) {
      if (data.channelId === channelId) {
        this.botResponses.delete(responseId);
      }
    }
  }

  /**
   * Get context summary for debugging
   */
  getContextSummary(channelId) {
    const messages = this.channelMessages.get(channelId) || [];
    const recentMessages = messages.slice(-5);
    
    return {
      channelId,
      messageCount: messages.length,
      recentMessages: recentMessages.map(msg => ({
        author: msg.author.username,
        content: this.truncateText(msg.content, 50),
        timeAgo: this.getTimeAgo(msg.timestamp)
      }))
    };
  }
}

// Create singleton instance
export const messageContext = new MessageContext();
export default messageContext;
