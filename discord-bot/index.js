import { Client, GatewayIntentBits, ActivityType } from 'discord.js';
import { config, validateConfig } from './config.js';
import { CommandHandler } from './commandHandler.js';
import { SlashCommandHandler } from './slashCommandHandler.js';
import { aiService } from './aiService.js';
import logger, { logDiscordEvent, logError } from './logger.js';
// Removed cron for memory optimization
import express from 'express';

/**
 * PROJECT MADARA Discord Bot
 * AI-powered Discord bot with Groq integration
 */
class MadaraBot {
  constructor() {
    this.client = null;
    this.commandHandler = null;
    this.slashCommandHandler = null;
    this.startTime = Date.now();
  }

  /**
   * Initialize the bot
   */
  async initialize() {
    try {
      // Validate configuration
      validateConfig();
      logger.info('Configuration validated successfully');

      // Create Discord client with minimal intents
      this.client = new Client({
        intents: [
          GatewayIntentBits.Guilds,
          GatewayIntentBits.GuildMessages,
          GatewayIntentBits.MessageContent
          // Removed GuildMembers to save memory
        ]
      });

      // Initialize command handlers
      this.commandHandler = new CommandHandler();
      this.slashCommandHandler = new SlashCommandHandler();

      // Set up event listeners
      this.setupEventListeners();

      // Login to Discord
      await this.client.login(config.discord.token);
      
    } catch (error) {
      logError(error, { context: 'bot_initialization' });
      process.exit(1);
    }
  }

  /**
   * Set up Discord event listeners
   */
  setupEventListeners() {
    // Bot ready event
    this.client.once('ready', () => {
      this.onReady();
    });

    // Message events
    this.client.on('messageCreate', async (message) => {
      await this.commandHandler.handleMessage(message, this.client);
    });

    // Interaction events (slash commands)
    this.client.on('interactionCreate', async (interaction) => {
      await this.slashCommandHandler.handleInteraction(interaction, this.client);
    });

    // Guild events
    this.client.on('guildCreate', (guild) => {
      logDiscordEvent('guild_joined', {
        guildId: guild.id,
        guildName: guild.name,
        memberCount: guild.memberCount
      });
    });

    this.client.on('guildDelete', (guild) => {
      logDiscordEvent('guild_left', {
        guildId: guild.id,
        guildName: guild.name
      });
    });

    // Error handling
    this.client.on('error', (error) => {
      logError(error, { context: 'discord_client_error' });
    });

    this.client.on('warn', (warning) => {
      logger.warn('Discord client warning:', warning);
    });

    // Unhandled promise rejections
    process.on('unhandledRejection', (error) => {
      logError(error, { context: 'unhandled_promise_rejection' });
    });

    // Graceful shutdown
    process.on('SIGINT', () => {
      this.shutdown();
    });

    process.on('SIGTERM', () => {
      this.shutdown();
    });
  }

  /**
   * Handle bot ready event
   */
  async onReady() {
    logger.info(`🔥 PROJECT MADARA Bot is ready!`);
    logger.info(`Logged in as: ${this.client.user.tag}`);
    logger.info(`Serving ${this.client.guilds.cache.size} guilds`);
    logger.info(`Total users: ${this.client.users.cache.size}`);
    logger.info(`Slash commands loaded: ${this.slashCommandHandler.getStats().totalSlashCommands}`);

    // Set bot presence
    await this.updatePresence();

    // Test AI service
    await this.testAIService();

    // Scheduled tasks removed for memory optimization

    // Start health check server for hosting platforms
    this.startHealthServer();

    logDiscordEvent('bot_ready', {
      botTag: this.client.user.tag,
      guildCount: this.client.guilds.cache.size,
      userCount: this.client.users.cache.size
    });
  }

  /**
   * Update bot presence with PROJECT MADARA branding
   */
  async updatePresence() {
    try {
      await this.client.user.setPresence({
        status: 'online',
        activities: [{
          name: 'PROJECT MADARA | !madara help',
          type: ActivityType.Watching
        }]
      });

      logger.info('Bot presence updated with PROJECT MADARA branding');
    } catch (error) {
      logError(error, { context: 'update_presence' });
    }
  }

  /**
   * Test AI service connectivity
   */
  async testAIService() {
    try {
      const healthCheck = await aiService.healthCheck();
      
      if (healthCheck.available) {
        logger.info(`✅ AI Service (${aiService.model}) is available`);
      } else {
        logger.error(`❌ AI Service is unavailable: ${healthCheck.error}`);
      }
    } catch (error) {
      logError(error, { context: 'ai_service_test' });
    }
  }

  // Scheduled tasks removed for memory optimization

  /**
   * Log bot statistics
   */
  logStatistics() {
    const uptime = Date.now() - this.startTime;
    const memoryUsage = process.memoryUsage();
    
    logDiscordEvent('hourly_statistics', {
      uptime: uptime,
      guilds: this.client.guilds.cache.size,
      users: this.client.users.cache.size,
      memoryUsage: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      commandStats: this.commandHandler.getStats()
    });
  }

  /**
   * Minimal health server for hosting platforms
   */
  startHealthServer() {
    const app = express();
    const port = process.env.HEALTH_PORT || process.env.PORT || 3001;

    // Minimal health check - just return OK
    app.get('/health', (req, res) => {
      res.json({ status: 'ok', uptime: Math.floor(process.uptime()) });
    });

    // Keep-alive endpoint (prevents sleeping)
    app.get('/ping', (req, res) => {
      res.send('pong');
    });

    app.listen(port, () => {
      logger.info(`Health server running on port ${port}`);
    });

    // Self-ping to prevent sleeping (every 14 minutes)
    if (process.env.RENDER_SERVICE_NAME) {
      setInterval(() => {
        fetch(`https://${process.env.RENDER_SERVICE_NAME}.onrender.com/ping`)
          .catch(() => {}); // Ignore errors
      }, 14 * 60 * 1000); // 14 minutes
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    logger.info('Shutting down PROJECT MADARA Bot...');

    try {
      if (this.client) {
        await this.client.destroy();
      }

      logger.info('Bot shutdown complete');
      process.exit(0);
    } catch (error) {
      logError(error, { context: 'bot_shutdown' });
      process.exit(1);
    }
  }
}

// Start the bot
const bot = new MadaraBot();
bot.initialize().catch((error) => {
  console.error('Failed to start PROJECT MADARA Bot:', error);
  process.exit(1);
});

export default MadaraBot;
