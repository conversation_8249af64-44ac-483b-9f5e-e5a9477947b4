{"name": "project-madara-discord-bot", "version": "1.0.0", "description": "PROJECT MADARA Discord Bo<PERSON> with AI Integration", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "build": "echo 'Build completed successfully' && exit 0", "dev": "node --watch index.js", "test": "echo \"Error: no test specified\" && exit 1", "register-commands": "node registerCommands.js allowed", "register-global": "node registerCommands.js global", "register-guild": "node registerCommands.js guild", "clear-commands": "node registerCommands.js clear-global", "list-commands": "node registerCommands.js list-global"}, "keywords": ["discord", "bot", "ai", "project-madara", "groq", "roblo<PERSON>"], "author": "PROJECT MADARA Team", "license": "MIT", "dependencies": {"discord.js": "^14.14.1", "dotenv": "^16.4.5", "express": "^4.21.2", "groq-sdk": "^0.3.3"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}