services:
  - type: web
    name: project-madara-discord-bot
    env: node
    plan: free
    buildCommand: npm install
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: DISCORD_TOKEN
        sync: false
      - key: DISCORD_CLIENT_ID
        sync: false
      - key: OPENROUTER_API_KEY
        sync: false
      - key: PORT
        value: 3000
    healthCheckPath: /health
