import { MadaraEmbedBuilder } from './embedBuilder.js';

export const scriptsCommand = {
  name: 'scripts',
  description: 'Get information about PROJECT MADARA scripts',
  aliases: ['script', 'hub'],
  
  async execute(message, args) {
    const category = args && args.length > 0 ? args[0].toLowerCase() : 'all';

    // Handle different categories
    switch (category) {
      case 'games':
        return await this.showGameScripts(message);
      case 'utility':
        return await this.showUtilityScripts(message);
      case 'popular':
        return await this.showPopularScripts(message);
      default:
        return await this.showAllScripts(message);
    }
  },

  /**
   * Show game-specific scripts
   */
  async showGameScripts(message) {
    const embed = MadaraEmbedBuilder.createEmbed({
      title: '🎮 Game Scripts - PROJECT MADARA',
      description: 'High-quality scripts for popular Roblox games:',
      fields: [
        {
          name: '⚔️ Combat Games',
          value: '• Arsenal Scripts\n• Phantom Forces Scripts\n• Counter Blox Scripts\n• Bad Business Scripts',
          inline: true
        },
        {
          name: '🏃 Simulator Games',
          value: '• Pet Simulator Scripts\n• Bee Swarm Simulator\n• Mining Simulator\n• Weight Lifting Simulator',
          inline: true
        },
        {
          name: '🎯 Popular Games',
          value: '• Adopt Me Scripts\n• Brookhaven Scripts\n• Jailbreak Scripts\n• Murder Mystery Scripts',
          inline: true
        }
      ],
      color: '#FF6B35'
    });

    await message.reply({ embeds: [embed] });
  },

  /**
   * Show utility scripts
   */
  async showUtilityScripts(message) {
    const embed = MadaraEmbedBuilder.createEmbed({
      title: '🔧 Utility Scripts - PROJECT MADARA',
      description: 'Essential utility scripts for enhanced gameplay:',
      fields: [
        {
          name: '🎯 General Utilities',
          value: '• Universal ESP\n• Speed/Jump Hacks\n• Teleport Scripts\n• Auto-Farm Scripts',
          inline: true
        },
        {
          name: '🛡️ Safety Tools',
          value: '• Anti-Kick Scripts\n• Server Hop Scripts\n• Lag Reduction\n• Performance Boosters',
          inline: true
        },
        {
          name: '🎨 Visual Enhancements',
          value: '• GUI Improvements\n• Custom Themes\n• Better Graphics\n• UI Modifications',
          inline: true
        }
      ],
      color: '#9932CC'
    });

    await message.reply({ embeds: [embed] });
  },

  /**
   * Show popular scripts
   */
  async showPopularScripts(message) {
    const embed = MadaraEmbedBuilder.createEmbed({
      title: '🎯 Popular Scripts - PROJECT MADARA',
      description: 'Most requested and highly-rated scripts:',
      fields: [
        {
          name: '🔥 Top Picks',
          value: '• Universal Hub (All Games)\n• Infinite Yield (Admin Commands)\n• Dark Dex (Explorer)\n• Remote Spy (Debugging)',
          inline: false
        },
        {
          name: '⭐ Community Favorites',
          value: '• Auto-Farm Collection\n• ESP Bundle Pack\n• Speed Hack Suite\n• Teleport Manager',
          inline: false
        },
        {
          name: '📊 Usage Stats',
          value: '• 50,000+ downloads this month\n• 4.8/5 average rating\n• 99.2% success rate\n• Regular updates',
          inline: false
        }
      ],
      color: '#00FF00'
    });

    await message.reply({ embeds: [embed] });
  },

  /**
   * Show all scripts overview
   */
  async showAllScripts(message) {
    const embed = MadaraEmbedBuilder.createEmbed({
      title: '📜 PROJECT MADARA Scripts',
      description: 'Access premium Roblox scripts through our secure platform.',
      fields: [
        {
          name: '🔑 How to Get Scripts',
          value: '1. Visit our website\n2. Generate a key\n3. Complete verification\n4. Access premium scripts',
          inline: false
        },
        {
          name: '🎮 Popular Scripts',
          value: '• Universal ESP\n• Speed/Jump Hacks\n• Auto-Farm Scripts\n• Game-Specific Tools\n• And much more!',
          inline: true
        },
        {
          name: '🛡️ Security Features',
          value: '• Advanced obfuscation\n• Anti-detection\n• Regular updates\n• Secure key system\n• 24/7 monitoring',
          inline: true
        },
        {
          name: '⚡ Key Features',
          value: '• Instant access\n• Regular updates\n• Multiple games supported\n• User-friendly interface\n• Premium support',
          inline: false
        },
        {
          name: '🔗 Get Started',
          value: '[🌐 Visit Website](https://checkingbefore.netlify.app)\n[🔑 Generate Key](https://checkingbefore.netlify.app/generate-key)',
          inline: false
        },
        {
          name: '📂 Browse by Category',
          value: '• Use `/scripts games` for game-specific scripts\n• Use `/scripts utility` for utility tools\n• Use `/scripts popular` for top picks',
          inline: false
        }
      ],
      color: '#FF6B35'
    });

    await message.reply({ embeds: [embed] });
  }
};

export default scriptsCommand;
