/**
 * Smart Context Memory System for PROJECT MADARA Discord bot
 * Maintains conversation context and user preferences across interactions
 */
export class ContextMemory {
  constructor() {
    // User conversation contexts
    this.userContexts = new Map();
    
    // User preferences and learning data
    this.userProfiles = new Map();
    
    // Context cleanup settings
    this.maxContextAge = 30 * 60 * 1000; // 30 minutes
    this.maxContextMessages = 10; // Keep last 10 messages
    this.maxUsers = 1000; // Memory limit
    
    // Start cleanup interval
    this.startCleanupInterval();
  }

  /**
   * Get or create user context
   */
  getUserContext(userId) {
    if (!this.userContexts.has(userId)) {
      this.userContexts.set(userId, {
        messages: [],
        currentTopic: null,
        programmingLanguage: null,
        skillLevel: 'beginner',
        lastInteraction: Date.now(),
        sessionId: this.generateSessionId(),
        preferences: {
          responseStyle: 'detailed', // detailed, concise, code-focused
          preferredLanguages: [],
          learningGoals: [],
          timezone: null
        },
        learningProgress: {
          topicsDiscussed: new Set(),
          questionsAsked: 0,
          codeShared: 0,
          errorsFixed: 0,
          tutorialsCompleted: []
        }
      });
    }
    
    return this.userContexts.get(userId);
  }

  /**
   * Add message to user context
   */
  addMessage(userId, message, isBot = false) {
    const context = this.getUserContext(userId);
    
    const messageData = {
      content: message,
      timestamp: Date.now(),
      isBot,
      messageId: this.generateMessageId(),
      topics: this.extractTopics(message),
      codeDetected: this.detectCodeInMessage(message),
      sentiment: this.analyzeSentiment(message)
    };

    context.messages.push(messageData);
    context.lastInteraction = Date.now();

    // Keep only recent messages
    if (context.messages.length > this.maxContextMessages) {
      context.messages = context.messages.slice(-this.maxContextMessages);
    }

    // Update learning progress
    this.updateLearningProgress(userId, messageData);

    // Detect topic changes
    this.updateCurrentTopic(userId, messageData);

    return messageData;
  }

  /**
   * Get conversation context for AI
   */
  getConversationContext(userId, includeHistory = true) {
    const context = this.getUserContext(userId);
    
    let contextString = '';

    // Add user profile information
    contextString += this.buildUserProfileContext(userId);

    // Add recent conversation history
    if (includeHistory && context.messages.length > 0) {
      contextString += '\n\nRECENT CONVERSATION HISTORY:\n';
      
      const recentMessages = context.messages.slice(-5); // Last 5 messages
      for (const msg of recentMessages) {
        const role = msg.isBot ? 'MADARA AI' : 'User';
        const timeAgo = this.getTimeAgo(msg.timestamp);
        contextString += `[${timeAgo}] ${role}: ${msg.content.substring(0, 200)}\n`;
      }
    }

    // Add current topic context
    if (context.currentTopic) {
      contextString += `\nCURRENT TOPIC: ${context.currentTopic}\n`;
    }

    // Add learning context
    if (context.learningProgress.topicsDiscussed.size > 0) {
      const topics = Array.from(context.learningProgress.topicsDiscussed).slice(-3);
      contextString += `\nRECENT TOPICS DISCUSSED: ${topics.join(', ')}\n`;
    }

    return contextString;
  }

  /**
   * Build user profile context
   */
  buildUserProfileContext(userId) {
    const context = this.getUserContext(userId);
    
    let profileContext = `USER PROFILE:\n`;
    profileContext += `- Skill Level: ${context.skillLevel}\n`;
    profileContext += `- Response Style: ${context.preferences.responseStyle}\n`;
    
    if (context.programmingLanguage) {
      profileContext += `- Current Language: ${context.programmingLanguage}\n`;
    }
    
    if (context.preferences.preferredLanguages.length > 0) {
      profileContext += `- Preferred Languages: ${context.preferences.preferredLanguages.join(', ')}\n`;
    }
    
    if (context.preferences.learningGoals.length > 0) {
      profileContext += `- Learning Goals: ${context.preferences.learningGoals.join(', ')}\n`;
    }

    profileContext += `- Questions Asked: ${context.learningProgress.questionsAsked}\n`;
    profileContext += `- Code Shared: ${context.learningProgress.codeShared}\n`;
    profileContext += `- Errors Fixed: ${context.learningProgress.errorsFixed}\n`;

    return profileContext;
  }

  /**
   * Update user preferences
   */
  updateUserPreference(userId, key, value) {
    const context = this.getUserContext(userId);
    
    if (key === 'programmingLanguage') {
      context.programmingLanguage = value;
      if (!context.preferences.preferredLanguages.includes(value)) {
        context.preferences.preferredLanguages.push(value);
      }
    } else if (key === 'skillLevel') {
      context.skillLevel = value;
    } else if (key === 'responseStyle') {
      context.preferences.responseStyle = value;
    } else if (key === 'learningGoal') {
      if (!context.preferences.learningGoals.includes(value)) {
        context.preferences.learningGoals.push(value);
      }
    }

    context.lastInteraction = Date.now();
  }

  /**
   * Detect and update current topic
   */
  updateCurrentTopic(userId, messageData) {
    const context = this.getUserContext(userId);
    const topics = messageData.topics;

    if (topics.length > 0) {
      // Use the most specific topic
      context.currentTopic = topics[0];
      
      // Add to discussed topics
      topics.forEach(topic => {
        context.learningProgress.topicsDiscussed.add(topic);
      });
    }
  }

  /**
   * Extract topics from message
   */
  extractTopics(message) {
    const topics = [];
    const lowerMessage = message.toLowerCase();

    // Programming languages
    const languages = ['lua', 'luau', 'python', 'javascript', 'java', 'c++', 'cpp', 'c#', 'html', 'css'];
    for (const lang of languages) {
      if (lowerMessage.includes(lang)) {
        topics.push(`programming_${lang}`);
      }
    }

    // Programming concepts
    const concepts = [
      'function', 'variable', 'loop', 'array', 'object', 'class', 'method',
      'error', 'debug', 'syntax', 'algorithm', 'data structure', 'api',
      'database', 'web development', 'game development'
    ];
    
    for (const concept of concepts) {
      if (lowerMessage.includes(concept)) {
        topics.push(`concept_${concept.replace(' ', '_')}`);
      }
    }

    // Roblox-specific topics
    const robloxTopics = [
      'roblox', 'script', 'executor', 'exploit', 'game', 'player', 'workspace',
      'gui', 'remote', 'event', 'datastore', 'teleport', 'esp', 'aimbot'
    ];
    
    for (const topic of robloxTopics) {
      if (lowerMessage.includes(topic)) {
        topics.push(`roblox_${topic}`);
      }
    }

    return topics;
  }

  /**
   * Detect code in message
   */
  detectCodeInMessage(message) {
    const codePatterns = [
      /```[\s\S]*?```/g, // Code blocks
      /`[^`]+`/g, // Inline code
      /function\s+\w+/g, // Function definitions
      /local\s+\w+\s*=/g, // Lua variables
      /if\s+.*\s+then/g, // Lua conditionals
      /for\s+.*\s+do/g, // Lua loops
      /def\s+\w+/g, // Python functions
      /class\s+\w+/g, // Class definitions
    ];

    return codePatterns.some(pattern => pattern.test(message));
  }

  /**
   * Simple sentiment analysis
   */
  analyzeSentiment(message) {
    const positive = ['thanks', 'thank you', 'great', 'awesome', 'perfect', 'excellent', 'good', 'helpful'];
    const negative = ['error', 'problem', 'issue', 'broken', 'not working', 'help', 'stuck', 'confused'];
    
    const lowerMessage = message.toLowerCase();
    
    let positiveScore = 0;
    let negativeScore = 0;
    
    positive.forEach(word => {
      if (lowerMessage.includes(word)) positiveScore++;
    });
    
    negative.forEach(word => {
      if (lowerMessage.includes(word)) negativeScore++;
    });
    
    if (positiveScore > negativeScore) return 'positive';
    if (negativeScore > positiveScore) return 'negative';
    return 'neutral';
  }

  /**
   * Update learning progress
   */
  updateLearningProgress(userId, messageData) {
    const context = this.getUserContext(userId);
    
    if (!messageData.isBot) {
      context.learningProgress.questionsAsked++;
      
      if (messageData.codeDetected) {
        context.learningProgress.codeShared++;
      }
      
      // Detect if user is asking for error help
      if (messageData.content.toLowerCase().includes('error') || 
          messageData.content.toLowerCase().includes('not working')) {
        context.learningProgress.errorsFixed++;
      }
    }
  }

  /**
   * Get smart suggestions based on context
   */
  getSmartSuggestions(userId) {
    const context = this.getUserContext(userId);
    const suggestions = [];

    // Suggest based on current topic
    if (context.currentTopic) {
      if (context.currentTopic.includes('lua')) {
        suggestions.push('Would you like to learn more Lua concepts?');
        suggestions.push('Try our Lua tutorial: `/tutorial scripting`');
      }
      
      if (context.currentTopic.includes('error')) {
        suggestions.push('Need help debugging? Share your error message!');
        suggestions.push('Check our troubleshooting guide: `/tutorial troubleshooting`');
      }
    }

    // Suggest based on skill level
    if (context.skillLevel === 'beginner' && context.learningProgress.questionsAsked > 5) {
      suggestions.push('Ready for intermediate topics? Let me know!');
    }

    // Suggest based on learning goals
    if (context.preferences.learningGoals.includes('game_development')) {
      suggestions.push('Want to see some game development examples?');
    }

    return suggestions.slice(0, 3); // Return max 3 suggestions
  }

  /**
   * Clear user context
   */
  clearUserContext(userId) {
    this.userContexts.delete(userId);
  }

  /**
   * Get context statistics
   */
  getContextStats() {
    return {
      totalUsers: this.userContexts.size,
      activeUsers: Array.from(this.userContexts.values())
        .filter(ctx => Date.now() - ctx.lastInteraction < this.maxContextAge).length,
      totalMessages: Array.from(this.userContexts.values())
        .reduce((sum, ctx) => sum + ctx.messages.length, 0),
      memoryUsage: this.estimateMemoryUsage()
    };
  }

  /**
   * Cleanup old contexts
   */
  startCleanupInterval() {
    setInterval(() => {
      this.cleanupOldContexts();
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Remove old contexts
   */
  cleanupOldContexts() {
    const now = Date.now();
    const toDelete = [];

    for (const [userId, context] of this.userContexts.entries()) {
      if (now - context.lastInteraction > this.maxContextAge) {
        toDelete.push(userId);
      }
    }

    toDelete.forEach(userId => {
      this.userContexts.delete(userId);
    });

    // If still too many users, remove oldest
    if (this.userContexts.size > this.maxUsers) {
      const sortedUsers = Array.from(this.userContexts.entries())
        .sort((a, b) => a[1].lastInteraction - b[1].lastInteraction);
      
      const toRemove = sortedUsers.slice(0, sortedUsers.length - this.maxUsers);
      toRemove.forEach(([userId]) => {
        this.userContexts.delete(userId);
      });
    }
  }

  /**
   * Helper methods
   */
  generateSessionId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  generateMessageId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
  }

  getTimeAgo(timestamp) {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);
    if (seconds < 60) return `${seconds}s ago`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ago`;
    return `${Math.floor(seconds / 3600)}h ago`;
  }

  estimateMemoryUsage() {
    const contexts = Array.from(this.userContexts.values());
    const avgContextSize = 2000; // Rough estimate in bytes
    return `${Math.round(contexts.length * avgContextSize / 1024)}KB`;
  }
}

// Create singleton instance
export const contextMemory = new ContextMemory();
export default contextMemory;
