import { config } from './config.js';
import { SecurityManager } from './security.js';
import { MadaraEmbedBuilder } from './embedBuilder.js';
import { logDiscordEvent, logError } from './logger.js';

// Import command handlers
import { askCommand } from './ask.js';
import { tutorialCommand } from './tutorial.js';
import { helpCommand } from './help.js';
import { pingCommand } from './ping.js';
import { statusCommand } from './status.js';
import { aboutCommand } from './about.js';
import { scriptsCommand } from './scripts.js';
import { supportCommand } from './support.js';

/**
 * Slash Command handler for PROJECT MADARA Discord bot
 */
export class SlashCommandHandler {
  constructor() {
    this.commands = new Map();
    this.loadSlashCommands();
  }

  /**
   * Load all slash commands into the handler
   */
  loadSlashCommands() {
    // Map slash commands to their handlers
    this.commands.set('ask', this.handleAskCommand.bind(this));
    this.commands.set('tutorial', this.handleTutorialCommand.bind(this));
    this.commands.set('help', this.handleHelpCommand.bind(this));
    this.commands.set('ping', this.handlePingCommand.bind(this));
    this.commands.set('status', this.handleStatusCommand.bind(this));
    this.commands.set('about', this.handleAboutCommand.bind(this));
    this.commands.set('scripts', this.handleScriptsCommand.bind(this));
    this.commands.set('support', this.handleSupportCommand.bind(this));

    // Admin commands
    this.commands.set('admin-reload', this.handleAdminReload.bind(this));
    this.commands.set('admin-stats', this.handleAdminStats.bind(this));
    this.commands.set('admin-cache', this.handleAdminCache.bind(this));

    console.log(`Loaded ${this.commands.size} slash commands`);
  }

  /**
   * Handle incoming slash command interactions
   */
  async handleInteraction(interaction, client) {
    if (!interaction.isChatInputCommand()) return;

    const { commandName } = interaction;

    // Check if command exists
    if (!this.commands.has(commandName)) {
      await interaction.reply({
        content: '❌ Unknown command.',
        ephemeral: true
      });
      return;
    }

    try {
      // Security checks
      if (!this.performSecurityChecks(interaction)) {
        return;
      }

      // Execute the command
      await this.commands.get(commandName)(interaction, client);

      // Log successful interaction
      logDiscordEvent('slash_command_executed', {
        userId: interaction.user.id,
        username: interaction.user.username,
        commandName,
        guildId: interaction.guild?.id,
        channelId: interaction.channel?.id
      });

    } catch (error) {
      logError(error, {
        userId: interaction.user.id,
        commandName,
        context: 'slash_command_execution'
      });

      const errorEmbed = MadaraEmbedBuilder.createErrorEmbed(
        'Command Error',
        'An error occurred while executing this command. Please try again later.'
      );

      if (interaction.replied || interaction.deferred) {
        await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
      } else {
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
      }
    }
  }

  /**
   * Perform security checks for slash commands
   */
  performSecurityChecks(interaction) {
    // Check if guild is allowed
    if (interaction.guild && !SecurityManager.isGuildAllowed(interaction.guild.id)) {
      const embed = MadaraEmbedBuilder.createUnauthorizedEmbed('guild');
      interaction.reply({ embeds: [embed], ephemeral: true });
      return false;
    }

    // Check if channel is allowed for AI interactions
    if (['ask'].includes(interaction.commandName) && !SecurityManager.isChannelAllowed(interaction.channel.id)) {
      const embed = MadaraEmbedBuilder.createUnauthorizedEmbed('channel');
      interaction.reply({ embeds: [embed], ephemeral: true });
      return false;
    }

    // Check rate limiting for AI commands
    if (['ask'].includes(interaction.commandName)) {
      const rateLimitCheck = SecurityManager.checkRateLimit(interaction.user.id);
      if (!rateLimitCheck.allowed) {
        const embed = MadaraEmbedBuilder.createRateLimitEmbed(rateLimitCheck.resetIn);
        interaction.reply({ embeds: [embed], ephemeral: true });
        return false;
      }
    }

    return true;
  }

  /**
   * Handle ask slash command
   */
  async handleAskCommand(interaction, client) {
    const question = interaction.options.getString('question');
    const language = interaction.options.getString('language');
    const isPrivate = interaction.options.getBoolean('private') || false;

    // Create a mock message object for compatibility with existing ask command
    const mockMessage = {
      author: interaction.user,
      guild: interaction.guild,
      channel: interaction.channel,
      reply: async (options) => {
        if (interaction.replied || interaction.deferred) {
          return await interaction.followUp({ ...options, ephemeral: isPrivate });
        } else {
          return await interaction.reply({ ...options, ephemeral: isPrivate });
        }
      }
    };

    // Prepare args with language context if provided
    let args = question.split(' ');
    if (language && language !== 'other') {
      args = [`[${language.toUpperCase()}]`, ...args];
    }

    // Defer reply for longer processing
    await interaction.deferReply({ ephemeral: isPrivate });

    // Execute the existing ask command logic
    await askCommand.execute(mockMessage, args, client);
  }

  /**
   * Handle tutorial slash command
   */
  async handleTutorialCommand(interaction, client) {
    const topic = interaction.options.getString('topic');

    const mockMessage = {
      author: interaction.user,
      guild: interaction.guild,
      channel: interaction.channel,
      reply: async (options) => {
        if (interaction.replied || interaction.deferred) {
          return await interaction.followUp(options);
        } else {
          return await interaction.reply(options);
        }
      }
    };

    const args = topic ? [topic] : [];
    await tutorialCommand.execute(mockMessage, args, client);
  }

  /**
   * Handle help slash command
   */
  async handleHelpCommand(interaction, client) {
    const category = interaction.options.getString('category');

    const mockMessage = {
      author: interaction.user,
      guild: interaction.guild,
      channel: interaction.channel,
      reply: async (options) => {
        if (interaction.replied || interaction.deferred) {
          return await interaction.followUp(options);
        } else {
          return await interaction.reply(options);
        }
      }
    };

    const args = category ? [category] : [];
    await helpCommand.execute(mockMessage, args);
  }

  /**
   * Handle ping slash command
   */
  async handlePingCommand(interaction, client) {
    const mockMessage = {
      author: interaction.user,
      guild: interaction.guild,
      channel: interaction.channel,
      reply: async (options) => {
        if (interaction.replied || interaction.deferred) {
          return await interaction.followUp(options);
        } else {
          return await interaction.reply(options);
        }
      }
    };

    await pingCommand.execute(mockMessage, [], client);
  }

  /**
   * Handle status slash command
   */
  async handleStatusCommand(interaction, client) {
    const mockMessage = {
      author: interaction.user,
      guild: interaction.guild,
      channel: interaction.channel,
      reply: async (options) => {
        if (interaction.replied || interaction.deferred) {
          return await interaction.followUp(options);
        } else {
          return await interaction.reply(options);
        }
      }
    };

    await statusCommand.execute(mockMessage, [], client);
  }

  /**
   * Handle about slash command
   */
  async handleAboutCommand(interaction, client) {
    const mockMessage = {
      author: interaction.user,
      guild: interaction.guild,
      channel: interaction.channel,
      reply: async (options) => {
        if (interaction.replied || interaction.deferred) {
          return await interaction.followUp(options);
        } else {
          return await interaction.reply(options);
        }
      }
    };

    await aboutCommand.execute(mockMessage, [], client);
  }

  /**
   * Handle scripts slash command
   */
  async handleScriptsCommand(interaction, client) {
    const category = interaction.options.getString('category');

    const mockMessage = {
      author: interaction.user,
      guild: interaction.guild,
      channel: interaction.channel,
      reply: async (options) => {
        if (interaction.replied || interaction.deferred) {
          return await interaction.followUp(options);
        } else {
          return await interaction.reply(options);
        }
      }
    };

    const args = category ? [category] : [];
    await scriptsCommand.execute(mockMessage, args, client);
  }

  /**
   * Handle support slash command
   */
  async handleSupportCommand(interaction, client) {
    const issue = interaction.options.getString('issue');

    const mockMessage = {
      author: interaction.user,
      guild: interaction.guild,
      channel: interaction.channel,
      reply: async (options) => {
        if (interaction.replied || interaction.deferred) {
          return await interaction.followUp(options);
        } else {
          return await interaction.reply(options);
        }
      }
    };

    const args = issue ? [issue] : [];
    await supportCommand.execute(mockMessage, args, client);
  }

  /**
   * Handle admin reload command
   */
  async handleAdminReload(interaction, client) {
    if (!SecurityManager.isOwner(interaction.user.id)) {
      await interaction.reply({
        content: '❌ You do not have permission to use this command.',
        ephemeral: true
      });
      return;
    }

    await interaction.reply({
      content: '🔄 Reloading bot configuration...',
      ephemeral: true
    });

    // Reload logic would go here
    // For now, just acknowledge
    await interaction.followUp({
      content: '✅ Bot configuration reloaded successfully.',
      ephemeral: true
    });
  }

  /**
   * Handle admin stats command
   */
  async handleAdminStats(interaction, client) {
    if (!SecurityManager.isOwner(interaction.user.id)) {
      await interaction.reply({
        content: '❌ You do not have permission to use this command.',
        ephemeral: true
      });
      return;
    }

    const embed = MadaraEmbedBuilder.createEmbed({
      title: '📊 Admin Statistics',
      description: 'Detailed bot statistics and analytics',
      fields: [
        {
          name: '🏠 Servers',
          value: client.guilds.cache.size.toString(),
          inline: true
        },
        {
          name: '👥 Users',
          value: client.users.cache.size.toString(),
          inline: true
        },
        {
          name: '💾 Memory',
          value: `${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`,
          inline: true
        }
      ]
    });

    await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  /**
   * Handle admin cache command
   */
  async handleAdminCache(interaction, client) {
    if (!SecurityManager.isOwner(interaction.user.id)) {
      await interaction.reply({
        content: '❌ You do not have permission to use this command.',
        ephemeral: true
      });
      return;
    }

    const action = interaction.options.getString('action');

    await interaction.reply({
      content: `🔄 Performing cache action: ${action}...`,
      ephemeral: true
    });

    // Cache management logic would go here
    await interaction.followUp({
      content: `✅ Cache action "${action}" completed successfully.`,
      ephemeral: true
    });
  }

  /**
   * Get command statistics
   */
  getStats() {
    return {
      totalSlashCommands: this.commands.size,
      loadedCommands: Array.from(this.commands.keys())
    };
  }
}

export default SlashCommandHandler;
