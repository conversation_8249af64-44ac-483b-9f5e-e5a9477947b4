/**
 * Smart FAQ Matcher for PROJECT MADARA Discord bot
 * Intelligently matches user questions to FAQ responses
 */
export class FAQMatcher {
  constructor() {
    this.faqDatabase = this.initializeFAQDatabase();
    this.confidenceThreshold = 0.7;
  }

  /**
   * Initialize comprehensive FAQ database
   */
  initializeFAQDatabase() {
    return [
      {
        id: 'what_is_madara',
        category: 'general',
        keywords: ['what is', 'about', 'tell me about', 'project madara', 'madara'],
        patterns: [
          /what\s+is\s+project\s+madara/i,
          /about\s+project\s+madara/i,
          /tell\s+me\s+about\s+madara/i,
          /what\s+is\s+madara/i
        ],
        question: 'What is Project Madara?',
        answer: 'Project Madara is a Roblox script hub created by Sabin07, which supports multiple games and provides quality Roblox scripts.',
        tags: ['general', 'info', 'about']
      },
      {
        id: 'creator',
        category: 'general',
        keywords: ['who created', 'who made', 'creator', 'developer', 'sabin07'],
        patterns: [
          /who\s+(created|made|developed)\s+.*madara/i,
          /creator\s+of\s+madara/i,
          /who\s+is\s+the\s+creator/i,
          /made\s+by\s+who/i
        ],
        question: 'Who created Project Madara?',
        answer: 'Project Madara was created by Sabin07.',
        tags: ['general', 'creator', 'info']
      },
      {
        id: 'pricing',
        category: 'general',
        keywords: ['free', 'cost', 'price', 'how much', 'money', 'pay'],
        patterns: [
          /is\s+.*\s+free/i,
          /how\s+much\s+.*\s+cost/i,
          /price\s+of/i,
          /do\s+i\s+have\s+to\s+pay/i,
          /costs\s+money/i
        ],
        question: 'Is Project Madara free?',
        answer: 'Yes, Project Madara is free to use. However, if I feel like it I might add Premium Keys.',
        tags: ['general', 'pricing', 'free']
      },
      {
        id: 'getting_started',
        category: 'usage',
        keywords: ['how to start', 'getting started', 'begin', 'first time', 'new user'],
        patterns: [
          /how\s+to\s+(get\s+)?started/i,
          /getting\s+started/i,
          /how\s+to\s+begin/i,
          /first\s+time\s+using/i,
          /new\s+to\s+this/i
        ],
        question: 'How do I get started?',
        answer: 'Getting started is easy! First, ensure you have a compatible script executor. Then, check our script page for the Roblox script that you need for your game.',
        tags: ['usage', 'beginner', 'start']
      },
      {
        id: 'script_usage',
        category: 'usage',
        keywords: ['how to use', 'use scripts', 'run scripts', 'execute'],
        patterns: [
          /how\s+to\s+use\s+.*\s+script/i,
          /how\s+do\s+i\s+run\s+.*\s+script/i,
          /execute\s+.*\s+script/i,
          /use\s+these\s+scripts/i
        ],
        question: 'How do I use these scripts?',
        answer: 'Each Roblox script comes with detailed instructions. Generally, you\'ll need a script executor compatible with Roblox. Copy the Roblox script from our platform and run it using your preferred executor. We provide step-by-step guides for popular executors.',
        tags: ['usage', 'scripts', 'execution']
      },
      {
        id: 'script_safety',
        category: 'security',
        keywords: ['safe', 'security', 'virus', 'malware', 'trust'],
        patterns: [
          /are\s+.*\s+scripts\s+safe/i,
          /safe\s+to\s+use/i,
          /virus\s+free/i,
          /can\s+i\s+trust/i,
          /security\s+of\s+scripts/i
        ],
        question: 'Are these scripts safe?',
        answer: 'We thoroughly verify and test all Roblox scripts before publishing. However, we recommend using them responsibly and at your own discretion. Always ensure you\'re downloading from our official platform and follow our safety guidelines.',
        tags: ['security', 'safety', 'trust']
      },
      {
        id: 'script_not_working',
        category: 'troubleshooting',
        keywords: ['not working', 'broken', 'error', 'issue', 'problem'],
        patterns: [
          /script\s+.*\s+not\s+working/i,
          /.*\s+script\s+.*\s+broken/i,
          /having\s+issues\s+with/i,
          /script\s+.*\s+error/i,
          /problem\s+with\s+script/i
        ],
        question: 'Why isn\'t my script working?',
        answer: 'Common issues include: incompatible executor, outdated script version, or incorrect implementation. Make sure you\'re using a supported executor, the Roblox script is up to date, and you\'ve followed all instructions. Check our troubleshooting guide or contact support for assistance.',
        tags: ['troubleshooting', 'issues', 'problems']
      },
      {
        id: 'script_updates',
        category: 'maintenance',
        keywords: ['update', 'updated', 'latest version', 'new version'],
        patterns: [
          /how\s+often\s+.*\s+updated/i,
          /script\s+updates/i,
          /latest\s+version/i,
          /when\s+.*\s+update/i
        ],
        question: 'How often are scripts updated?',
        answer: 'We regularly update our Roblox scripts to ensure compatibility with the latest Roblox updates. Critical updates are released immediately, while feature updates follow a scheduled release cycle.',
        tags: ['maintenance', 'updates', 'versions']
      },
      {
        id: 'custom_scripts',
        category: 'requests',
        keywords: ['custom script', 'request', 'make script', 'create script'],
        patterns: [
          /custom\s+script/i,
          /can\s+you\s+make\s+.*\s+script/i,
          /request\s+.*\s+script/i,
          /create\s+.*\s+script\s+for/i
        ],
        question: 'Can I request custom scripts?',
        answer: 'Yes! We accept custom Roblox script requests through our script request system. While we can\'t guarantee all requests will be fulfilled, we review each one and prioritize based on community demand and feasibility.',
        tags: ['requests', 'custom', 'development']
      },
      {
        id: 'support',
        category: 'support',
        keywords: ['help', 'support', 'contact', 'assistance'],
        patterns: [
          /need\s+help/i,
          /contact\s+support/i,
          /get\s+assistance/i,
          /help\s+me\s+with/i
        ],
        question: 'How can I get help?',
        answer: 'You can get help through our contact form, community forums, or by checking our comprehensive documentation.',
        tags: ['support', 'help', 'contact']
      }
    ];
  }

  /**
   * Find the best FAQ match for a user question
   */
  findBestMatch(userQuestion) {
    const normalizedQuestion = userQuestion.toLowerCase().trim();
    let bestMatch = null;
    let highestScore = 0;

    for (const faq of this.faqDatabase) {
      const score = this.calculateMatchScore(normalizedQuestion, faq);
      
      if (score > highestScore && score >= this.confidenceThreshold) {
        highestScore = score;
        bestMatch = {
          ...faq,
          confidence: score,
          matchType: this.getMatchType(normalizedQuestion, faq)
        };
      }
    }

    return bestMatch;
  }

  /**
   * Calculate match score between question and FAQ entry
   */
  calculateMatchScore(question, faq) {
    let score = 0;
    let maxScore = 0;

    // Pattern matching (highest weight)
    for (const pattern of faq.patterns) {
      maxScore += 0.4;
      if (pattern.test(question)) {
        score += 0.4;
        break; // Only count one pattern match
      }
    }

    // Keyword matching
    for (const keyword of faq.keywords) {
      maxScore += 0.1;
      if (question.includes(keyword.toLowerCase())) {
        score += 0.1;
      }
    }

    // Semantic similarity (basic)
    const questionWords = question.split(/\s+/);
    const faqWords = faq.question.toLowerCase().split(/\s+/);
    
    let commonWords = 0;
    for (const word of questionWords) {
      if (faqWords.includes(word) && word.length > 2) {
        commonWords++;
      }
    }
    
    const semanticScore = commonWords / Math.max(questionWords.length, faqWords.length);
    score += semanticScore * 0.3;
    maxScore += 0.3;

    return maxScore > 0 ? score / maxScore : 0;
  }

  /**
   * Determine the type of match found
   */
  getMatchType(question, faq) {
    // Check if it's a pattern match
    for (const pattern of faq.patterns) {
      if (pattern.test(question)) {
        return 'pattern';
      }
    }

    // Check if it's a keyword match
    for (const keyword of faq.keywords) {
      if (question.includes(keyword.toLowerCase())) {
        return 'keyword';
      }
    }

    return 'semantic';
  }

  /**
   * Get FAQ suggestions based on category
   */
  getFAQSuggestions(category = null, limit = 5) {
    let faqs = this.faqDatabase;
    
    if (category) {
      faqs = faqs.filter(faq => faq.category === category);
    }

    return faqs
      .slice(0, limit)
      .map(faq => ({
        question: faq.question,
        category: faq.category,
        id: faq.id
      }));
  }

  /**
   * Get all FAQ categories
   */
  getCategories() {
    const categories = [...new Set(this.faqDatabase.map(faq => faq.category))];
    return categories.map(category => ({
      name: category,
      count: this.faqDatabase.filter(faq => faq.category === category).length
    }));
  }

  /**
   * Search FAQs by text
   */
  searchFAQs(searchText, limit = 10) {
    const normalizedSearch = searchText.toLowerCase();
    
    return this.faqDatabase
      .map(faq => ({
        ...faq,
        relevance: this.calculateSearchRelevance(normalizedSearch, faq)
      }))
      .filter(faq => faq.relevance > 0)
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, limit);
  }

  /**
   * Calculate search relevance for FAQ entries
   */
  calculateSearchRelevance(searchText, faq) {
    let relevance = 0;

    // Check question text
    if (faq.question.toLowerCase().includes(searchText)) {
      relevance += 0.5;
    }

    // Check answer text
    if (faq.answer.toLowerCase().includes(searchText)) {
      relevance += 0.3;
    }

    // Check keywords
    for (const keyword of faq.keywords) {
      if (keyword.toLowerCase().includes(searchText) || searchText.includes(keyword.toLowerCase())) {
        relevance += 0.2;
      }
    }

    // Check tags
    for (const tag of faq.tags) {
      if (tag.toLowerCase().includes(searchText)) {
        relevance += 0.1;
      }
    }

    return relevance;
  }
}

export default FAQMatcher;
