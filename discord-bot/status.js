import { MadaraEmbedBuilder } from './embedBuilder.js';
import { aiService } from './aiService.js';

export const statusCommand = {
  name: 'status',
  description: 'Show bot status and statistics',
  aliases: ['stats', 'info'],
  
  async execute(message, args, client) {
    // Calculate uptime
    const uptime = process.uptime();
    const days = Math.floor(uptime / 86400);
    const hours = Math.floor((uptime % 86400) / 3600);
    const minutes = Math.floor((uptime % 3600) / 60);
    const seconds = Math.floor(uptime % 60);
    
    const uptimeString = `${days}d ${hours}h ${minutes}m ${seconds}s`;
    
    // Calculate memory usage
    const memoryUsage = process.memoryUsage();
    const memoryMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
    
    // Get bot statistics
    const stats = {
      ping: Math.round(client.ws.ping),
      uptime: uptimeString,
      guilds: client.guilds.cache.size,
      users: client.users.cache.size,
      memory: `${memoryMB} MB`
    };
    
    const embed = MadaraEmbedBuilder.createStatusEmbed(stats);
    
    // Add AI service status
    try {
      const aiStats = aiService.getStats();
      embed.addFields([
        {
          name: '🤖 AI Service',
          value: `**Provider:** ${aiStats.provider}\n**Model:** ${aiStats.model}\n**Status:** ✅ Online`,
          inline: true
        }
      ]);
    } catch (error) {
      embed.addFields([
        {
          name: '🤖 AI Service',
          value: '**Status:** ❌ Offline',
          inline: true
        }
      ]);
    }
    
    await message.reply({ embeds: [embed] });
  }
};

export default statusCommand;
