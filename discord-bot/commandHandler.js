import { config } from './config.js';
import { SecurityManager } from './security.js';
import { MadaraEmbedBuilder } from './embedBuilder.js';
import { logDiscordEvent } from './logger.js';
import { messageContext } from './messageContext.js';

// Import commands
import { helpCommand } from './help.js';
import { pingCommand } from './ping.js';
import { askCommand } from './ask.js';
import { aboutCommand } from './about.js';
import { scriptsCommand } from './scripts.js';
import { supportCommand } from './support.js';
import { tutorialCommand } from './tutorial.js';

/**
 * Command handler for PROJECT MADARA Discord bot
 */
export class CommandHandler {
  constructor() {
    this.commands = new Map();
    this.aliases = new Map();
    this.prefix = config.discord.prefix;
    
    this.loadCommands();
  }

  /**
   * Load all commands into the handler
   */
  loadCommands() {
    const commands = [
      helpCommand,
      pingCommand,
      askCommand,
      aboutCommand,
      scriptsCommand,
      supportCommand,
      tutorialCommand
    ];

    commands.forEach(command => {
      this.commands.set(command.name, command);
      
      // Register aliases
      if (command.aliases) {
        command.aliases.forEach(alias => {
          this.aliases.set(alias, command.name);
        });
      }
    });

    console.log(`Loaded ${this.commands.size} commands with ${this.aliases.size} aliases`);
  }

  /**
   * Handle incoming messages
   */
  async handleMessage(message, client) {
    // Add all messages to context (except bot messages)
    if (!message.author.bot) {
      messageContext.addMessage(message);
    }

    // Ignore bot messages for responses
    if (message.author.bot) return;

    // Check if bot is mentioned
    if (message.mentions.has(client.user)) {
      return await this.handleMention(message, client);
    }

    // Check if message is a reply to the bot
    if (message.reference && message.reference.messageId) {
      try {
        const repliedMessage = await message.channel.messages.fetch(message.reference.messageId);
        if (repliedMessage.author.id === client.user.id) {
          return await this.handleReply(message, client);
        }
      } catch (error) {
        // Ignore errors fetching replied message
      }
    }

    // Don't respond to regular messages anymore - only mentions and replies
    return;
  }

  /**
   * Get command by name or alias
   */
  getCommand(name) {
    // Check direct command name
    if (this.commands.has(name)) {
      return this.commands.get(name);
    }

    // Check aliases
    if (this.aliases.has(name)) {
      const commandName = this.aliases.get(name);
      return this.commands.get(commandName);
    }

    return null;
  }

  /**
   * Get all commands for help display
   */
  getAllCommands() {
    return Array.from(this.commands.values());
  }

  /**
   * Handle bot mentions
   */
  async handleMention(message, client) {
    return await this.handleAIInteraction(message, client, 'mention');
  }

  /**
   * Handle replies to bot messages
   */
  async handleReply(message, client) {
    return await this.handleAIInteraction(message, client, 'reply');
  }

  /**
   * Handle AI interactions (mentions and replies)
   */
  async handleAIInteraction(message, client, type) {
    // Check if guild is allowed
    if (message.guild && !SecurityManager.isGuildAllowed(message.guild.id)) {
      const embed = MadaraEmbedBuilder.createUnauthorizedEmbed('guild');
      return await message.reply({ embeds: [embed] });
    }

    // Check if channel is allowed for AI interactions
    if (!SecurityManager.isChannelAllowed(message.channel.id)) {
      const embed = MadaraEmbedBuilder.createUnauthorizedEmbed('channel');
      return await message.reply({ embeds: [embed] });
    }

    // Check rate limiting
    const rateLimitCheck = SecurityManager.checkRateLimit(message.author.id);
    if (!rateLimitCheck.allowed) {
      const embed = MadaraEmbedBuilder.createRateLimitEmbed(rateLimitCheck.resetIn);
      return await message.reply({ embeds: [embed] });
    }

    // Get the message content (remove mention if it's a mention)
    let content = message.content;
    if (type === 'mention') {
      content = content.replace(/<@!?\d+>/g, '').trim();
    }

    // If no content, show help
    if (!content || content.length === 0) {
      const embed = MadaraEmbedBuilder.createInfoEmbed(
        'Hello!',
        `I'm **MADARA AI**, your PROJECT MADARA assistant.\n\n` +
        `**How to use:**\n` +
        `• Mention me with a question\n` +
        `• Reply to any of my messages\n\n` +
        `**I can help with:**\n` +
        `• Roblox scripting questions\n` +
        `• Code fixes and explanations\n` +
        `• Programming in any language\n` +
        `• PROJECT MADARA info`
      );
      return await message.reply({ embeds: [embed] });
    }

    // Show typing indicator
    await message.channel.sendTyping();

    // Use AI service to generate response
    const askCommand = this.commands.get('ask');
    if (askCommand) {
      const args = content.split(' ');
      return await askCommand.execute(message, args, client);
    }

    // Log interaction
    logDiscordEvent(`bot_${type}`, {
      userId: message.author.id,
      username: message.author.username,
      guildId: message.guild?.id,
      channelId: message.channel.id,
      content: content.substring(0, 100)
    });
  }

  /**
   * Get command statistics
   */
  getStats() {
    return {
      totalCommands: this.commands.size,
      totalAliases: this.aliases.size,
      prefix: this.prefix
    };
  }
}

export default CommandHandler;
