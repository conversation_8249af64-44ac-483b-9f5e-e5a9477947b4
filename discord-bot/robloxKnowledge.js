/**
 * Comprehensive Roblox/Luau Knowledge Base for PROJECT MADARA AI
 */

export const RobloxKnowledge = {
  // Core Roblox Services with methods and events
  services: {
    Players: {
      methods: ['GetPlayerByUserId', 'GetPlayerFromCharacter', 'GetPlayers', 'CreateHumanoidDescription', 'GetUserIdFromNameAsync', 'GetNameFromUserIdAsync'],
      events: ['PlayerAdded', 'PlayerRemoving', 'CharacterAdded', 'CharacterRemoving'],
      properties: ['LocalPlayer', 'MaxPlayers', 'NumPlayers', 'RespawnTime'],
      exploitUseful: ['LocalPlayer.Character', 'LocalPlayer.PlayerGui', 'LocalPlayer.Backpack']
    },

    Workspace: {
      methods: ['Raycast', 'GetPartBoundsInBox', 'GetPartBoundsInRegion3', 'FindFirstChild', 'FindFirstChildOfClass', 'GetChildren', 'GetDescendants', 'WaitForChild'],
      events: ['ChildAdded', 'ChildRemoved', 'DescendantAdded', 'DescendantRemoving'],
      properties: ['CurrentCamera', 'Gravity', 'FallenPartsDestroyHeight', 'StreamingEnabled'],
      exploitUseful: ['CurrentCamera for ESP/Aimbot', 'GetDescendants() for finding all parts']
    },
    
    RunService: {
      methods: ['IsClient', 'IsServer', 'IsStudio', 'BindToRenderStep', 'UnbindFromRenderStep'],
      events: ['Heartbeat', 'Stepped', 'RenderStepped', 'PreAnimation', 'PostSimulation'],
      properties: ['ClientGitHash', 'ServerGitHash']
    },
    
    UserInputService: {
      methods: ['IsKeyDown', 'IsMouseButtonPressed', 'GetMouseLocation', 'GetGamepadState'],
      events: ['InputBegan', 'InputEnded', 'InputChanged', 'JumpRequest', 'TouchStarted'],
      properties: ['MouseEnabled', 'KeyboardEnabled', 'TouchEnabled', 'GamepadEnabled']
    },
    
    TweenService: {
      methods: ['Create', 'GetValue'],
      enums: ['EasingStyle', 'EasingDirection'],
      easingStyles: ['Linear', 'Quad', 'Cubic', 'Quart', 'Quint', 'Sine', 'Back', 'Bounce', 'Elastic', 'Exponential', 'Circular'],
      easingDirections: ['In', 'Out', 'InOut']
    },
    
    HttpService: {
      methods: ['GetAsync', 'PostAsync', 'RequestAsync', 'JSONEncode', 'JSONDecode', 'UrlEncode'],
      properties: ['HttpEnabled'],
      notes: 'Requires HttpEnabled = true in game settings'
    },
    
    DataStoreService: {
      methods: ['GetDataStore', 'GetGlobalDataStore', 'GetOrderedDataStore', 'GetRequestBudgetForRequestType'],
      types: ['DataStore', 'OrderedDataStore', 'GlobalDataStore'],
      limits: 'Rate limits apply - use pcall for error handling'
    },

    // Exploit-specific services
    Camera: {
      properties: ['CFrame', 'Focus', 'FieldOfView', 'ViewportSize'],
      methods: ['WorldToViewportPoint', 'WorldToScreenPoint', 'ScreenPointToRay'],
      exploitUseful: ['WorldToScreenPoint for ESP', 'ScreenPointToRay for aimbot']
    },

    Mouse: {
      properties: ['Hit', 'Target', 'X', 'Y', 'UnitRay'],
      events: ['Button1Down', 'Button1Up', 'Button2Down', 'Button2Up', 'Move'],
      exploitUseful: ['Hit.Position for teleportation', 'Target for part detection']
    },

    Humanoid: {
      properties: ['Health', 'MaxHealth', 'WalkSpeed', 'JumpPower', 'JumpHeight', 'PlatformStand'],
      methods: ['MoveTo', 'ChangeState', 'GetState'],
      events: ['Died', 'HealthChanged', 'StateChanged'],
      exploitUseful: ['WalkSpeed modification', 'JumpPower/JumpHeight for speed hacks']
    },

    Character: {
      parts: ['Head', 'Torso', 'HumanoidRootPart', 'LeftArm', 'RightArm', 'LeftLeg', 'RightLeg'],
      methods: ['FindFirstChild', 'WaitForChild'],
      exploitUseful: ['HumanoidRootPart.CFrame for teleportation', 'Head for ESP']
    }
  },

  // Common code patterns and examples
  patterns: {
    playerJoined: `game.Players.PlayerAdded:Connect(function(player)
    -- Create leaderstats
    local leaderstats = Instance.new("Folder")
    leaderstats.Name = "leaderstats"
    leaderstats.Parent = player
    
    local coins = Instance.new("IntValue")
    coins.Name = "Coins"
    coins.Value = 0
    coins.Parent = leaderstats
end)`,

    remoteEvent: `-- ServerScript
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local remoteEvent = ReplicatedStorage:WaitForChild("RemoteEvent")

remoteEvent.OnServerEvent:Connect(function(player, data)
    -- Validate data on server
    if typeof(data) == "string" and #data <= 100 then
        -- Process request
    end
end)

-- LocalScript
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local remoteEvent = ReplicatedStorage:WaitForChild("RemoteEvent")

remoteEvent:FireServer("Hello Server!")`,

    dataStore: `local DataStoreService = game:GetService("DataStoreService")
local playerData = DataStoreService:GetDataStore("PlayerData")

local function saveData(player)
    local success, errorMessage = pcall(function()
        local data = {
            coins = player.leaderstats.Coins.Value,
            level = player.leaderstats.Level.Value
        }
        playerData:SetAsync(player.UserId, data)
    end)
    
    if not success then
        warn("Failed to save data for " .. player.Name .. ": " .. errorMessage)
    end
end`,

    raycast: `local Workspace = game:GetService("Workspace")

local raycastParams = RaycastParams.new()
raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
raycastParams.FilterDescendantsInstances = {character}

local raycastResult = Workspace:Raycast(origin, direction, raycastParams)

if raycastResult then
    local hitPart = raycastResult.Instance
    local hitPosition = raycastResult.Position
    local hitNormal = raycastResult.Normal
end`,

    tween: `local TweenService = game:GetService("TweenService")

local part = workspace.Part
local tweenInfo = TweenInfo.new(
    2, -- Duration
    Enum.EasingStyle.Quad,
    Enum.EasingDirection.Out,
    0, -- Repeat count
    false, -- Reverses
    0 -- Delay
)

local tween = TweenService:Create(part, tweenInfo, {
    Position = Vector3.new(0, 10, 0),
    Transparency = 0.5
})

tween:Play()`,

    // Exploit-specific patterns
    esp: `-- Basic ESP
local function createESP(player)
    local character = player.Character
    if not character then return end

    local head = character:FindFirstChild("Head")
    if not head then return end

    local billboard = Instance.new("BillboardGui")
    billboard.Size = UDim2.new(0, 100, 0, 50)
    billboard.StudsOffset = Vector3.new(0, 2, 0)
    billboard.Parent = head

    local nameLabel = Instance.new("TextLabel")
    nameLabel.Size = UDim2.new(1, 0, 1, 0)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = player.Name
    nameLabel.TextColor3 = Color3.new(1, 1, 1)
    nameLabel.Parent = billboard
end`,

    aimbot: `-- Basic Aimbot
local camera = workspace.CurrentCamera
local players = game:GetService("Players")
local localPlayer = players.LocalPlayer

local function getClosestPlayer()
    local closestPlayer = nil
    local shortestDistance = math.huge

    for _, player in pairs(players:GetPlayers()) do
        if player ~= localPlayer and player.Character and player.Character:FindFirstChild("Head") then
            local head = player.Character.Head
            local screenPoint = camera:WorldToViewportPoint(head.Position)
            local distance = (Vector2.new(screenPoint.X, screenPoint.Y) - Vector2.new(camera.ViewportSize.X/2, camera.ViewportSize.Y/2)).Magnitude

            if distance < shortestDistance then
                shortestDistance = distance
                closestPlayer = player
            end
        end
    end

    return closestPlayer
end`,

    speedHack: `-- Speed Hack
local player = game.Players.LocalPlayer
local character = player.Character or player.CharacterAdded:Wait()
local humanoid = character:WaitForChild("Humanoid")

-- Modify walk speed
humanoid.WalkSpeed = 50 -- Default is 16

-- Modify jump power (older games)
humanoid.JumpPower = 100 -- Default is 50

-- Modify jump height (newer games)
humanoid.JumpHeight = 50 -- Default is 7.2`,

    teleport: `-- Teleportation
local player = game.Players.LocalPlayer
local character = player.Character or player.CharacterAdded:Wait()
local humanoidRootPart = character:WaitForChild("HumanoidRootPart")

-- Teleport to position
humanoidRootPart.CFrame = CFrame.new(Vector3.new(0, 50, 0))

-- Teleport to another player
local targetPlayer = game.Players:FindFirstChild("TargetPlayerName")
if targetPlayer and targetPlayer.Character then
    local targetHRP = targetPlayer.Character:FindFirstChild("HumanoidRootPart")
    if targetHRP then
        humanoidRootPart.CFrame = targetHRP.CFrame
    end
end`,

    noclip: `-- Noclip
local player = game.Players.LocalPlayer
local character = player.Character or player.CharacterAdded:Wait()

local function noclip()
    for _, part in pairs(character:GetDescendants()) do
        if part:IsA("BasePart") and part.CanCollide then
            part.CanCollide = false
        end
    end
end

-- Run noclip continuously
game:GetService("RunService").Stepped:Connect(noclip)`
  },

  // Security best practices
  security: {
    remoteValidation: 'Always validate data on server side',
    rateLimit: 'Implement rate limiting for RemoteEvents',
    sanitizeInput: 'Sanitize user input before processing',
    antiExploit: 'Use sanity checks for player actions',
    serverAuthority: 'Server has final authority on game state'
  },

  // Performance tips
  performance: {
    heartbeat: 'Use RunService.Heartbeat for frame-based updates',
    connections: 'Disconnect unused connections to prevent memory leaks',
    instances: 'Reuse instances instead of creating new ones repeatedly',
    tables: 'Use table.clear() instead of creating new tables',
    strings: 'Use string.format or table.concat for string building'
  },

  // Common errors and solutions
  errors: {
    'attempt to index nil': 'Check if object exists before accessing properties',
    'ServerStorage is not available on client': 'Use ReplicatedStorage for client-server communication',
    'HTTP 403 (Forbidden)': 'Enable HttpService in game settings',
    'DataStore request was throttled': 'Implement proper error handling with pcall',
    'RemoteEvent is not a valid member': 'Use WaitForChild() to wait for objects to load',
    'Players.LocalPlayer is nil': 'Script is running on server, use LocalScript for client-side code',
    'Workspace.CurrentCamera is nil': 'Camera not loaded yet, wait for it or use LocalScript',
    'Character is nil': 'Player character not spawned yet, use CharacterAdded event'
  },

  // Exploit detection and bypasses
  exploitDetection: {
    common: [
      'Humanoid.WalkSpeed checks',
      'Humanoid.JumpPower/JumpHeight monitoring',
      'Position change rate limiting',
      'RemoteEvent spam detection',
      'Unusual player behavior patterns'
    ],
    bypasses: [
      'Gradual speed increases instead of instant',
      'Randomized timing for actions',
      'Mimicking human-like movement patterns',
      'Using legitimate game mechanics when possible'
    ]
  },

  // Executor-specific functions (common across exploits)
  executorFunctions: {
    identification: ['identifyexecutor', 'getexecutorname'],
    filesystem: ['readfile', 'writefile', 'makefolder', 'delfolder', 'listfiles'],
    http: ['request', 'syn_request', 'http_request'],
    misc: ['setclipboard', 'getclipboard', 'setfpscap', 'getfpscap'],
    drawing: ['Drawing.new', 'Drawing API for visuals'],
    hooking: ['hookfunction', 'hookmetamethod', 'getnamecallmethod']
  },

  // Luau-specific features
  luau: {
    typeAnnotations: 'local name: string = "Hello"',
    generics: 'function map<T, U>(list: {T}, fn: (T) -> U): {U}',
    unionTypes: 'local value: string | number = "hello"',
    stringInterpolation: 'local message = `Hello {name}!`',
    compoundAssignment: 'value += 1, value ..= "text"',
    continueStatement: 'Use continue in loops to skip iterations',
    tableFreeze: 'table.freeze() makes tables immutable',
    tableClone: 'table.clone() creates shallow copies'
  },

  // Advanced Roblox concepts
  advanced: {
    metatables: `local mt = {
  __index = function(t, k)
    return "Default value for " .. k
  end
}
setmetatable(myTable, mt)`,

    coroutines: `local function myCoroutine()
  for i = 1, 5 do
    print("Coroutine step:", i)
    coroutine.yield()
  end
end

local co = coroutine.create(myCoroutine)
coroutine.resume(co)`,

    oop: `local MyClass = {}
MyClass.__index = MyClass

function MyClass.new(name)
  local self = setmetatable({}, MyClass)
  self.name = name
  return self
end

function MyClass:greet()
  print("Hello, " .. self.name)
end`,

    modules: `-- ModuleScript
local MyModule = {}

function MyModule.doSomething()
  return "Hello from module!"
end

return MyModule

-- Using the module
local MyModule = require(script.MyModule)
print(MyModule.doSomething())`
  },

  // Common Roblox APIs and their usage
  apis: {
    cframe: {
      creation: 'CFrame.new(x, y, z) or CFrame.new(position, lookVector)',
      rotation: 'CFrame.Angles(x, y, z) or CFrame.fromEulerAnglesXYZ(x, y, z)',
      multiplication: 'cf1 * cf2 combines transformations',
      methods: ['ToWorldSpace', 'ToObjectSpace', 'Inverse', 'LookVector', 'RightVector', 'UpVector']
    },

    vector3: {
      creation: 'Vector3.new(x, y, z)',
      operations: 'Addition (+), Subtraction (-), Multiplication (*), Division (/)',
      methods: ['Magnitude', 'Unit', 'Cross', 'Dot', 'Lerp'],
      constants: ['Vector3.zero', 'Vector3.one', 'Vector3.xAxis', 'Vector3.yAxis', 'Vector3.zAxis']
    },

    udim2: {
      creation: 'UDim2.new(scaleX, offsetX, scaleY, offsetY)',
      shortcuts: 'UDim2.fromScale(x, y) or UDim2.fromOffset(x, y)',
      interpolation: 'UDim2:Lerp(goal, alpha)'
    }
  }
};

export default RobloxKnowledge;
