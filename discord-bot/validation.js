/**
 * Validation utilities for PROJECT MADARA Discord bot
 */
export class ValidationUtils {
  /**
   * Validate Discord snowflake ID
   */
  static isValidSnowflake(id) {
    if (!id || typeof id !== 'string') return false;
    return /^\d{17,19}$/.test(id);
  }

  /**
   * Validate message content
   */
  static validateMessageContent(content) {
    const errors = [];

    if (!content || typeof content !== 'string') {
      errors.push('Content must be a non-empty string');
      return { isValid: false, errors };
    }

    if (content.length === 0) {
      errors.push('Content cannot be empty');
    }

    if (content.length > 4000) {
      errors.push('Content exceeds maximum length of 4000 characters');
    }

    // Check for potentially harmful content
    const harmfulPatterns = [
      /discord\.gg\/[a-zA-Z0-9]+/gi, // Discord invites (log but don't block)
      /@everyone|@here/gi, // Mass mentions
      /```[\s\S]*```/g // Code blocks (check for malicious code)
    ];

    const warnings = [];
    harmfulPatterns.forEach((pattern, index) => {
      if (pattern.test(content)) {
        switch (index) {
          case 0:
            warnings.push('Contains Discord invite link');
            break;
          case 1:
            warnings.push('Contains mass mention');
            break;
          case 2:
            warnings.push('Contains code block');
            break;
        }
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate AI prompt
   */
  static validateAIPrompt(prompt) {
    const errors = [];

    if (!prompt || typeof prompt !== 'string') {
      errors.push('Prompt must be a non-empty string');
      return { isValid: false, errors };
    }

    const trimmedPrompt = prompt.trim();

    if (trimmedPrompt.length === 0) {
      errors.push('Prompt cannot be empty');
    }

    if (trimmedPrompt.length < 3) {
      errors.push('Prompt must be at least 3 characters long');
    }

    if (trimmedPrompt.length > 4000) {
      errors.push('Prompt exceeds maximum length of 4000 characters');
    }

    // Check for spam patterns
    const spamPatterns = [
      /(.)\1{10,}/g, // Repeated characters
      /^(.{1,10})\1{5,}$/g, // Repeated phrases
      /[!?]{5,}/g // Excessive punctuation
    ];

    const warnings = [];
    spamPatterns.forEach((pattern, index) => {
      if (pattern.test(trimmedPrompt)) {
        switch (index) {
          case 0:
            warnings.push('Contains excessive repeated characters');
            break;
          case 1:
            warnings.push('Contains repeated phrases');
            break;
          case 2:
            warnings.push('Contains excessive punctuation');
            break;
        }
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      cleanPrompt: trimmedPrompt
    };
  }

  /**
   * Validate environment configuration
   */
  static validateEnvironment() {
    const required = [
      'DISCORD_BOT_TOKEN',
      'GROQ_API_KEY',
      'CLIENT_ID'
    ];

    const missing = [];
    const invalid = [];

    required.forEach(key => {
      const value = process.env[key];
      
      if (!value) {
        missing.push(key);
        return;
      }

      // Validate specific formats
      switch (key) {
        case 'DISCORD_BOT_TOKEN':
          if (!this.isValidDiscordToken(value)) {
            invalid.push(`${key}: Invalid Discord bot token format`);
          }
          break;
        case 'CLIENT_ID':
          if (!this.isValidSnowflake(value)) {
            invalid.push(`${key}: Invalid Discord client ID format`);
          }
          break;
        case 'GROQ_API_KEY':
          if (!this.isValidGroqApiKey(value)) {
            invalid.push(`${key}: Invalid Groq API key format`);
          }
          break;
      }
    });

    return {
      isValid: missing.length === 0 && invalid.length === 0,
      missing,
      invalid
    };
  }

  /**
   * Validate Discord bot token format
   */
  static isValidDiscordToken(token) {
    // Discord bot tokens have a specific format
    return /^[A-Za-z0-9_-]{24}\.[A-Za-z0-9_-]{6}\.[A-Za-z0-9_-]{27}$/.test(token);
  }

  /**
   * Validate Groq API key format
   */
  static isValidGroqApiKey(key) {
    // Groq API keys start with 'gsk_'
    return /^gsk_[A-Za-z0-9]{48}$/.test(key);
  }

  /**
   * Sanitize user input
   */
  static sanitizeInput(input) {
    if (typeof input !== 'string') return '';
    
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML/XML tags
      .replace(/[@#]/g, '') // Remove Discord mention prefixes
      .substring(0, 4000); // Limit length
  }

  /**
   * Validate command arguments
   */
  static validateCommandArgs(args, expectedTypes = []) {
    const errors = [];

    if (!Array.isArray(args)) {
      errors.push('Arguments must be an array');
      return { isValid: false, errors };
    }

    if (args.length < expectedTypes.length) {
      errors.push(`Expected ${expectedTypes.length} arguments, got ${args.length}`);
    }

    expectedTypes.forEach((type, index) => {
      const arg = args[index];
      
      switch (type) {
        case 'string':
          if (typeof arg !== 'string' || arg.length === 0) {
            errors.push(`Argument ${index + 1} must be a non-empty string`);
          }
          break;
        case 'number':
          if (isNaN(Number(arg))) {
            errors.push(`Argument ${index + 1} must be a number`);
          }
          break;
        case 'snowflake':
          if (!this.isValidSnowflake(arg)) {
            errors.push(`Argument ${index + 1} must be a valid Discord ID`);
          }
          break;
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export default ValidationUtils;
