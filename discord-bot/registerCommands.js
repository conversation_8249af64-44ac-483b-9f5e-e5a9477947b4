import { REST, Routes } from 'discord.js';
import { config, validateConfig } from './config.js';
import { MadaraSlashCommandBuilder } from './slashCommandBuilder.js';
import logger from './logger.js';

/**
 * Register slash commands with Discord
 */
class CommandRegistrar {
  constructor() {
    this.rest = new REST({ version: '10' }).setToken(config.discord.token);
    this.clientId = config.discord.clientId;
  }

  /**
   * Register commands globally
   */
  async registerGlobalCommands() {
    try {
      logger.info('Starting global slash command registration...');

      const commands = MadaraSlashCommandBuilder.getCommandData();
      
      logger.info(`Registering ${commands.length} global slash commands`);

      const data = await this.rest.put(
        Routes.applicationCommands(this.clientId),
        { body: commands }
      );

      logger.info(`Successfully registered ${data.length} global slash commands`);
      return data;

    } catch (error) {
      logger.error('Failed to register global slash commands:', error);
      throw error;
    }
  }

  /**
   * Register commands for a specific guild (faster for testing)
   */
  async registerGuildCommands(guildId) {
    try {
      logger.info(`Starting guild slash command registration for guild: ${guildId}`);

      const commands = MadaraSlashCommandBuilder.getAllCommandData();
      
      logger.info(`Registering ${commands.length} guild slash commands`);

      const data = await this.rest.put(
        Routes.applicationGuildCommands(this.clientId, guildId),
        { body: commands }
      );

      logger.info(`Successfully registered ${data.length} guild slash commands`);
      return data;

    } catch (error) {
      logger.error('Failed to register guild slash commands:', error);
      throw error;
    }
  }

  /**
   * Register commands for allowed guilds only
   */
  async registerForAllowedGuilds() {
    const allowedGuilds = config.security.allowedGuilds;
    
    if (!allowedGuilds || allowedGuilds.length === 0) {
      logger.warn('No allowed guilds configured, registering globally');
      return await this.registerGlobalCommands();
    }

    const results = [];
    
    for (const guildId of allowedGuilds) {
      try {
        const result = await this.registerGuildCommands(guildId);
        results.push({ guildId, success: true, commandCount: result.length });
      } catch (error) {
        logger.error(`Failed to register commands for guild ${guildId}:`, error);
        results.push({ guildId, success: false, error: error.message });
      }
    }

    return results;
  }

  /**
   * Clear all commands (useful for cleanup)
   */
  async clearGlobalCommands() {
    try {
      logger.info('Clearing all global slash commands...');

      const data = await this.rest.put(
        Routes.applicationCommands(this.clientId),
        { body: [] }
      );

      logger.info('Successfully cleared all global slash commands');
      return data;

    } catch (error) {
      logger.error('Failed to clear global slash commands:', error);
      throw error;
    }
  }

  /**
   * Clear commands for a specific guild
   */
  async clearGuildCommands(guildId) {
    try {
      logger.info(`Clearing guild slash commands for guild: ${guildId}`);

      const data = await this.rest.put(
        Routes.applicationGuildCommands(this.clientId, guildId),
        { body: [] }
      );

      logger.info(`Successfully cleared guild slash commands for guild: ${guildId}`);
      return data;

    } catch (error) {
      logger.error(`Failed to clear guild slash commands for guild ${guildId}:`, error);
      throw error;
    }
  }

  /**
   * List all registered commands
   */
  async listGlobalCommands() {
    try {
      const commands = await this.rest.get(
        Routes.applicationCommands(this.clientId)
      );

      logger.info(`Found ${commands.length} registered global commands`);
      return commands;

    } catch (error) {
      logger.error('Failed to list global commands:', error);
      throw error;
    }
  }

  /**
   * List commands for a specific guild
   */
  async listGuildCommands(guildId) {
    try {
      const commands = await this.rest.get(
        Routes.applicationGuildCommands(this.clientId, guildId)
      );

      logger.info(`Found ${commands.length} registered guild commands for guild: ${guildId}`);
      return commands;

    } catch (error) {
      logger.error(`Failed to list guild commands for guild ${guildId}:`, error);
      throw error;
    }
  }
}

/**
 * Main registration function
 */
async function main() {
  try {
    // Validate configuration
    validateConfig();
    
    const registrar = new CommandRegistrar();
    const args = process.argv.slice(2);
    const command = args[0];
    const guildId = args[1];

    switch (command) {
      case 'global':
        await registrar.registerGlobalCommands();
        break;
        
      case 'guild':
        if (!guildId) {
          console.error('Guild ID required for guild registration');
          process.exit(1);
        }
        await registrar.registerGuildCommands(guildId);
        break;
        
      case 'allowed':
        await registrar.registerForAllowedGuilds();
        break;
        
      case 'clear-global':
        await registrar.clearGlobalCommands();
        break;
        
      case 'clear-guild':
        if (!guildId) {
          console.error('Guild ID required for guild clearing');
          process.exit(1);
        }
        await registrar.clearGuildCommands(guildId);
        break;
        
      case 'list-global':
        const globalCommands = await registrar.listGlobalCommands();
        console.log('Global Commands:', globalCommands.map(cmd => cmd.name));
        break;
        
      case 'list-guild':
        if (!guildId) {
          console.error('Guild ID required for guild listing');
          process.exit(1);
        }
        const guildCommands = await registrar.listGuildCommands(guildId);
        console.log(`Guild Commands for ${guildId}:`, guildCommands.map(cmd => cmd.name));
        break;
        
      default:
        console.log('Usage:');
        console.log('  node registerCommands.js global                    - Register commands globally');
        console.log('  node registerCommands.js guild <guildId>           - Register commands for specific guild');
        console.log('  node registerCommands.js allowed                   - Register commands for allowed guilds');
        console.log('  node registerCommands.js clear-global              - Clear all global commands');
        console.log('  node registerCommands.js clear-guild <guildId>     - Clear commands for specific guild');
        console.log('  node registerCommands.js list-global               - List all global commands');
        console.log('  node registerCommands.js list-guild <guildId>      - List commands for specific guild');
        process.exit(1);
    }

    logger.info('Command registration completed successfully');
    process.exit(0);

  } catch (error) {
    logger.error('Command registration failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { CommandRegistrar };
export default CommandRegistrar;
