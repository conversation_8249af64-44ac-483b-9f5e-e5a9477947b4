import { MadaraEmbedBuilder } from './embedBuilder.js';
import { config } from './config.js';

export const aboutCommand = {
  name: 'about',
  description: 'Learn about PROJECT MADARA',
  aliases: ['info', 'madara'],
  
  async execute(message, args) {
    const embed = MadaraEmbedBuilder.createEmbed({
      title: '🔥 About PROJECT MADARA',
      description: 'PROJECT MADARA is a premium Roblox script hub providing high-quality, secure scripts for the Roblox community.',
      fields: [
        {
          name: '🎯 Our Mission',
          value: 'To provide the most advanced, secure, and reliable Roblox scripts while maintaining the highest standards of quality and user experience.',
          inline: false
        },
        {
          name: '🛡️ Security First',
          value: 'All scripts undergo rigorous testing and security validation. We use advanced obfuscation and anti-tampering protection.',
          inline: false
        },
        {
          name: '🔧 Features',
          value: '• Premium script collection\n• Advanced key system\n• 24/7 support\n• Regular updates\n• Anti-detection technology',
          inline: true
        },
        {
          name: '🤖 AI Assistant',
          value: 'This Discord bot features MADARA AI, powered by advanced language models to help with questions and support.',
          inline: true
        },
        {
          name: '🔗 Quick Links',
          value: '[🌐 Website](https://checkingbefore.netlify.app)\n[🔑 Get Key](https://checkingbefore.netlify.app/get-key)\n[💬 Discord](https://discord.gg/madara)',
          inline: false
        }
      ],
      thumbnail: config.bot.iconUrl,
      color: config.bot.embedColor
    });

    await message.reply({ embeds: [embed] });
  }
};

export default aboutCommand;
