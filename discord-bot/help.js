import { MadaraEmbedBuilder } from './embedBuilder.js';
import { config } from './config.js';

export const helpCommand = {
  name: 'help',
  description: 'Show all available PROJECT MADARA bot commands',
  aliases: ['h', 'commands'],
  
  async execute(message, args) {
    const commands = [
      {
        name: 'help',
        description: 'Show this help message'
      },
      {
        name: 'ping',
        description: 'Check bot latency and status'
      },
      {
        name: 'ask <question>',
        description: 'Ask MADARA AI a question'
      },
      {
        name: 'ai <prompt>',
        description: 'Chat with MADARA AI (alias for ask)'
      },
      {
        name: 'tutorial [topic]',
        description: 'Interactive tutorials and guides'
      },
      {
        name: 'status',
        description: 'Show bot status and statistics'
      },
      {
        name: 'about',
        description: 'Learn about PROJECT MADARA'
      },
      {
        name: 'scripts',
        description: 'Get information about PROJECT MADARA scripts'
      },
      {
        name: 'support',
        description: 'Get PROJECT MADARA support information'
      }
    ];

    const embed = MadaraEmbedBuilder.createHelpEmbed(commands);
    
    // Add additional information
    embed.addFields([
      {
        name: '🤖 AI Features',
        value: '• **Smart FAQ System** - Instant answers to common questions\n• **Code Analysis** - Automatic syntax checking and fixes\n• **Multi-language Support** - Lua, JavaScript, Python, C++, Java\n• **Error Explanation** - Understand what went wrong',
        inline: false
      },
      {
        name: '📚 Learning Resources',
        value: `• Use \`${config.discord.prefix}tutorial\` for step-by-step guides\n• **Mention me** or **reply** to my messages for AI chat\n• Ask specific coding questions for better help\n• Share error messages for detailed explanations`,
        inline: false
      },
      {
        name: '🔗 Quick Links',
        value: `[Website](https://checkingbefore.netlify.app) • [Discord](https://discord.gg/madara) • [Scripts](https://checkingbefore.netlify.app/generate-key)`,
        inline: false
      },
      {
        name: '💡 Pro Tips',
        value: `• I respond to mentions and replies (no prefix needed!)\n• Use tutorials for step-by-step learning\n• AI responses are powered by advanced language models\n• Rate limits apply to prevent spam`,
        inline: false
      }
    ]);

    await message.reply({ embeds: [embed] });
  }
};

export default helpCommand;
