import { config } from './config.js';
import { logAIInteraction, logError } from './logger.js';
import { CodeHelper } from './codeHelper.js';
import { ContentAnalyzer } from './contentAnalyzer.js';
import { FAQMatcher } from './faqMatcher.js';
import { messageContext } from './messageContext.js';
import { RobloxKnowledge } from './robloxKnowledge.js';

/**
 * AI Service for PROJECT MADARA Discord bot with multiple provider support
 */
export class AIService {
  constructor() {
    this.provider = config.ai.provider;
    this.apiKey = config.ai.apiKey;
    this.model = config.ai.model;
    this.maxResponseLength = config.ai.maxResponseLength;
    this.systemPrompt = config.ai.systemPrompt;

    // Set base URL based on provider
    this.baseUrl = this.getBaseUrl();

    // Response cache for common questions
    this.responseCache = new Map();
    this.cacheMaxSize = 100;
    this.cacheExpiry = 30 * 60 * 1000; // 30 minutes

    // Initialize FAQ matcher
    this.faqMatcher = new FAQMatcher();

    // FAQ patterns for intelligent matching (legacy - keeping for compatibility)
    this.faqPatterns = this.initializeFAQPatterns();
  }

  /**
   * Get base URL based on AI provider
   */
  getBaseUrl() {
    switch (this.provider) {
      case 'huggingface':
        return 'https://router.huggingface.co/v1/chat/completions';
      case 'openrouter':
        return 'https://openrouter.ai/api/v1/chat/completions';
      case 'groq':
        return 'https://api.groq.com/openai/v1/chat/completions';
      default:
        return 'https://router.huggingface.co/v1/chat/completions';
    }
  }

  /**
   * Initialize FAQ patterns for intelligent matching
   */
  initializeFAQPatterns() {
    return [
      {
        patterns: ['what is project madara', 'about project madara', 'tell me about madara'],
        response: 'Project Madara is a Roblox script hub created by Sabin07, which supports multiple games and provides quality Roblox scripts.'
      },
      {
        patterns: ['who created', 'who made', 'creator of madara'],
        response: 'Project Madara was created by Sabin07.'
      },
      {
        patterns: ['is it free', 'cost', 'price', 'how much'],
        response: 'Yes, Project Madara is free to use. However, if I feel like it I might add Premium Keys.'
      },
      {
        patterns: ['how to get started', 'getting started', 'how to use'],
        response: 'Getting started is easy! First, ensure you have a compatible script executor. Then, check our script page for the Roblox script that you need for your game.'
      },
      {
        patterns: ['script not working', 'script broken', 'not working'],
        response: 'Common issues include: incompatible executor, outdated script version, or incorrect implementation. Make sure you\'re using a supported executor, the Roblox script is up to date, and you\'ve followed all instructions.'
      }
    ];
  }

  /**
   * Check for FAQ match using enhanced FAQ matcher
   */
  checkFAQMatch(prompt) {
    // Use the new FAQ matcher first
    const faqMatch = this.faqMatcher.findBestMatch(prompt);

    if (faqMatch && faqMatch.confidence >= 0.7) {
      return {
        matched: true,
        response: faqMatch.answer,
        confidence: faqMatch.confidence,
        question: faqMatch.question,
        category: faqMatch.category,
        matchType: faqMatch.matchType,
        enhanced: true
      };
    }

    // Fallback to legacy FAQ patterns
    const lowerPrompt = prompt.toLowerCase();

    for (const faq of this.faqPatterns) {
      for (const pattern of faq.patterns) {
        if (lowerPrompt.includes(pattern)) {
          return {
            matched: true,
            response: faq.response,
            confidence: 0.8,
            enhanced: false
          };
        }
      }
    }

    return { matched: false };
  }

  /**
   * Get cached response if available
   */
  getCachedResponse(cacheKey) {
    const cached = this.responseCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.response;
    }
    return null;
  }

  /**
   * Cache response for future use
   */
  cacheResponse(cacheKey, response) {
    // Clean old entries if cache is full
    if (this.responseCache.size >= this.cacheMaxSize) {
      const oldestKey = this.responseCache.keys().next().value;
      this.responseCache.delete(oldestKey);
    }

    this.responseCache.set(cacheKey, {
      response,
      timestamp: Date.now()
    });
  }

  /**
   * Generate AI response using OpenRouter with enhanced features and message context
   */
  async generateResponse(prompt, userId, context = {}, message = null, contentAnalysis = null) {
    try {
      // Validate input
      if (!prompt || typeof prompt !== 'string') {
        throw new Error('Invalid prompt provided');
      }

      if (prompt.length > 4000) {
        throw new Error('Prompt too long. Please keep your message under 4000 characters.');
      }

      // Check for FAQ match first
      const faqMatch = this.checkFAQMatch(prompt);
      if (faqMatch.matched) {
        return {
          success: true,
          response: faqMatch.response,
          followUpParts: [],
          model: 'FAQ System',
          provider: 'Built-in',
          cached: false,
          faqMatch: true
        };
      }

      // Get message context if available
      let messageContextStr = '';
      if (message) {
        messageContextStr = await messageContext.getMessageContext(message);
      }

      // Create cache key with message context
      const cacheKey = this.createCacheKey(prompt + messageContextStr, context);

      // Check cache first
      const cachedResponse = this.getCachedResponse(cacheKey);
      if (cachedResponse) {
        return {
          ...cachedResponse,
          cached: true
        };
      }

      // Analyze the prompt for code-related content if not provided
      const codeAnalysis = CodeHelper.detectCode(prompt);

      // Use provided content analysis or create a basic one
      if (!contentAnalysis) {
        contentAnalysis = ContentAnalyzer.analyzeContent(message || prompt);
      }

      // Add Roblox knowledge if this is a Roblox/Luau related question
      const robloxContext = this.getRobloxContext(prompt, codeAnalysis);

      // Format user prompt with context, code analysis, content analysis, and message context
      const userPrompt = this.formatUserPrompt(prompt, context, codeAnalysis, messageContextStr, contentAnalysis, robloxContext);

      // Call AI API based on provider
      const response = await this.callAIProvider(userPrompt);

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`API Error: ${response.status} - ${errorData}`);
      }

      const data = await response.json();

      const aiResponse = this.parseAIResponse(data);

      if (!aiResponse) {
        throw new Error('No response generated from AI');
      }

      // Handle long responses
      const responseData = this.handleLongResponse(aiResponse);

      // Log the interaction
      logAIInteraction(userId, prompt, responseData.mainResponse, this.model);

      // Create response object
      const responseObj = {
        success: true,
        response: responseData.mainResponse,
        followUpParts: responseData.followUpParts,
        model: this.model,
        provider: 'OpenRouter',
        usage: data.usage,
        cached: false,
        faqMatch: false
      };

      // Cache the response
      this.cacheResponse(cacheKey, responseObj);

      return responseObj;

    } catch (error) {
      logError(error, {
        userId,
        prompt: prompt?.substring(0, 100),
        service: 'openrouter-ai'
      });

      const errorInfo = this.formatErrorMessage(error);

      return {
        success: false,
        error: errorInfo.message || errorInfo,
        errorType: errorInfo.type || 'unknown',
        retryAfter: errorInfo.retryAfter || 300,
        model: this.model
      };
    }
  }

  /**
   * Create cache key for response caching
   */
  createCacheKey(prompt, context) {
    // Create a simple hash of the prompt and relevant context
    const contextStr = `${context.guildName || ''}_${context.channelName || ''}`;
    const fullKey = `${prompt}_${contextStr}`.toLowerCase().trim();

    // Simple hash function
    let hash = 0;
    for (let i = 0; i < fullKey.length; i++) {
      const char = fullKey.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    return `cache_${Math.abs(hash)}`;
  }

  /**
   * Format user prompt with context, code analysis, content analysis, message context, and Roblox knowledge
   */
  formatUserPrompt(prompt, context, codeAnalysis, messageContextStr = '', contentAnalysis = null, robloxContext = '') {
    let formattedPrompt = prompt;

    // Add content analysis context first
    if (contentAnalysis) {
      const suggestions = ContentAnalyzer.getResponseSuggestions(contentAnalysis);
      formattedPrompt = `CONTENT ANALYSIS:
- Content Type: ${contentAnalysis.type}
- Response Style: ${contentAnalysis.responseStyle}
- Confidence: ${contentAnalysis.confidence.toFixed(2)}
- Suggested Tone: ${suggestions.tone}
- Focus Areas: ${suggestions.focusAreas.join(', ')}

RESPONSE INSTRUCTIONS:
${this.getResponseInstructions(contentAnalysis)}

USER MESSAGE: ${prompt}`;
    }

    // Add Roblox knowledge context if relevant
    if (robloxContext && robloxContext.trim()) {
      formattedPrompt = `${robloxContext}\n\n${formattedPrompt}`;
    }

    // Add message context
    if (messageContextStr && messageContextStr.trim()) {
      formattedPrompt = `${messageContextStr}\n\nCURRENT QUESTION: ${formattedPrompt}`;
    }

    // Add context if available
    if (context.username) {
      formattedPrompt = `User ${context.username} asks: ${formattedPrompt}`;
    }

    if (context.guildName) {
      formattedPrompt += `\n\nContext: This is from the ${context.guildName} Discord server.`;
    }

    // Add enhanced code analysis context
    if (codeAnalysis && codeAnalysis.hasCode) {
      formattedPrompt += `\n\nCODE ANALYSIS:`;
      formattedPrompt += `\n- Contains code: Yes`;
      formattedPrompt += `\n- Detected language: ${codeAnalysis.language}`;

      // Perform syntax validation
      const syntaxValidation = CodeHelper.validateSyntax(
        codeAnalysis.codeBlocks?.[0]?.content || prompt,
        codeAnalysis.language
      );

      if (!syntaxValidation.isValid) {
        formattedPrompt += `\n- Syntax errors found: ${syntaxValidation.errors.length}`;
        formattedPrompt += `\n- PLEASE FIX THESE SYNTAX ERRORS AND EXPLAIN WHAT WAS WRONG:`;
        syntaxValidation.errors.forEach(error => {
          formattedPrompt += `\n  • Line ${error.line}: ${error.message}`;
        });
      }

      if (syntaxValidation.warnings.length > 0) {
        formattedPrompt += `\n- Code warnings: ${syntaxValidation.warnings.length}`;
        formattedPrompt += `\n- PLEASE ADDRESS THESE WARNINGS AND SUGGEST IMPROVEMENTS:`;
        syntaxValidation.warnings.forEach(warning => {
          formattedPrompt += `\n  • Line ${warning.line}: ${warning.message}`;
        });
      }

      if (syntaxValidation.suggestions.length > 0) {
        formattedPrompt += `\n- Improvement suggestions: ${syntaxValidation.suggestions.length}`;
        formattedPrompt += `\n- PLEASE IMPLEMENT THESE SUGGESTIONS FOR BETTER CODE:`;
        syntaxValidation.suggestions.forEach(suggestion => {
          formattedPrompt += `\n  • Line ${suggestion.line}: ${suggestion.message}`;
        });
      }

      if (codeAnalysis.errors && codeAnalysis.errors.length > 0) {
        formattedPrompt += `\n- Runtime errors detected: ${codeAnalysis.errors.map(e => e.type).join(', ')}`;
        formattedPrompt += `\n- PLEASE HELP FIX THESE ERRORS AND EXPLAIN WHAT WAS WRONG`;
      }

      if (codeAnalysis.codeBlocks && codeAnalysis.codeBlocks.length > 0) {
        formattedPrompt += `\n- Code blocks found: ${codeAnalysis.codeBlocks.length}`;
        formattedPrompt += `\n- PLEASE FORMAT THE CODE PROPERLY, ADD HELPFUL COMMENTS, AND APPLY AUTO-FIXES`;
      }
    }

    // Check if it's a Lua learning question
    if (CodeHelper.isLuaLearningQuestion(prompt)) {
      formattedPrompt += `\n\nLUA LEARNING REQUEST: Please provide a clear, beginner-friendly explanation with simple examples.`;
    }

    return formattedPrompt;
  }

  /**
   * Get relevant Roblox knowledge context based on the prompt
   */
  getRobloxContext(prompt, codeAnalysis) {
    const promptLower = prompt.toLowerCase();

    // Check if this is Roblox/Luau related
    const robloxKeywords = [
      'roblox', 'luau', 'lua', 'script', 'localscript', 'serverscript', 'modulescript',
      'workspace', 'players', 'game', 'remoteevent', 'remotefunction', 'datastore',
      'runservice', 'userinputservice', 'tweenservice', 'httpservice', 'part', 'gui',
      'screengui', 'frame', 'textlabel', 'textbutton', 'humanoid', 'character',
      'leaderstats', 'cframe', 'vector3', 'raycast', 'tween', 'heartbeat'
    ];

    const isRobloxRelated = robloxKeywords.some(keyword => promptLower.includes(keyword)) ||
                           (codeAnalysis && codeAnalysis.language === 'lua');

    if (!isRobloxRelated) {
      return '';
    }

    // Build relevant context based on what's mentioned in the prompt
    let context = 'ROBLOX KNOWLEDGE CONTEXT:\n';

    // Add service-specific knowledge
    if (promptLower.includes('players') || promptLower.includes('player')) {
      context += `Players Service: ${JSON.stringify(RobloxKnowledge.services.Players, null, 2)}\n`;
    }

    if (promptLower.includes('workspace') || promptLower.includes('raycast')) {
      context += `Workspace Service: ${JSON.stringify(RobloxKnowledge.services.Workspace, null, 2)}\n`;
    }

    if (promptLower.includes('runservice') || promptLower.includes('heartbeat')) {
      context += `RunService: ${JSON.stringify(RobloxKnowledge.services.RunService, null, 2)}\n`;
    }

    if (promptLower.includes('input') || promptLower.includes('key') || promptLower.includes('mouse')) {
      context += `UserInputService: ${JSON.stringify(RobloxKnowledge.services.UserInputService, null, 2)}\n`;
    }

    if (promptLower.includes('tween') || promptLower.includes('animation')) {
      context += `TweenService: ${JSON.stringify(RobloxKnowledge.services.TweenService, null, 2)}\n`;
    }

    if (promptLower.includes('http') || promptLower.includes('web') || promptLower.includes('api')) {
      context += `HttpService: ${JSON.stringify(RobloxKnowledge.services.HttpService, null, 2)}\n`;
    }

    if (promptLower.includes('datastore') || promptLower.includes('data') || promptLower.includes('save')) {
      context += `DataStoreService: ${JSON.stringify(RobloxKnowledge.services.DataStoreService, null, 2)}\n`;
    }

    // Add relevant code patterns
    if (promptLower.includes('player') && promptLower.includes('join')) {
      context += `\nPlayer Joined Pattern:\n${RobloxKnowledge.patterns.playerJoined}\n`;
    }

    if (promptLower.includes('remote')) {
      context += `\nRemoteEvent Pattern:\n${RobloxKnowledge.patterns.remoteEvent}\n`;
    }

    if (promptLower.includes('datastore') || promptLower.includes('save')) {
      context += `\nDataStore Pattern:\n${RobloxKnowledge.patterns.dataStore}\n`;
    }

    if (promptLower.includes('raycast')) {
      context += `\nRaycast Pattern:\n${RobloxKnowledge.patterns.raycast}\n`;
    }

    if (promptLower.includes('tween')) {
      context += `\nTween Pattern:\n${RobloxKnowledge.patterns.tween}\n`;
    }

    // Add security notes if relevant
    if (promptLower.includes('remote') || promptLower.includes('exploit') || promptLower.includes('security')) {
      context += `\nSecurity Best Practices:\n${JSON.stringify(RobloxKnowledge.security, null, 2)}\n`;
    }

    // Add performance tips if relevant
    if (promptLower.includes('lag') || promptLower.includes('performance') || promptLower.includes('optimize')) {
      context += `\nPerformance Tips:\n${JSON.stringify(RobloxKnowledge.performance, null, 2)}\n`;
    }

    // Add Luau-specific features if relevant
    if (promptLower.includes('luau') || promptLower.includes('type') || promptLower.includes('annotation')) {
      context += `\nLuau Features:\n${JSON.stringify(RobloxKnowledge.luau, null, 2)}\n`;
    }

    return context;
  }

  /**
   * Call AI provider based on configuration
   */
  async callAIProvider(userPrompt) {
    switch (this.provider) {
      case 'huggingface':
        return await this.callHuggingFace(userPrompt);
      case 'openrouter':
        return await this.callOpenRouter(userPrompt);
      case 'groq':
        return await this.callGroq(userPrompt);
      default:
        return await this.callHuggingFace(userPrompt);
    }
  }

  /**
   * Call Hugging Face API (now uses OpenAI-compatible format)
   */
  async callHuggingFace(userPrompt) {
    const messages = [
      {
        role: 'system',
        content: this.systemPrompt
      },
      {
        role: 'user',
        content: userPrompt
      }
    ];

    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.model,
        messages: messages,
        max_tokens: 300,
        temperature: 0.7,
        stream: false
      })
    });

    return response;
  }

  /**
   * Call OpenRouter API
   */
  async callOpenRouter(userPrompt) {
    const messages = [
      {
        role: 'system',
        content: this.systemPrompt
      },
      {
        role: 'user',
        content: userPrompt
      }
    ];

    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.model,
        messages: messages,
        max_tokens: 300,
        temperature: 0.7,
        top_p: 0.9,
        stream: false
      })
    });

    return response;
  }

  /**
   * Call Groq API
   */
  async callGroq(userPrompt) {
    const messages = [
      {
        role: 'system',
        content: this.systemPrompt
      },
      {
        role: 'user',
        content: userPrompt
      }
    ];

    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.model,
        messages: messages,
        max_tokens: 300,
        temperature: 0.7,
        stream: false
      })
    });

    return response;
  }

  /**
   * Parse AI response (all providers now use OpenAI-compatible format)
   */
  parseAIResponse(data) {
    // All providers now use OpenAI-compatible format
    return data.choices?.[0]?.message?.content || '';
  }

  /**
   * Get specific response instructions based on content analysis
   */
  getResponseInstructions(contentAnalysis) {
    switch (contentAnalysis.type) {
      case 'media':
        return `This user shared a ${contentAnalysis.context.mediaType || 'media file'}. Respond positively with 1-2 emojis. Be enthusiastic but not overwhelming. Example: "That's adorable! 😸 Thanks for sharing!"`;

      case 'casual':
        return `This is casual conversation. Be friendly and conversational with 1-2 emojis. Match their energy but keep it natural and not overwhelming.`;

      case 'code':
        return `This is a coding question. Be helpful and encouraging. You can use 1 emoji if it fits naturally, but focus on being informative and supportive.`;

      case 'question':
        return `This is a question. Be informative and helpful. Use a friendly tone with minimal emojis (0-1). Make them feel comfortable asking questions.`;

      case 'greeting':
        return `This is a greeting. Respond warmly with 1-2 emojis maximum. Be welcoming but not overwhelming. Example: "Hey there! 😊 How's your day going?"`;

      case 'emotional':
        return `This message has emotional content. Respond with empathy and understanding. Use 0-1 supportive emoji if appropriate. Be caring and helpful.`;

      default:
        return `Respond in a friendly way with 1-2 emojis maximum. Be conversational and helpful without being overwhelming.`;
    }
  }

  /**
   * Truncate response to fit Discord limits and handle long responses
   */
  truncateResponse(response) {
    // Discord limit is 2000 characters, but we want some buffer
    const discordLimit = 1900;

    if (response.length <= discordLimit) {
      return response;
    }

    // For very long responses, split into multiple parts
    const parts = this.splitLongResponse(response, discordLimit);

    if (parts.length > 1) {
      // Return first part with continuation indicator
      return parts[0] + '\n\n*[Continued in next message...]*';
    }

    // Single part truncation
    const truncated = response.substring(0, discordLimit - 100);
    const lastSentence = truncated.lastIndexOf('.');
    const lastNewline = truncated.lastIndexOf('\n');
    const lastSpace = truncated.lastIndexOf(' ');

    const cutPoint = Math.max(lastSentence, lastNewline, lastSpace);

    if (cutPoint > discordLimit * 0.7) {
      return truncated.substring(0, cutPoint + 1) + '\n\n*[Response truncated for Discord limits]*';
    }

    return truncated + '...\n\n*[Response truncated for Discord limits]*';
  }

  /**
   * Split long response into multiple parts
   */
  splitLongResponse(response, maxLength) {
    const parts = [];
    let currentPart = '';
    const sentences = response.split(/(?<=[.!?])\s+/);

    for (const sentence of sentences) {
      if ((currentPart + sentence).length > maxLength - 50) {
        if (currentPart) {
          parts.push(currentPart.trim());
          currentPart = sentence;
        } else {
          // Single sentence is too long, force split
          parts.push(sentence.substring(0, maxLength - 50) + '...');
          currentPart = '';
        }
      } else {
        currentPart += (currentPart ? ' ' : '') + sentence;
      }
    }

    if (currentPart) {
      parts.push(currentPart.trim());
    }

    return parts;
  }

  /**
   * Handle long responses by splitting them appropriately
   */
  handleLongResponse(response) {
    const discordLimit = 1900;

    if (response.length <= discordLimit) {
      return {
        mainResponse: response,
        followUpParts: []
      };
    }

    const parts = this.splitLongResponse(response, discordLimit);

    if (parts.length <= 1) {
      return {
        mainResponse: this.truncateResponse(response),
        followUpParts: []
      };
    }

    // Return first part as main response, rest as follow-ups
    const mainResponse = parts[0] + '\n\n*[Continued in next message...]*';
    const followUpParts = parts.slice(1).map((part, index) => {
      const isLast = index === parts.length - 2;
      return part + (isLast ? '' : '\n\n*[Continued...]*');
    });

    return {
      mainResponse,
      followUpParts
    };
  }

  /**
   * Format error message for users with friendly, helpful messages
   */
  formatErrorMessage(error) {
    const errorMsg = error.message.toLowerCase();

    // Rate limiting errors
    if (errorMsg.includes('rate limit') || errorMsg.includes('quota') || errorMsg.includes('429')) {
      return {
        message: "I'm getting a lot of requests right now! 😅 Give me a moment to catch my breath and try again in a few minutes! ⏰",
        type: 'rate_limit',
        retryAfter: 300 // 5 minutes
      };
    }

    // Credit/payment errors
    if (errorMsg.includes('credit') || errorMsg.includes('402') || errorMsg.includes('payment')) {
      return {
        message: "Looks like I'm running low on AI credits! 💸 Don't worry, Sabin will top me up soon! Try again in a bit! 🔄",
        type: 'credits',
        retryAfter: 3600 // 1 hour
      };
    }

    // Authentication errors
    if (errorMsg.includes('api key') || errorMsg.includes('401') || errorMsg.includes('unauthorized')) {
      return {
        message: "There's a configuration issue that needs to be resolved. The administrator has been notified and will fix this shortly.",
        type: 'auth',
        retryAfter: 1800 // 30 minutes
      };
    }

    // Model not found errors
    if (errorMsg.includes('model') && (errorMsg.includes('not found') || errorMsg.includes('404'))) {
      return {
        message: "The AI service is currently unavailable due to a configuration issue. This will be resolved soon.",
        type: 'model',
        retryAfter: 1800 // 30 minutes
      };
    }

    // Timeout errors
    if (errorMsg.includes('timeout') || errorMsg.includes('timed out')) {
      return {
        message: "I'm thinking a bit slowly today! 🐌 Try asking me again with a shorter message! 💭",
        type: 'timeout',
        retryAfter: 60 // 1 minute
      };
    }

    // Content filtering
    if (errorMsg.includes('content') && errorMsg.includes('filter')) {
      return {
        message: "Let's keep our conversation friendly and appropriate! 😊 Try rephrasing your message! 💬",
        type: 'content',
        retryAfter: 0
      };
    }

    // Network errors
    if (errorMsg.includes('network') || errorMsg.includes('connection') || errorMsg.includes('fetch')) {
      return {
        message: "Unable to connect to the AI service. Please check your internet connection and try again.",
        type: 'network',
        retryAfter: 120 // 2 minutes
      };
    }

    // Generic error with friendly tone
    return {
      message: "An unexpected error occurred. Please try again in a few moments.",
      type: 'unknown',
      retryAfter: 300 // 5 minutes
    };
  }

  /**
   * Check if AI service is available
   */
  async healthCheck() {
    try {
      // All providers now use OpenAI-compatible format
      const messages = [
        {
          role: 'system',
          content: 'You are a test assistant.'
        },
        {
          role: 'user',
          content: 'Say "OK" if you can hear me.'
        }
      ];

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: this.model,
          messages: messages,
          max_tokens: 10,
          temperature: 0
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Health check failed: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      const testResponse = this.parseAIResponse(data);

      return {
        available: true,
        model: this.model,
        provider: this.provider,
        response: testResponse
      };

    } catch (error) {
      logError(error, { service: `${this.provider}-ai-health-check` });

      return {
        available: false,
        error: error.message
      };
    }
  }

  /**
   * Get AI service stats
   */
  getStats() {
    return {
      model: this.model,
      maxResponseLength: this.maxResponseLength,
      provider: 'OpenRouter',
      systemPromptLength: this.systemPrompt.length
    };
  }
}

// Create singleton instance
export const aiService = new AIService();
export default aiService;
