import { aiService } from './aiService.js';
import { MadaraEmbedBuilder } from './embedBuilder.js';
import { SecurityManager } from './security.js';
import { logDiscordEvent } from './logger.js';
import { CodeHelper } from './codeHelper.js';
import { ContentAnalyzer } from './contentAnalyzer.js';
import { messageContext } from './messageContext.js';

export const askCommand = {
  name: 'ask',
  description: 'Ask MADARA AI a question',
  aliases: ['ai', 'chat', 'question'],
  
  async execute(message, args, client) {
    // Check if channel is allowed for AI interactions
    if (!SecurityManager.isChannelAllowed(message.channel.id)) {
      const embed = MadaraEmbedBuilder.createUnauthorizedEmbed('channel');
      return await message.reply({ embeds: [embed] });
    }

    // Check rate limiting
    const rateLimitCheck = SecurityManager.checkRateLimit(message.author.id);
    if (!rateLimitCheck.allowed) {
      const embed = MadaraEmbedBuilder.createRateLimitEmbed(rateLimitCheck.resetIn);
      return await message.reply({ embeds: [embed] });
    }

    // Validate prompt
    if (!args || args.length === 0) {
      const embed = MadaraEmbedBuilder.createErrorEmbed(
        'Missing Question',
        `Please provide a question to ask MADARA AI.\n\nExample: \`!madara ask What is PROJECT MADARA?\``
      );
      return await message.reply({ embeds: [embed] });
    }

    const prompt = args.join(' ');

    // Validate message content
    const contentValidation = SecurityManager.validateMessageContent(prompt);
    if (!contentValidation.isValid && contentValidation.hasInappropriateContent) {
      const embed = MadaraEmbedBuilder.createErrorEmbed(
        'Inappropriate Content',
        'Please keep our community friendly and appropriate for all ages. Let\'s focus on programming and helpful discussions!'
      );
      return await message.reply({ embeds: [embed] });
    }

    if (contentValidation.issues.length > 0) {
      logDiscordEvent('ai_request_with_issues', {
        userId: message.author.id,
        issues: contentValidation.issues
      });
    }

    // Analyze the content for appropriate response type
    const contentAnalysis = ContentAnalyzer.analyzeContent(message);
    const codeAnalysis = CodeHelper.detectCode(prompt);

    // Show typing indicator
    await message.channel.sendTyping();

    try {
      // Prepare context for AI
      const context = {
        username: message.author.username,
        guildName: message.guild?.name,
        channelName: message.channel.name
      };

      // Get AI response with message context and content analysis
      const aiResponse = await aiService.generateResponse(prompt, message.author.id, context, message, contentAnalysis);

      if (!aiResponse.success) {
        // Create friendly error embed with retry information
        const embed = MadaraEmbedBuilder.createErrorEmbed(
          '🤖 Oops! Something went wrong!',
          aiResponse.error,
          {
            errorType: aiResponse.errorType,
            retryAfter: aiResponse.retryAfter,
            showRetryTime: true
          }
        );
        return await message.reply({ embeds: [embed] });
      }

      // Create appropriate response embed
      let embed;
      if (aiResponse.faqMatch) {
        // Use FAQ embed for FAQ matches
        embed = MadaraEmbedBuilder.createFAQEmbed(prompt, {
          response: aiResponse.response,
          question: 'FAQ Response',
          category: 'general',
          confidence: 0.9,
          matchType: 'faq'
        });
      } else {
        // Use AI embed for AI-generated responses
        embed = MadaraEmbedBuilder.createAIEmbed(
          prompt,
          aiResponse.response,
          aiResponse.model,
          codeAnalysis,
          null,
          aiResponse.suggestions || [],
          contentAnalysis.type // Pass content type for context-aware styling
        );
      }

      // Add rate limit info to footer if user is close to limit
      if (rateLimitCheck.remaining <= 5) {
        embed.addFields([{
          name: '⚠️ Rate Limit Warning',
          value: `You have ${rateLimitCheck.remaining} requests remaining.`,
          inline: false
        }]);
      }

      const mainReply = await message.reply({ embeds: [embed] });

      // Add bot response to message context
      messageContext.addBotResponse(message, mainReply, aiResponse);

      // Send follow-up messages if response was split
      if (aiResponse.followUpParts && aiResponse.followUpParts.length > 0) {
        for (let i = 0; i < aiResponse.followUpParts.length; i++) {
          // Add small delay between messages
          await new Promise(resolve => setTimeout(resolve, 1000));

          const followUpEmbed = MadaraEmbedBuilder.createEmbed({
            title: `🤖 MADARA AI Response (Part ${i + 2})`,
            description: aiResponse.followUpParts[i],
            color: '#FF6B35'
          });

          await message.channel.send({ embeds: [followUpEmbed] });
        }
      }

      // Log successful interaction
      logDiscordEvent('ai_request_success', {
        userId: message.author.id,
        username: message.author.username,
        guildId: message.guild?.id,
        channelId: message.channel.id,
        promptLength: prompt.length,
        responseLength: aiResponse.response.length,
        model: aiResponse.model
      });

    } catch (error) {
      const embed = MadaraEmbedBuilder.createErrorEmbed(
        'Unexpected Error',
        'An unexpected error occurred while processing your request. Please try again later.'
      );
      
      await message.reply({ embeds: [embed] });
      
      logDiscordEvent('ai_request_error', {
        userId: message.author.id,
        error: error.message,
        prompt: prompt.substring(0, 100)
      });
    }
  }
};

export default askCommand;
