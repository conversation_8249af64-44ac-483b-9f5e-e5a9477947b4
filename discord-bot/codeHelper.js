/**
 * Code Helper utilities for PROJECT MADARA Discord bot
 */
export class CodeHelper {
  /**
   * Detect if message contains code
   */
  static detectCode(message) {
    // First check if this is likely non-code content
    if (this.isNonCodeContent(message)) {
      return { hasCode: false, reason: 'non_code_content' };
    }

    const codePatterns = [
      /```[\s\S]*```/g, // Code blocks
      /`[^`]+`/g, // Inline code
      /function\s+\w+\s*\(/g, // Function definitions
      /local\s+\w+\s*=/g, // Lua local variables
      /if\s+.*\s+then/g, // Lua if statements
      /for\s+.*\s+do/g, // Lua for loops
      /while\s+.*\s+do/g, // Lua while loops
      /print\s*\(/g, // Print statements
      /return\s+/g, // Return statements
      /end\s*$/gm, // Lua end statements
      // More specific method call pattern to avoid URLs
      /(?:^|\s)(\w+)\.(\w+)\s*\(/g, // Method calls (more specific)
      /--\s*.*/g, // Lua comments
      /\/\/\s*.*/g, // Other language comments
      /\/\*[\s\S]*?\*\//g, // Block comments
      /#\s*.*/g, // Python comments
      /def\s+\w+\s*\(/g, // Python functions
      /class\s+\w+/g, // Class definitions
      /import\s+\w+/g, // Import statements
      /from\s+\w+\s+import/g, // Python imports
      /console\.log\s*\(/g, // JavaScript console.log
      /document\.\w+/g, // JavaScript DOM
      /\$\(\s*["'].*["']\s*\)/g, // jQuery
      /public\s+static\s+void\s+main/g, // Java main
      /System\.out\.print/g, // Java print
      /std::/g, // C++ std namespace
      /cout\s*<</g, // C++ cout
      /cin\s*>>/g, // C++ cin
      /printf\s*\(/g, // C printf
      /scanf\s*\(/g, // C scanf
    ];

    // Count pattern matches to determine confidence
    let matchCount = 0;
    let totalMatches = 0;

    for (const pattern of codePatterns) {
      const matches = message.match(pattern);
      if (matches) {
        matchCount++;
        totalMatches += matches.length;
      }
    }

    // Require multiple indicators for code detection
    const hasCode = matchCount >= 2 || totalMatches >= 3 || this.hasExplicitCodeMarkers(message);

    if (hasCode) {
      return {
        hasCode: true,
        language: this.detectLanguage(message),
        codeBlocks: this.extractCodeBlocks(message),
        errors: this.detectErrors(message),
        confidence: Math.min(matchCount / 3, 1.0)
      };
    }

    return { hasCode: false, matchCount, totalMatches };
  }

  /**
   * Check if content is clearly non-code (URLs, images, casual messages)
   */
  static isNonCodeContent(message) {
    const nonCodePatterns = [
      // URLs and links
      /https?:\/\/[^\s]+/gi,
      /www\.[^\s]+/gi,
      // Image/GIF URLs
      /\.(gif|jpg|jpeg|png|webp|mp4|webm)\b/gi,
      // Discord CDN links
      /cdn\.discordapp\.com/gi,
      /media\.discordapp\.net/gi,
      // Common media domains
      /tenor\.com/gi,
      /giphy\.com/gi,
      /imgur\.com/gi,
      // Social expressions
      /^(lol|lmao|haha|xd|:D|:\)|:\(|<3)+$/gi,
      // Single emoji or emoji sequences
      /^[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F1E0}-\u{1F1FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]+$/u,
    ];

    return nonCodePatterns.some(pattern => pattern.test(message));
  }

  /**
   * Check for explicit code markers
   */
  static hasExplicitCodeMarkers(message) {
    const explicitMarkers = [
      /```/g, // Code blocks
      /`[^`]+`/g, // Inline code
      /^\s*\/\//gm, // Line comments at start
      /^\s*--/gm, // Lua comments at start
      /^\s*#/gm, // Python comments at start
    ];

    return explicitMarkers.some(pattern => pattern.test(message));
  }

  /**
   * Detect programming language
   */
  static detectLanguage(message) {
    const languagePatterns = {
      lua: [
        /local\s+\w+/g,
        /function\s+\w+\s*\(/g,
        /if\s+.*\s+then/g,
        /for\s+.*\s+do/g,
        /while\s+.*\s+do/g,
        /end\s*$/gm,
        /--\s*.*/g,
        /game\.\w+/g,
        /workspace\./g,
        /Players\./g,
        /script\./g
      ],
      javascript: [
        /console\.log/g,
        /document\./g,
        /window\./g,
        /function\s*\(/g,
        /=>\s*{/g,
        /const\s+\w+/g,
        /let\s+\w+/g,
        /var\s+\w+/g,
        /\/\/\s*.*/g
      ],
      python: [
        /def\s+\w+\s*\(/g,
        /import\s+\w+/g,
        /from\s+\w+\s+import/g,
        /print\s*\(/g,
        /if\s+.*:/g,
        /for\s+.*:/g,
        /while\s+.*:/g,
        /#\s*.*/g,
        /class\s+\w+/g
      ],
      java: [
        /public\s+static\s+void\s+main/g,
        /System\.out\.print/g,
        /public\s+class/g,
        /private\s+\w+/g,
        /import\s+java\./g,
        /\/\/\s*.*/g,
        /\/\*[\s\S]*?\*\//g
      ],
      cpp: [
        /#include\s*</g,
        /std::/g,
        /cout\s*<</g,
        /cin\s*>>/g,
        /int\s+main\s*\(/g,
        /using\s+namespace\s+std/g,
        /\/\/\s*.*/g,
        /\/\*[\s\S]*?\*\//g
      ],
      c: [
        /#include\s*</g,
        /printf\s*\(/g,
        /scanf\s*\(/g,
        /int\s+main\s*\(/g,
        /\/\/\s*.*/g,
        /\/\*[\s\S]*?\*\//g
      ]
    };

    for (const [language, patterns] of Object.entries(languagePatterns)) {
      const matches = patterns.reduce((count, pattern) => {
        return count + (message.match(pattern) || []).length;
      }, 0);
      
      if (matches > 0) {
        return language;
      }
    }

    return 'unknown';
  }

  /**
   * Extract code blocks from message
   */
  static extractCodeBlocks(message) {
    const codeBlocks = [];
    
    // Extract fenced code blocks
    const fencedBlocks = message.match(/```[\s\S]*?```/g);
    if (fencedBlocks) {
      fencedBlocks.forEach(block => {
        const content = block.replace(/```\w*\n?/g, '').replace(/```$/g, '');
        codeBlocks.push({
          type: 'fenced',
          content: content.trim(),
          language: this.detectLanguage(content)
        });
      });
    }

    // Extract inline code
    const inlineCode = message.match(/`[^`]+`/g);
    if (inlineCode) {
      inlineCode.forEach(code => {
        const content = code.replace(/`/g, '');
        codeBlocks.push({
          type: 'inline',
          content: content.trim(),
          language: this.detectLanguage(content)
        });
      });
    }

    return codeBlocks;
  }

  /**
   * Detect common errors in message
   */
  static detectErrors(message) {
    const errorPatterns = [
      {
        pattern: /error/gi,
        type: 'general_error'
      },
      {
        pattern: /exception/gi,
        type: 'exception'
      },
      {
        pattern: /syntax\s*error/gi,
        type: 'syntax_error'
      },
      {
        pattern: /runtime\s*error/gi,
        type: 'runtime_error'
      },
      {
        pattern: /nil\s*value/gi,
        type: 'lua_nil_error'
      },
      {
        pattern: /attempt\s*to\s*index/gi,
        type: 'lua_index_error'
      },
      {
        pattern: /unexpected\s*symbol/gi,
        type: 'lua_syntax_error'
      },
      {
        pattern: /reference\s*error/gi,
        type: 'js_reference_error'
      },
      {
        pattern: /type\s*error/gi,
        type: 'js_type_error'
      },
      {
        pattern: /undefined/gi,
        type: 'js_undefined'
      },
      {
        pattern: /null\s*pointer/gi,
        type: 'null_pointer'
      },
      {
        pattern: /segmentation\s*fault/gi,
        type: 'segfault'
      },
      {
        pattern: /compilation\s*error/gi,
        type: 'compilation_error'
      },
      {
        pattern: /indentation\s*error/gi,
        type: 'python_indentation'
      },
      {
        pattern: /name\s*error/gi,
        type: 'python_name_error'
      }
    ];

    const detectedErrors = [];
    
    errorPatterns.forEach(({ pattern, type }) => {
      const matches = message.match(pattern);
      if (matches) {
        detectedErrors.push({
          type,
          matches: matches.length,
          pattern: pattern.source
        });
      }
    });

    return detectedErrors;
  }

  /**
   * Check if message is asking for help with Lua concepts
   */
  static isLuaLearningQuestion(message) {
    const luaLearningPatterns = [
      /how\s+.*\s+lua/gi,
      /what\s+is\s+.*\s+lua/gi,
      /lua\s+.*\s+tutorial/gi,
      /learn\s+lua/gi,
      /lua\s+basics/gi,
      /lua\s+function/gi,
      /lua\s+loop/gi,
      /lua\s+variable/gi,
      /lua\s+table/gi,
      /lua\s+string/gi,
      /roblox\s+scripting/gi,
      /how\s+to\s+script/gi,
      /scripting\s+help/gi,
      /what\s+is\s+.*\s+function/gi,
      /how\s+.*\s+loop/gi,
      /what\s+is\s+.*\s+variable/gi
    ];

    return luaLearningPatterns.some(pattern => pattern.test(message));
  }

  /**
   * Advanced syntax validation for different languages
   */
  static validateSyntax(code, language) {
    const validationResults = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    switch (language) {
      case 'lua':
        return this.validateLuaSyntax(code);
      case 'javascript':
        return this.validateJavaScriptSyntax(code);
      case 'python':
        return this.validatePythonSyntax(code);
      default:
        return this.validateGenericSyntax(code);
    }
  }

  /**
   * Validate Lua/Luau syntax
   */
  static validateLuaSyntax(code) {
    const errors = [];
    const warnings = [];
    const suggestions = [];

    // Check for common Lua syntax issues
    const lines = code.split('\n');
    let openBlocks = 0;
    let inFunction = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      const lineNum = i + 1;

      // Check for unmatched blocks
      if (line.match(/\b(if|for|while|function|repeat)\b/) && !line.includes('end')) {
        openBlocks++;
        if (line.includes('function')) inFunction = true;
      }
      if (line.includes('end')) {
        openBlocks--;
        if (openBlocks === 0) inFunction = false;
      }

      // Check for common errors
      if (line.includes('=') && !line.includes('==') && !line.includes('~=') && !line.includes('local')) {
        if (!line.match(/\w+\s*=\s*.+/)) {
          errors.push({
            line: lineNum,
            message: 'Possible assignment syntax error',
            type: 'syntax'
          });
        }
      }

      // Check for missing 'local' keyword
      if (line.match(/^\s*\w+\s*=/) && !line.includes('local') && !inFunction) {
        warnings.push({
          line: lineNum,
          message: 'Consider using "local" for variable declaration',
          type: 'best_practice'
        });
      }

      // Check for deprecated Roblox methods
      if (line.includes('wait(') && !line.includes('task.wait(')) {
        suggestions.push({
          line: lineNum,
          message: 'Consider using task.wait() instead of wait() for better performance',
          type: 'modernization'
        });
      }
    }

    // Check for unmatched blocks
    if (openBlocks > 0) {
      errors.push({
        line: lines.length,
        message: `${openBlocks} unmatched block(s) - missing 'end' statement(s)`,
        type: 'syntax'
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * Validate JavaScript syntax (basic)
   */
  static validateJavaScriptSyntax(code) {
    const errors = [];
    const warnings = [];
    const suggestions = [];

    const lines = code.split('\n');
    let openBraces = 0;
    let openParens = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      const lineNum = i + 1;

      // Count braces and parentheses
      openBraces += (line.match(/{/g) || []).length;
      openBraces -= (line.match(/}/g) || []).length;
      openParens += (line.match(/\(/g) || []).length;
      openParens -= (line.match(/\)/g) || []).length;

      // Check for missing semicolons
      if (line.length > 0 && !line.endsWith(';') && !line.endsWith('{') && !line.endsWith('}') && !line.startsWith('//')) {
        warnings.push({
          line: lineNum,
          message: 'Consider adding semicolon at end of statement',
          type: 'style'
        });
      }

      // Check for var usage
      if (line.includes('var ')) {
        suggestions.push({
          line: lineNum,
          message: 'Consider using "let" or "const" instead of "var"',
          type: 'modernization'
        });
      }
    }

    if (openBraces !== 0) {
      errors.push({
        line: lines.length,
        message: 'Unmatched braces',
        type: 'syntax'
      });
    }

    if (openParens !== 0) {
      errors.push({
        line: lines.length,
        message: 'Unmatched parentheses',
        type: 'syntax'
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * Validate Python syntax (basic)
   */
  static validatePythonSyntax(code) {
    const errors = [];
    const warnings = [];
    const suggestions = [];

    const lines = code.split('\n');
    let expectedIndent = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineNum = i + 1;

      if (line.trim().length === 0) continue;

      // Check indentation
      const indent = line.length - line.trimStart().length;
      if (line.trim().endsWith(':')) {
        expectedIndent += 4;
      }

      // Basic indentation check
      if (indent % 4 !== 0 && line.trim().length > 0) {
        warnings.push({
          line: lineNum,
          message: 'Inconsistent indentation (should be multiples of 4 spaces)',
          type: 'style'
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * Generic syntax validation
   */
  static validateGenericSyntax(code) {
    const errors = [];
    const warnings = [];
    const suggestions = [];

    // Basic checks for any language
    const lines = code.split('\n');
    let openParens = 0;
    let openBrackets = 0;
    let openBraces = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      openParens += (line.match(/\(/g) || []).length;
      openParens -= (line.match(/\)/g) || []).length;
      openBrackets += (line.match(/\[/g) || []).length;
      openBrackets -= (line.match(/\]/g) || []).length;
      openBraces += (line.match(/{/g) || []).length;
      openBraces -= (line.match(/}/g) || []).length;
    }

    if (openParens !== 0) {
      errors.push({
        line: lines.length,
        message: 'Unmatched parentheses',
        type: 'syntax'
      });
    }

    if (openBrackets !== 0) {
      errors.push({
        line: lines.length,
        message: 'Unmatched brackets',
        type: 'syntax'
      });
    }

    if (openBraces !== 0) {
      errors.push({
        line: lines.length,
        message: 'Unmatched braces',
        type: 'syntax'
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * Auto-fix common code issues
   */
  static autoFixCode(code, language) {
    let fixedCode = code;
    const fixes = [];

    switch (language) {
      case 'lua':
        // Fix common Lua issues
        fixedCode = fixedCode.replace(/wait\(/g, 'task.wait(');
        if (fixedCode !== code) {
          fixes.push('Replaced wait() with task.wait()');
        }
        break;

      case 'javascript':
        // Fix common JS issues
        fixedCode = fixedCode.replace(/var\s+/g, 'let ');
        if (fixedCode !== code) {
          fixes.push('Replaced var with let');
        }
        break;
    }

    // Generic fixes
    fixedCode = fixedCode
      .replace(/\t/g, '    ') // Convert tabs to spaces
      .replace(/\r\n/g, '\n') // Normalize line endings
      .replace(/\r/g, '\n')
      .replace(/\s+$/gm, ''); // Remove trailing whitespace

    if (fixedCode !== code) {
      fixes.push('Normalized whitespace and line endings');
    }

    return {
      code: fixedCode,
      fixes
    };
  }

  /**
   * Format code with proper syntax highlighting and auto-fixes
   */
  static formatCode(code, language = 'lua') {
    // Auto-fix common issues
    const { code: fixedCode, fixes } = this.autoFixCode(code, language);

    // Clean up the code
    const cleanCode = fixedCode.trim();

    // Return formatted code block
    let result = `\`\`\`${language}\n${cleanCode}\n\`\`\``;

    if (fixes.length > 0) {
      result += `\n\n**Auto-fixes applied:**\n${fixes.map(fix => `• ${fix}`).join('\n')}`;
    }

    return result;
  }
}

export default CodeHelper;
