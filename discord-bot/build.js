#!/usr/bin/env node

/**
 * Build script for PROJECT MADARA Discord Bot
 * This script prepares the bot for production deployment
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 Building PROJECT MADARA Discord Bot...');

// Check if required files exist
const requiredFiles = [
  'src/index.js',
  'src/config/config.js',
  'package.json',
  '.env'
];

let buildSuccess = true;

console.log('📋 Checking required files...');
for (const file of requiredFiles) {
  const filePath = join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    buildSuccess = false;
  }
}

// Check environment variables
console.log('\n🔧 Checking environment variables...');
const requiredEnvVars = [
  'DISCORD_BOT_TOKEN',
  'CLIENT_ID'
];

const optionalEnvVars = [
  'OPENROUTER_API_KEY',
  'HUGGINGFACE_API_KEY',
  'GROQ_API_KEY',
  'ALLOWED_CHANNEL_IDS',
  'ALLOWED_GUILD_IDS'
];

// Load .env file
try {
  const envContent = fs.readFileSync(join(__dirname, '.env'), 'utf8');
  const envVars = {};
  
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      envVars[key.trim()] = value.trim();
    }
  });

  // Check required vars
  for (const envVar of requiredEnvVars) {
    if (envVars[envVar]) {
      console.log(`✅ ${envVar}`);
    } else {
      console.log(`❌ ${envVar} - MISSING`);
      buildSuccess = false;
    }
  }

  // Check optional vars
  console.log('\n📝 Optional environment variables:');
  for (const envVar of optionalEnvVars) {
    if (envVars[envVar]) {
      console.log(`✅ ${envVar}`);
    } else {
      console.log(`⚠️  ${envVar} - Not set`);
    }
  }

  // Check if at least one AI API key is present
  const hasAIKey = optionalEnvVars.slice(0, 3).some(key => envVars[key]);
  if (!hasAIKey) {
    console.log('\n❌ No AI API key found! Please set at least one of: OPENROUTER_API_KEY, HUGGINGFACE_API_KEY, GROQ_API_KEY');
    buildSuccess = false;
  }

} catch (error) {
  console.log('❌ Error reading .env file:', error.message);
  buildSuccess = false;
}

// Validate package.json
console.log('\n📦 Validating package.json...');
try {
  const packageJson = JSON.parse(fs.readFileSync(join(__dirname, 'package.json'), 'utf8'));
  
  if (packageJson.type === 'module') {
    console.log('✅ ES Modules configured');
  } else {
    console.log('⚠️  Not using ES Modules');
  }

  if (packageJson.engines && packageJson.engines.node) {
    console.log(`✅ Node.js version requirement: ${packageJson.engines.node}`);
  }

} catch (error) {
  console.log('❌ Error reading package.json:', error.message);
  buildSuccess = false;
}

// Final result
console.log('\n' + '='.repeat(50));
if (buildSuccess) {
  console.log('🎉 Build completed successfully!');
  console.log('✅ Bot is ready for deployment');
  console.log('\n📋 Next steps:');
  console.log('1. Run: npm start');
  console.log('2. Register slash commands if needed');
  console.log('3. Invite bot to your server');
  process.exit(0);
} else {
  console.log('❌ Build failed!');
  console.log('🔧 Please fix the issues above and try again');
  process.exit(1);
}
