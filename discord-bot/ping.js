import { MadaraEmbedBuilder } from './embedBuilder.js';

export const pingCommand = {
  name: 'ping',
  description: 'Check bot latency and response time',
  aliases: ['latency', 'pong'],
  
  async execute(message, args, client) {
    const sent = await message.reply('🏓 Pinging...');
    
    const botLatency = sent.createdTimestamp - message.createdTimestamp;
    const apiLatency = Math.round(client.ws.ping);
    
    // Determine latency quality
    const getLatencyStatus = (latency) => {
      if (latency < 100) return { emoji: '🟢', status: 'Excellent' };
      if (latency < 200) return { emoji: '🟡', status: 'Good' };
      if (latency < 300) return { emoji: '🟠', status: 'Fair' };
      return { emoji: '🔴', status: 'Poor' };
    };
    
    const botStatus = getLatencyStatus(botLatency);
    const apiStatus = getLatencyStatus(apiLatency);
    
    const embed = MadaraEmbedBuilder.createEmbed({
      title: '🏓 Pong!',
      description: 'PROJECT MADARA Bot Latency Information',
      fields: [
        {
          name: '🤖 Bot Latency',
          value: `${botStatus.emoji} ${botLatency}ms (${botStatus.status})`,
          inline: true
        },
        {
          name: '🌐 API Latency',
          value: `${apiStatus.emoji} ${apiLatency}ms (${apiStatus.status})`,
          inline: true
        },
        {
          name: '📊 Overall Status',
          value: botLatency < 200 && apiLatency < 200 ? '✅ All systems operational' : '⚠️ Some delays detected',
          inline: false
        }
      ],
      color: botLatency < 200 && apiLatency < 200 ? '#00FF00' : '#FFA500'
    });
    
    await sent.edit({ content: null, embeds: [embed] });
  }
};

export default pingCommand;
