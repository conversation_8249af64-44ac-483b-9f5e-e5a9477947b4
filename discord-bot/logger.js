// Minimal console-only logger for free hosting
// No file logging to save memory and avoid folder issues

// Simple console logger - minimal memory usage
const logger = {
  info: (message, meta = {}) => {
    // Only log important info to reduce CPU
    if (message.includes('ready') || message.includes('Health server') || message.includes('Bot presence')) {
      console.log(`[${new Date().toLocaleTimeString()}] INFO: ${message}`);
    }
  },
  warn: (message, meta = {}) => {
    console.warn(`[${new Date().toLocaleTimeString()}] WARN: ${message}`);
  },
  error: (message, meta = {}) => {
    console.error(`[${new Date().toLocaleTimeString()}] ERROR: ${message}`);
  }
};

/**
 * Log Discord events with minimal overhead
 */
export function logDiscordEvent(eventType, data = {}) {
  // Only log critical events to save CPU
  if (eventType === 'bot_ready' || eventType.includes('error')) {
    logger.info(`Discord Event: ${eventType}`, data);
  }
}

/**
 * Log errors with minimal overhead
 */
export function logError(error, context = '') {
  const message = context ? `${context}: ${error.message}` : error.message;
  logger.error(message);
}

/**
 * Log security events - only warnings and errors
 */
export function logSecurityEvent(eventType, data = {}) {
  if (eventType.includes('unauthorized') || eventType.includes('blocked')) {
    logger.warn(`Security: ${eventType}`, data);
  }
}

/**
 * Log AI interactions - minimal logging
 */
export function logAIInteraction(eventType, data = {}) {
  // Only log errors to save CPU
  if (eventType.includes('error') || eventType.includes('failed')) {
    logger.error(`AI: ${eventType}`, data);
  }
}

export default logger;
