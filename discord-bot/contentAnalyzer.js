/**
 * Content Analyzer for PROJECT MADARA Discord bot
 * Analyzes message content to determine appropriate response type
 */
export class ContentAnalyzer {
  /**
   * Analyze message content and determine the appropriate response type
   */
  static analyzeContent(message) {
    const content = message.content || message;
    const analysis = {
      type: 'unknown',
      confidence: 0,
      context: {},
      responseStyle: 'neutral'
    };

    // Check for different content types in order of specificity
    const contentChecks = [
      this.checkForMedia,
      this.checkForCasualContent,
      this.checkForCodeContent,
      this.checkForQuestion,
      this.checkForGreeting,
      this.checkForEmotional
    ];

    for (const check of contentChecks) {
      const result = check.call(this, content, message);
      if (result.confidence > analysis.confidence) {
        Object.assign(analysis, result);
      }
    }

    // Add additional context
    analysis.context.length = content.length;
    analysis.context.hasAttachments = message.attachments?.size > 0;
    analysis.context.hasEmbeds = message.embeds?.length > 0;

    return analysis;
  }

  /**
   * Check for media content (images, GIFs, videos)
   */
  static checkForMedia(content, message) {
    const mediaPatterns = [
      // Direct media URLs
      /https?:\/\/[^\s]*\.(gif|jpg|jpeg|png|webp|mp4|webm|mov)/gi,
      // Media hosting domains
      /https?:\/\/(tenor\.com|giphy\.com|imgur\.com|media\.discordapp\.net|cdn\.discordapp\.com)/gi,
      // Media file extensions in text
      /\.(gif|jpg|jpeg|png|webp|mp4|webm)\b/gi
    ];

    const hasMediaUrl = mediaPatterns.some(pattern => pattern.test(content));
    const hasAttachments = message?.attachments?.size > 0;

    if (hasMediaUrl || hasAttachments) {
      return {
        type: 'media',
        confidence: 0.9,
        responseStyle: 'fun',
        context: {
          mediaType: this.detectMediaType(content),
          isAttachment: hasAttachments
        }
      };
    }

    return { confidence: 0 };
  }

  /**
   * Check for casual/fun content
   */
  static checkForCasualContent(content) {
    const casualPatterns = [
      // Expressions and reactions
      /^(lol|lmao|haha|xd|omg|wow|nice|cool|awesome|amazing)+$/gi,
      // Emoji-only messages
      /^[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F1E0}-\u{1F1FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}\s]+$/u,
      // Emoticons
      /^[:;=]-?[)(\]D]+$/g,
      // Casual greetings
      /^(hey|hi|hello|sup|yo)\s*[!.]*$/gi,
      // Meme references
      /(poggers|pog|based|cringe|sus|among\s*us|bruh|fr|no\s*cap)/gi
    ];

    const casualScore = casualPatterns.reduce((score, pattern) => {
      return score + (pattern.test(content) ? 1 : 0);
    }, 0);

    if (casualScore > 0 || content.length < 20) {
      return {
        type: 'casual',
        confidence: Math.min(casualScore * 0.3 + 0.4, 0.8),
        responseStyle: 'fun',
        context: {
          casualScore,
          isShort: content.length < 20
        }
      };
    }

    return { confidence: 0 };
  }

  /**
   * Check for code-related content
   */
  static checkForCodeContent(content) {
    // Import CodeHelper to use existing logic
    const codeAnalysis = this.detectCodeSimple(content);

    if (codeAnalysis.hasCode) {
      return {
        type: 'code',
        confidence: codeAnalysis.confidence || 0.8,
        responseStyle: 'technical',
        context: {
          language: codeAnalysis.language,
          hasErrors: codeAnalysis.errors?.length > 0,
          codeBlocks: codeAnalysis.codeBlocks?.length || 0
        }
      };
    }

    return { confidence: 0 };
  }

  /**
   * Check for questions
   */
  static checkForQuestion(content) {
    const questionPatterns = [
      /^(what|how|why|when|where|who|which|can|could|would|should|is|are|do|does|did)\s/gi,
      /\?+\s*$/g,
      /(help|explain|tell\s+me|show\s+me)/gi
    ];

    const questionScore = questionPatterns.reduce((score, pattern) => {
      return score + (pattern.test(content) ? 1 : 0);
    }, 0);

    if (questionScore > 0) {
      return {
        type: 'question',
        confidence: Math.min(questionScore * 0.3 + 0.3, 0.7),
        responseStyle: 'helpful',
        context: {
          questionScore,
          hasQuestionMark: /\?/.test(content)
        }
      };
    }

    return { confidence: 0 };
  }

  /**
   * Check for greetings
   */
  static checkForGreeting(content) {
    const greetingPatterns = [
      /^(hello|hi|hey|good\s+(morning|afternoon|evening)|greetings)/gi,
      /^(what's\s+up|how\s+are\s+you|how's\s+it\s+going)/gi
    ];

    if (greetingPatterns.some(pattern => pattern.test(content))) {
      return {
        type: 'greeting',
        confidence: 0.8,
        responseStyle: 'friendly',
        context: {}
      };
    }

    return { confidence: 0 };
  }

  /**
   * Check for emotional content
   */
  static checkForEmotional(content) {
    const emotionalPatterns = [
      /(thanks|thank\s+you|appreciate)/gi,
      /(sorry|apologize)/gi,
      /(love|hate|angry|sad|happy|excited)/gi,
      /(frustrated|confused|stuck)/gi
    ];

    const emotionalScore = emotionalPatterns.reduce((score, pattern) => {
      return score + (pattern.test(content) ? 1 : 0);
    }, 0);

    if (emotionalScore > 0) {
      return {
        type: 'emotional',
        confidence: Math.min(emotionalScore * 0.2 + 0.3, 0.6),
        responseStyle: 'empathetic',
        context: {
          emotionalScore
        }
      };
    }

    return { confidence: 0 };
  }

  /**
   * Detect media type from content
   */
  static detectMediaType(content) {
    if (/\.(gif|webp)/gi.test(content) || /tenor\.com|giphy\.com/gi.test(content)) {
      return 'gif';
    }
    if (/\.(jpg|jpeg|png)/gi.test(content)) {
      return 'image';
    }
    if (/\.(mp4|webm|mov)/gi.test(content)) {
      return 'video';
    }
    return 'unknown';
  }

  /**
   * Simple code detection (lightweight version)
   */
  static detectCodeSimple(content) {
    // Check for explicit code markers first
    const explicitMarkers = [
      /```[\s\S]*```/g,
      /`[^`]+`/g
    ];

    if (explicitMarkers.some(pattern => pattern.test(content))) {
      return { hasCode: true, confidence: 0.9, language: 'unknown' };
    }

    // Check for programming keywords
    const codeKeywords = [
      /\b(function|local|if|then|else|end|for|while|do|return|print)\b/gi,
      /\b(var|let|const|console\.log|document\.)\b/gi,
      /\b(def|import|from|class|print|if|else|elif|for|while)\b/gi
    ];

    const keywordMatches = codeKeywords.reduce((count, pattern) => {
      const matches = content.match(pattern);
      return count + (matches ? matches.length : 0);
    }, 0);

    if (keywordMatches >= 2) {
      return { hasCode: true, confidence: 0.7, language: 'unknown' };
    }

    return { hasCode: false };
  }

  /**
   * Get response suggestions based on content analysis
   */
  static getResponseSuggestions(analysis) {
    const suggestions = {
      tone: 'neutral',
      includeEmoji: false,
      responseLength: 'medium',
      focusAreas: []
    };

    switch (analysis.type) {
      case 'media':
        suggestions.tone = 'fun';
        suggestions.includeEmoji = true;
        suggestions.responseLength = 'short';
        suggestions.focusAreas = ['react_to_media', 'casual_conversation'];
        break;

      case 'casual':
        suggestions.tone = 'friendly';
        suggestions.includeEmoji = true;
        suggestions.responseLength = 'short';
        suggestions.focusAreas = ['casual_response', 'keep_conversation_light'];
        break;

      case 'code':
        suggestions.tone = 'technical';
        suggestions.includeEmoji = false;
        suggestions.responseLength = 'long';
        suggestions.focusAreas = ['code_help', 'technical_explanation', 'provide_examples'];
        break;

      case 'question':
        suggestions.tone = 'helpful';
        suggestions.includeEmoji = false;
        suggestions.responseLength = 'medium';
        suggestions.focusAreas = ['answer_question', 'provide_context', 'be_informative'];
        break;

      case 'greeting':
        suggestions.tone = 'friendly';
        suggestions.includeEmoji = true;
        suggestions.responseLength = 'short';
        suggestions.focusAreas = ['greet_back', 'offer_help'];
        break;

      case 'emotional':
        suggestions.tone = 'empathetic';
        suggestions.includeEmoji = false;
        suggestions.responseLength = 'medium';
        suggestions.focusAreas = ['acknowledge_emotion', 'provide_support'];
        break;
    }

    return suggestions;
  }
}

export default ContentAnalyzer;
