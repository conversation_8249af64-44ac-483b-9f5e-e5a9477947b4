import { SlashCommandBuilder, PermissionFlagsBits } from 'discord.js';

/**
 * Slash Command Builder for PROJECT MADARA Discord bot
 */
export class MadaraSlashCommandBuilder {
  /**
   * Build all slash commands for registration
   */
  static buildAllCommands() {
    return [
      this.buildAskCommand(),
      this.buildTutorialCommand(),
      this.buildHelpCommand(),
      this.buildPingCommand(),
      this.buildStatusCommand(),
      this.buildAboutCommand(),
      this.buildScriptsCommand(),
      this.buildSupportCommand(),
      this.buildContextCommand()
    ];
  }

  /**
   * Build ask command with auto-complete
   */
  static buildAskCommand() {
    return new SlashCommandBuilder()
      .setName('ask')
      .setDescription('Ask MADARA AI a question about coding, Roblox scripting, or PROJECT MADARA')
      .addStringOption(option =>
        option
          .setName('question')
          .setDescription('Your question for MADARA AI')
          .setRequired(true)
          .setMaxLength(2000)
      )
      .addStringOption(option =>
        option
          .setName('language')
          .setDescription('Programming language context (optional)')
          .setRequired(false)
          .addChoices(
            { name: 'Lua/Luau', value: 'lua' },
            { name: 'JavaScript', value: 'javascript' },
            { name: 'Python', value: 'python' },
            { name: 'C++', value: 'cpp' },
            { name: 'Java', value: 'java' },
            { name: 'Other', value: 'other' }
          )
      )
      .addBooleanOption(option =>
        option
          .setName('private')
          .setDescription('Send response as ephemeral (only you can see it)')
          .setRequired(false)
      );
  }

  /**
   * Build tutorial command with categories
   */
  static buildTutorialCommand() {
    return new SlashCommandBuilder()
      .setName('tutorial')
      .setDescription('Access interactive tutorials and step-by-step guides')
      .addStringOption(option =>
        option
          .setName('topic')
          .setDescription('Tutorial topic')
          .setRequired(false)
          .addChoices(
            { name: '🎯 Lua Scripting Basics', value: 'scripting' },
            { name: '⚙️ Script Executors', value: 'executor' },
            { name: '🔧 Troubleshooting', value: 'troubleshooting' },
            { name: '🛡️ Security & Safety', value: 'security' },
            { name: '📚 All Categories', value: 'categories' }
          )
      );
  }

  /**
   * Build help command
   */
  static buildHelpCommand() {
    return new SlashCommandBuilder()
      .setName('help')
      .setDescription('Show all available PROJECT MADARA bot commands and features')
      .addStringOption(option =>
        option
          .setName('category')
          .setDescription('Help category')
          .setRequired(false)
          .addChoices(
            { name: '🤖 AI Features', value: 'ai' },
            { name: '📚 Tutorials', value: 'tutorials' },
            { name: '📊 Bot Commands', value: 'commands' },
            { name: '🔗 Links & Support', value: 'links' }
          )
      );
  }

  /**
   * Build ping command
   */
  static buildPingCommand() {
    return new SlashCommandBuilder()
      .setName('ping')
      .setDescription('Check bot latency and response time');
  }

  /**
   * Build status command
   */
  static buildStatusCommand() {
    return new SlashCommandBuilder()
      .setName('status')
      .setDescription('Show bot status, statistics, and health information');
  }

  /**
   * Build about command
   */
  static buildAboutCommand() {
    return new SlashCommandBuilder()
      .setName('about')
      .setDescription('Learn about PROJECT MADARA and its features');
  }

  /**
   * Build scripts command
   */
  static buildScriptsCommand() {
    return new SlashCommandBuilder()
      .setName('scripts')
      .setDescription('Get information about PROJECT MADARA scripts and how to use them')
      .addStringOption(option =>
        option
          .setName('category')
          .setDescription('Script category')
          .setRequired(false)
          .addChoices(
            { name: '🎮 Game Scripts', value: 'games' },
            { name: '🔧 Utility Scripts', value: 'utility' },
            { name: '🎯 Popular Scripts', value: 'popular' },
            { name: '📋 All Scripts', value: 'all' }
          )
      );
  }

  /**
   * Build support command
   */
  static buildSupportCommand() {
    return new SlashCommandBuilder()
      .setName('support')
      .setDescription('Get PROJECT MADARA support information and contact details')
      .addStringOption(option =>
        option
          .setName('issue')
          .setDescription('Type of issue you need help with')
          .setRequired(false)
          .addChoices(
            { name: '🐛 Bug Report', value: 'bug' },
            { name: '❓ General Question', value: 'question' },
            { name: '💡 Feature Request', value: 'feature' },
            { name: '🔒 Security Issue', value: 'security' },
            { name: '📞 Contact Info', value: 'contact' }
          )
      );
  }

  /**
   * Build context command
   */
  static buildContextCommand() {
    return new SlashCommandBuilder()
      .setName('context')
      .setDescription('Manage your conversation context and AI preferences')
      .addStringOption(option =>
        option
          .setName('action')
          .setDescription('Context action')
          .setRequired(false)
          .addChoices(
            { name: '👁️ View Profile', value: 'view' },
            { name: '🗑️ Clear Context', value: 'clear' },
            { name: '📜 Show History', value: 'history' },
            { name: '📊 View Stats', value: 'stats' },
            { name: '⚙️ Set Preference', value: 'set' }
          )
      )
      .addStringOption(option =>
        option
          .setName('preference')
          .setDescription('Preference to set (only for set action)')
          .setRequired(false)
          .addChoices(
            { name: 'Skill Level', value: 'skill' },
            { name: 'Response Style', value: 'style' },
            { name: 'Programming Language', value: 'language' },
            { name: 'Learning Goal', value: 'goal' }
          )
      )
      .addStringOption(option =>
        option
          .setName('value')
          .setDescription('Value for the preference (only for set action)')
          .setRequired(false)
          .setMaxLength(50)
      );
  }

  /**
   * Build admin commands (if needed)
   */
  static buildAdminCommands() {
    return [
      new SlashCommandBuilder()
        .setName('admin-reload')
        .setDescription('Reload bot configuration and commands')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),
      
      new SlashCommandBuilder()
        .setName('admin-stats')
        .setDescription('View detailed bot statistics and analytics')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),
      
      new SlashCommandBuilder()
        .setName('admin-cache')
        .setDescription('Manage bot cache (clear, view stats)')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addStringOption(option =>
          option
            .setName('action')
            .setDescription('Cache action')
            .setRequired(true)
            .addChoices(
              { name: 'Clear All', value: 'clear' },
              { name: 'View Stats', value: 'stats' },
              { name: 'Clear FAQ Cache', value: 'clear-faq' },
              { name: 'Clear Response Cache', value: 'clear-responses' }
            )
        )
    ];
  }

  /**
   * Get command data for registration
   */
  static getCommandData() {
    const commands = this.buildAllCommands();
    return commands.map(command => command.toJSON());
  }

  /**
   * Get admin command data for registration
   */
  static getAdminCommandData() {
    const commands = this.buildAdminCommands();
    return commands.map(command => command.toJSON());
  }

  /**
   * Get all command data (regular + admin)
   */
  static getAllCommandData() {
    return [
      ...this.getCommandData(),
      ...this.getAdminCommandData()
    ];
  }
}

export default MadaraSlashCommandBuilder;
