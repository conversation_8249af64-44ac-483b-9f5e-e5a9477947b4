import { config } from './config.js';
import { logSecurityEvent } from './logger.js';

/**
 * Rate limiting storage
 */
const rateLimitMap = new Map();

/**
 * Security utility class for PROJECT MADARA Discord bot
 */
export class SecurityManager {
  /**
   * Check if guild is allowed
   */
  static isGuildAllowed(guildId) {
    // If no allowed guilds specified, allow all
    if (config.security.allowedGuilds.length === 0) {
      return true;
    }
    
    const allowed = config.security.allowedGuilds.includes(guildId);
    
    if (!allowed) {
      logSecurityEvent('unauthorized_guild_access', { guildId });
    }
    
    return allowed;
  }

  /**
   * Check if channel is allowed for AI interactions
   */
  static isChannelAllowed(channelId) {
    // If no allowed channels specified, require explicit configuration
    if (config.security.allowedChannels.length === 0) {
      return false;
    }

    const allowed = config.security.allowedChannels.includes(channelId);

    if (!allowed) {
      logSecurityEvent('unauthorized_channel_access', { channelId });
    }

    return allowed;
  }

  /**
   * Check if user is bot owner
   */
  static isOwner(userId) {
    return config.security.ownerUserIds.includes(userId);
  }

  /**
   * Rate limiting check
   */
  static checkRateLimit(userId) {
    const now = Date.now();
    const userKey = `rate_limit_${userId}`;
    
    if (!rateLimitMap.has(userKey)) {
      rateLimitMap.set(userKey, {
        requests: 1,
        resetTime: now + config.security.rateLimitWindow
      });
      return { allowed: true, remaining: config.security.rateLimitRequests - 1 };
    }
    
    const userData = rateLimitMap.get(userKey);
    
    // Reset if window expired
    if (now > userData.resetTime) {
      rateLimitMap.set(userKey, {
        requests: 1,
        resetTime: now + config.security.rateLimitWindow
      });
      return { allowed: true, remaining: config.security.rateLimitRequests - 1 };
    }
    
    // Check if limit exceeded
    if (userData.requests >= config.security.rateLimitRequests) {
      logSecurityEvent('rate_limit_exceeded', { 
        userId, 
        requests: userData.requests,
        resetTime: userData.resetTime 
      });
      
      const resetIn = Math.ceil((userData.resetTime - now) / 1000);
      return { 
        allowed: false, 
        remaining: 0, 
        resetIn 
      };
    }
    
    // Increment request count
    userData.requests++;
    rateLimitMap.set(userKey, userData);
    
    return { 
      allowed: true, 
      remaining: config.security.rateLimitRequests - userData.requests 
    };
  }

  /**
   * Clean up expired rate limit entries
   */
  static cleanupRateLimits() {
    const now = Date.now();
    
    for (const [key, data] of rateLimitMap.entries()) {
      if (now > data.resetTime) {
        rateLimitMap.delete(key);
      }
    }
  }

  /**
   * Validate message content for security and appropriateness
   */
  static validateMessageContent(content) {
    // Check for potential security issues
    const securityPatterns = [
      /discord\.gg\/[a-zA-Z0-9]+/gi, // Discord invites
      /https?:\/\/[^\s]+/gi, // URLs (for logging)
    ];

    // Strict profanity and offensive language filter
    const profanityPatterns = [
      // Strong profanity
      /\b(fuck|fucking|fucked|shit|shitting|bitch|bitches|asshole|bastard)\b/gi,

      // Racial slurs (partial list - add more as needed)
      /\b(nigger|nigga|chink|spic|wetback|kike|gook|towelhead)\b/gi,

      // Homophobic/transphobic slurs
      /\b(faggot|fag|tranny|dyke)\b/gi,

      // Sexual/inappropriate content
      /\b(pussy|dick|cock|penis|vagina|tits|boobs|porn|sex|sexual|nude|naked)\b/gi,

      // Harmful/threatening content
      /\b(kill\s+(yourself|myself)|kys|suicide|die\s+please|go\s+die)\b/gi,

      // Other offensive terms
      /\b(retard|retarded|autistic|cancer|aids|hitler|nazi)\b/gi
    ];

    const issues = [];
    let hasInappropriateContent = false;

    // Check security patterns
    securityPatterns.forEach((pattern, index) => {
      const matches = content.match(pattern);
      if (matches) {
        switch (index) {
          case 0:
            issues.push({ type: 'discord_invite', matches });
            break;
          case 1:
            issues.push({ type: 'external_url', matches });
            break;
        }
      }
    });

    // Check for inappropriate content
    profanityPatterns.forEach((pattern) => {
      const matches = content.match(pattern);
      if (matches) {
        hasInappropriateContent = true;
        issues.push({ type: 'inappropriate_language', matches });
      }
    });

    return {
      isValid: !hasInappropriateContent, // Block inappropriate content
      issues,
      hasInappropriateContent
    };
  }

  /**
   * Log user interaction
   */
  static logUserInteraction(interaction) {
    const { user, guild, channel, command } = interaction;
    
    logSecurityEvent('user_interaction', {
      userId: user.id,
      username: user.username,
      guildId: guild?.id,
      guildName: guild?.name,
      channelId: channel?.id,
      channelName: channel?.name,
      command: command || 'unknown'
    });
  }
}

/**
 * Cleanup rate limits every 5 minutes
 */
setInterval(() => {
  SecurityManager.cleanupRateLimits();
}, 5 * 60 * 1000);

export default SecurityManager;
