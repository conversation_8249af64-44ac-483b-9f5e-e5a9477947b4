import fs from 'fs';
import path from 'path';

function removeComments(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Remove single-line comments (// ...)
  const withoutSingleLine = content.replace(/^\s*\/\/.*$/gm, '');
  
  // Remove multi-line comments (/* ... */)
  const withoutMultiLine = withoutSingleLine.replace(/\/\*[\s\S]*?\*\//g, '');
  
  // Remove empty lines that were left after comment removal
  const cleaned = withoutMultiLine.replace(/^\s*\n/gm, '');
  
  fs.writeFileSync(filePath, cleaned);
  console.log(`Cleaned comments from ${filePath}`);
}

// Security files to clean
const filesToClean = [
  'src/utils/scriptIntegrity.js',
  'src/utils/securityConfig.js',
  'src/utils/behaviorTracker.js',
  'src/utils/mlBehaviorAnalysis.js',
  'src/utils/deviceFingerprint.js',
  'src/utils/debugLogger.js',
  'src/utils/clientProtection.js',
  'src/utils/honeypotTraps.js',
  'src/utils/performanceMonitoring.js',
  'src/utils/realTimeMonitoring.js',
  'src/utils/violationReporting.js',
  'src/utils/temporaryBans.js',
  'src/utils/consoleProtection.js',
  'src/utils/scriptInjectionProtection.js',
  'netlify/functions/security-violation.js',
  'netlify/functions/generate-key.js',
  'netlify/functions/validate-key.js',
  'netlify/functions/step-session.js',
  'netlify/functions/security.js',
  'netlify/functions/admin-keys.js',
  'netlify/functions/script-requests.js',
  'netlify/functions/analytics.js',
  'netlify/functions/ml-analysis.js',
  'netlify/functions/behavior-monitoring.js',
  'src/services/securityService.js',
  'src/components/security/SecurityDashboard.jsx',
  'src/components/admin/SecurityDashboard.jsx',
  'src/components/admin/MLSecurityDashboard.jsx',
  'src/KeySystem/KeyGenerator.jsx'
  
];

filesToClean.forEach(file => {
  if (fs.existsSync(file)) {
    removeComments(file);
  }
});

console.log('All security files cleaned!');