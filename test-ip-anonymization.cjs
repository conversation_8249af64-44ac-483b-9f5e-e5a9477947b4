/**
 * Test Script for IP Address Anonymization System
 * 
 * This script thoroughly tests the IP anonymization functionality
 * to ensure it works correctly and maintains security features.
 * 
 * Usage:
 * node test-ip-anonymization.cjs
 */

const { IPAnonymizer } = require('./netlify/functions/utils/ipAnonymization.cjs');

class IPAnonymizationTester {
  constructor() {
    this.anonymizer = new IPAnonymizer();
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0
    };
  }

  /**
   * Assert helper function
   */
  assert(condition, message) {
    this.testResults.total++;
    if (condition) {
      console.log(`✅ ${message}`);
      this.testResults.passed++;
    } else {
      console.log(`❌ ${message}`);
      this.testResults.failed++;
    }
  }

  /**
   * Test basic IP anonymization
   */
  testBasicAnonymization() {
    console.log('\n🧪 Testing Basic IP Anonymization');
    
    // Test valid IPv4 addresses
    const ipv4 = '***********';
    const hash1 = this.anonymizer.anonymizeIP(ipv4);
    const hash2 = this.anonymizer.anonymizeIP(ipv4);
    
    this.assert(hash1 === hash2, 'Same IP produces same hash (consistency)');
    this.assert(hash1.length === 64, 'Hash is 64 characters (SHA-256)');
    this.assert(/^[a-f0-9]+$/.test(hash1), 'Hash contains only hex characters');
    this.assert(hash1 !== ipv4, 'Hash is different from original IP');

    // Test different IPs produce different hashes
    const differentIP = '********';
    const differentHash = this.anonymizer.anonymizeIP(differentIP);
    this.assert(hash1 !== differentHash, 'Different IPs produce different hashes');

    // Test edge cases
    this.assert(this.anonymizer.anonymizeIP('unknown') === 'unknown', 'Unknown IP returns unknown');
    this.assert(this.anonymizer.anonymizeIP('localhost') === 'unknown', 'Localhost returns unknown');
    this.assert(this.anonymizer.anonymizeIP('127.0.0.1') === 'unknown', '127.0.0.1 returns unknown');
    this.assert(this.anonymizer.anonymizeIP('') === 'unknown', 'Empty string returns unknown');
    this.assert(this.anonymizer.anonymizeIP(null) === 'unknown', 'Null returns unknown');
  }

  /**
   * Test IP validation
   */
  testIPValidation() {
    console.log('\n🧪 Testing IP Validation');
    
    // Valid IPv4 addresses
    this.assert(this.anonymizer.isValidIP('***********'), 'Valid IPv4 is recognized');
    this.assert(this.anonymizer.isValidIP('********'), 'Valid IPv4 is recognized');
    this.assert(this.anonymizer.isValidIP('***************'), 'Max IPv4 is valid');
    this.assert(this.anonymizer.isValidIP('0.0.0.0'), 'Min IPv4 is valid');

    // Valid IPv6 addresses
    this.assert(this.anonymizer.isValidIP('2001:0db8:85a3:0000:0000:8a2e:0370:7334'), 'Valid IPv6 is recognized');
    this.assert(this.anonymizer.isValidIP('fe80:0000:0000:0000:0202:b3ff:fe1e:8329'), 'Valid IPv6 is recognized');

    // Invalid addresses
    this.assert(!this.anonymizer.isValidIP('256.1.1.1'), 'Invalid IPv4 is rejected');
    this.assert(!this.anonymizer.isValidIP('192.168.1'), 'Incomplete IPv4 is rejected');
    this.assert(!this.anonymizer.isValidIP('not.an.ip.address'), 'Non-IP string is rejected');
    this.assert(!this.anonymizer.isValidIP(''), 'Empty string is rejected');
    this.assert(!this.anonymizer.isValidIP(null), 'Null is rejected');
  }

  /**
   * Test subnet anonymization
   */
  testSubnetAnonymization() {
    console.log('\n🧪 Testing Subnet Anonymization');
    
    const ip1 = '***********00';
    const ip2 = '*************';
    const ip3 = '*************';
    
    // Same subnet should produce same hash
    const subnet1 = this.anonymizer.anonymizeIPSubnet(ip1, 24);
    const subnet2 = this.anonymizer.anonymizeIPSubnet(ip2, 24);
    const subnet3 = this.anonymizer.anonymizeIPSubnet(ip3, 24);
    
    this.assert(subnet1 === subnet2, 'IPs in same /24 subnet produce same hash');
    this.assert(subnet1 !== subnet3, 'IPs in different /24 subnets produce different hashes');
    
    // Test different subnet masks
    const subnet16_1 = this.anonymizer.anonymizeIPSubnet(ip1, 16);
    const subnet16_3 = this.anonymizer.anonymizeIPSubnet(ip3, 16);
    this.assert(subnet16_1 === subnet16_3, 'IPs in same /16 subnet produce same hash');
  }

  /**
   * Test IP comparison
   */
  testIPComparison() {
    console.log('\n🧪 Testing IP Comparison');
    
    const ip1 = '***********';
    const ip2 = '***********';
    const ip3 = '********';
    
    this.assert(this.anonymizer.compareIPs(ip1, ip2), 'Same IPs compare as equal');
    this.assert(!this.anonymizer.compareIPs(ip1, ip3), 'Different IPs compare as not equal');
    this.assert(this.anonymizer.compareIPs('unknown', 'unknown'), 'Unknown IPs compare as equal');
  }

  /**
   * Test header extraction
   */
  testHeaderExtraction() {
    console.log('\n🧪 Testing Header Extraction');
    
    // Test various header scenarios
    const headers1 = { 'x-forwarded-for': '***********' };
    const ip1 = this.anonymizer.extractIPFromHeaders(headers1);
    this.assert(ip1 === '***********', 'Extracts IP from x-forwarded-for');
    
    const headers2 = { 'client-ip': '********' };
    const ip2 = this.anonymizer.extractIPFromHeaders(headers2);
    this.assert(ip2 === '********', 'Extracts IP from client-ip');
    
    const headers3 = { 'x-forwarded-for': '***********, ********' };
    const ip3 = this.anonymizer.extractIPFromHeaders(headers3);
    this.assert(ip3 === '***********', 'Extracts first IP from comma-separated list');
    
    const headers4 = {};
    const ip4 = this.anonymizer.extractIPFromHeaders(headers4);
    this.assert(ip4 === 'unknown', 'Returns unknown when no IP headers present');
  }

  /**
   * Test caching functionality
   */
  testCaching() {
    console.log('\n🧪 Testing Caching Functionality');
    
    // Clear cache first
    this.anonymizer.clearCache();
    
    const ip = '***********';
    
    // First call should miss cache
    const hash1 = this.anonymizer.anonymizeIP(ip, true);
    
    // Second call should hit cache
    const hash2 = this.anonymizer.anonymizeIP(ip, true);
    
    this.assert(hash1 === hash2, 'Cached result matches original');
    
    const stats = this.anonymizer.getCacheStats();
    this.assert(stats.size > 0, 'Cache contains entries');
    
    // Test cache clearing
    this.anonymizer.clearCache();
    const statsAfterClear = this.anonymizer.getCacheStats();
    this.assert(statsAfterClear.size === 0, 'Cache is cleared');
  }

  /**
   * Test display formatting
   */
  testDisplayFormatting() {
    console.log('\n🧪 Testing Display Formatting');
    
    const hash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
    
    const truncated = this.anonymizer.formatHashForDisplay(hash, true);
    this.assert(truncated === 'abcdef12...7890', 'Hash is properly truncated for display');
    
    const full = this.anonymizer.formatHashForDisplay(hash, false);
    this.assert(full === hash, 'Full hash is returned when not truncating');
    
    const shortId = this.anonymizer.generateShortId(hash);
    this.assert(shortId === 'ABCDEF', 'Short ID is generated correctly');
    
    const unknownDisplay = this.anonymizer.formatHashForDisplay('unknown');
    this.assert(unknownDisplay === 'Unknown', 'Unknown hash displays as Unknown');
  }

  /**
   * Test performance with large datasets
   */
  testPerformance() {
    console.log('\n🧪 Testing Performance');
    
    const testIPs = [];
    for (let i = 0; i < 1000; i++) {
      testIPs.push(`192.168.${Math.floor(i / 256)}.${i % 256}`);
    }
    
    const startTime = Date.now();
    
    for (const ip of testIPs) {
      this.anonymizer.anonymizeIP(ip);
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    this.assert(duration < 5000, `Performance test completed in ${duration}ms (should be < 5000ms)`);
    
    const stats = this.anonymizer.getCacheStats();
    this.assert(stats.size > 0, 'Cache is populated after performance test');
  }

  /**
   * Test security properties
   */
  testSecurity() {
    console.log('\n🧪 Testing Security Properties');
    
    const ip = '***********';
    const hash = this.anonymizer.anonymizeIP(ip);
    
    // Hash should not contain the original IP as a substring
    this.assert(!hash.includes('***********'), 'Hash does not contain original IP');
    this.assert(!hash.includes('192.168'), 'Hash does not contain IP subnet');
    this.assert(hash !== ip, 'Hash is different from original IP');
    
    // Hash should be deterministic but not reversible
    this.assert(hash.length === 64, 'Hash is proper SHA-256 length');
    this.assert(/^[a-f0-9]+$/.test(hash), 'Hash is proper hex format');
    
    // Different salts should produce different hashes
    const anonymizer2 = new IPAnonymizer();
    anonymizer2.salt = 'different-salt';
    const hash2 = anonymizer2.anonymizeIP(ip);
    this.assert(hash !== hash2, 'Different salts produce different hashes');
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🚀 Starting IP Anonymization Test Suite\n');
    
    this.testBasicAnonymization();
    this.testIPValidation();
    this.testSubnetAnonymization();
    this.testIPComparison();
    this.testHeaderExtraction();
    this.testCaching();
    this.testDisplayFormatting();
    this.testPerformance();
    this.testSecurity();
    
    console.log('\n📊 Test Results Summary:');
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(`📝 Total: ${this.testResults.total}`);
    console.log(`📈 Success Rate: ${((this.testResults.passed / this.testResults.total) * 100).toFixed(1)}%`);
    
    if (this.testResults.failed === 0) {
      console.log('\n🎉 All tests passed! IP anonymization system is working correctly.');
      return true;
    } else {
      console.log('\n⚠️  Some tests failed. Please review the implementation.');
      return false;
    }
  }
}

// Main execution
async function main() {
  const tester = new IPAnonymizationTester();
  const success = await tester.runAllTests();
  process.exit(success ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { IPAnonymizationTester };
