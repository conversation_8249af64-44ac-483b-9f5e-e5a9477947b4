/**
 * Enhanced Security System for Lootlabs Integration
 * Provides comprehensive fraud prevention and security monitoring
 */
import { lootlabsAnalytics } from './lootlabsAnalytics';
class LootlabsSecurity {
  constructor() {
    this.securityConfig = {
      maxRetryAttempts: 3,
      minCompletionTime: 15000, // 15 seconds
      maxCompletionTime: 600000, // 10 minutes
      suspiciousActivityThreshold: 0.3,
      fraudDetectionEnabled: true,
      rateLimitWindow: 60000, // 1 minute
      maxRequestsPerWindow: 10
    };
    this.sessionData = new Map();
    this.requestHistory = new Map();
    this.suspiciousIPs = new Set();
    this.blockedSessions = new Set();
  }
  /**
   * Initialize security for a session
   */
  initializeSession(sessionId, campaignId) {
    const sessionSecurity = {
      sessionId,
      campaignId,
      startTime: Date.now(),
      attempts: 0,
      completedSteps: new Set(),
      securityEvents: [],
      riskScore: 0,
      isBlocked: false,
      lastActivity: Date.now(),
      userAgent: navigator.userAgent,
      ipFingerprint: this.generateIPFingerprint(),
      behaviorProfile: this.initializeBehaviorProfile()
    };
    this.sessionData.set(sessionId, sessionSecurity);
    return sessionSecurity;
  }
  /**
   * Validate step completion security
   */
  async validateStepCompletion(sessionId, step, verificationData) {
    const session = this.sessionData.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }
    // Check if session is blocked
    if (session.isBlocked || this.blockedSessions.has(sessionId)) {
      lootlabsAnalytics.trackError('blocked_session', 'Session blocked due to security violations', step);
      throw new Error('Session blocked due to security violations');
    }
    // Rate limiting check
    if (!this.checkRateLimit(sessionId)) {
      lootlabsAnalytics.trackError('rate_limit_exceeded', 'Too many requests', step);
      throw new Error('Rate limit exceeded. Please wait before trying again.');
    }
    // Timing validation
    const timingValidation = this.validateTiming(session, step, verificationData);
    if (!timingValidation.isValid) {
      this.addSecurityEvent(session, 'suspicious_timing', timingValidation);
      lootlabsAnalytics.trackError('suspicious_timing', timingValidation.reason, step);
    }
    // Behavior validation
    const behaviorValidation = this.validateBehavior(session, verificationData);
    if (!behaviorValidation.isValid) {
      this.addSecurityEvent(session, 'suspicious_behavior', behaviorValidation);
      lootlabsAnalytics.trackError('suspicious_behavior', behaviorValidation.reason, step);
    }
    // Referrer validation
    const referrerValidation = this.validateReferrer(verificationData.referrer);
    if (!referrerValidation.isValid) {
      this.addSecurityEvent(session, 'invalid_referrer', referrerValidation);
      lootlabsAnalytics.trackError('invalid_referrer', referrerValidation.reason, step);
    }
    // Calculate risk score
    const riskScore = this.calculateRiskScore(session, {
      timing: timingValidation,
      behavior: behaviorValidation,
      referrer: referrerValidation
    });
    session.riskScore = riskScore;
    session.lastActivity = Date.now();
    session.attempts++;
    // Block session if risk score is too high
    if (riskScore > 0.7) {
      session.isBlocked = true;
      this.blockedSessions.add(sessionId);
      lootlabsAnalytics.trackError('high_risk_session', `Risk score: ${riskScore}`, step);
      throw new Error('Session blocked due to high risk score');
    }
    // Mark step as completed
    session.completedSteps.add(step);
    return {
      isValid: riskScore < 0.7,
      riskScore,
      warnings: this.getSecurityWarnings(session),
      recommendations: this.getSecurityRecommendations(riskScore)
    };
  }
  /**
   * Validate timing patterns
   */
  validateTiming(session, step, verificationData) {
    const now = Date.now();
    const completionTime = verificationData.startTime 
      ? now - verificationData.startTime 
      : now - session.startTime;
    // Check minimum time
    if (completionTime < this.securityConfig.minCompletionTime) {
      return {
        isValid: false,
        reason: 'Completion too fast',
        completionTime,
        expected: `>${this.securityConfig.minCompletionTime}ms`
      };
    }
    // Check maximum time
    if (completionTime > this.securityConfig.maxCompletionTime) {
      return {
        isValid: false,
        reason: 'Completion too slow',
        completionTime,
        expected: `<${this.securityConfig.maxCompletionTime}ms`
      };
    }
    return {
      isValid: true,
      completionTime,
      reason: 'Timing validation passed'
    };
  }
  /**
   * Validate user behavior patterns
   */
  validateBehavior(session, verificationData) {
    const behavior = verificationData.userBehavior || {};
    const profile = session.behaviorProfile;
    // Update behavior profile
    profile.totalMouseMovement += behavior.mouseMovement || 0;
    profile.totalKeyboardActivity += behavior.keyboardActivity || 0;
    profile.totalScrollActivity += behavior.scrollActivity || 0;
    profile.totalFocusEvents += behavior.focusEvents || 0;
    profile.interactions++;
    // Calculate behavior score
    const behaviorScore = this.calculateBehaviorScore(behavior);
    profile.averageScore = (profile.averageScore * (profile.interactions - 1) + behaviorScore) / profile.interactions;
    // Check for suspicious patterns
    if (behaviorScore < 0.2) {
      return {
        isValid: false,
        reason: 'Insufficient user interaction',
        score: behaviorScore,
        expected: '>0.2'
      };
    }
    if (behavior.mouseMovement === 0 && behavior.keyboardActivity === 0) {
      return {
        isValid: false,
        reason: 'No user interaction detected',
        score: behaviorScore
      };
    }
    return {
      isValid: true,
      score: behaviorScore,
      reason: 'Behavior validation passed'
    };
  }
  /**
   * Validate referrer information
   */
  validateReferrer(referrer) {
    const validReferrers = [
      'lootlabs.gg',
      'creators.lootlabs.gg',
      'gateway.lootlabs.gg',
      window.location.hostname // Allow same-origin
    ];
    if (!referrer) {
      return {
        isValid: false,
        reason: 'No referrer provided',
        referrer: null
      };
    }
    const isValid = validReferrers.some(domain => 
      referrer.includes(domain) || referrer.includes(window.location.hostname)
    );
    return {
      isValid,
      reason: isValid ? 'Valid referrer' : 'Invalid referrer',
      referrer,
      validReferrers
    };
  }
  /**
   * Calculate overall risk score
   */
  calculateRiskScore(session, validations) {
    let riskScore = 0;
    // Timing risk
    if (!validations.timing.isValid) {
      riskScore += 0.3;
    }
    // Behavior risk
    if (!validations.behavior.isValid) {
      riskScore += 0.4;
    } else if (validations.behavior.score < 0.3) {
      riskScore += 0.2;
    }
    // Referrer risk
    if (!validations.referrer.isValid) {
      riskScore += 0.2;
    }
    // Session history risk
    if (session.attempts > 5) {
      riskScore += 0.1;
    }
    // Security events risk
    if (session.securityEvents.length > 3) {
      riskScore += 0.2;
    }
    return Math.min(riskScore, 1.0);
  }
  /**
   * Check rate limiting
   */
  checkRateLimit(sessionId) {
    const now = Date.now();
    const windowStart = now - this.securityConfig.rateLimitWindow;
    if (!this.requestHistory.has(sessionId)) {
      this.requestHistory.set(sessionId, []);
    }
    const requests = this.requestHistory.get(sessionId);
    // Remove old requests
    const recentRequests = requests.filter(timestamp => timestamp > windowStart);
    this.requestHistory.set(sessionId, recentRequests);
    // Check if limit exceeded
    if (recentRequests.length >= this.securityConfig.maxRequestsPerWindow) {
      return false;
    }
    // Add current request
    recentRequests.push(now);
    return true;
  }
  /**
   * Add security event to session
   */
  addSecurityEvent(session, eventType, data) {
    session.securityEvents.push({
      type: eventType,
      timestamp: Date.now(),
      data
    });
    // Keep only recent events
    if (session.securityEvents.length > 10) {
      session.securityEvents = session.securityEvents.slice(-10);
    }
  }
  /**
   * Generate IP fingerprint
   */
  generateIPFingerprint() {
    // This would typically use server-side IP detection
    // For client-side, we use available browser fingerprinting
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('IP fingerprint', 2, 2);
    return canvas.toDataURL().slice(-50); // Use last 50 chars as fingerprint
  }
  /**
   * Initialize behavior profile
   */
  initializeBehaviorProfile() {
    return {
      totalMouseMovement: 0,
      totalKeyboardActivity: 0,
      totalScrollActivity: 0,
      totalFocusEvents: 0,
      interactions: 0,
      averageScore: 0,
      patterns: []
    };
  }
  /**
   * Calculate behavior score
   */
  calculateBehaviorScore(behavior) {
    let score = 0;
    if (behavior.mouseMovement > 10) score += 0.3;
    if (behavior.keyboardActivity > 0) score += 0.2;
    if (behavior.scrollActivity > 0) score += 0.2;
    if (behavior.focusEvents > 0) score += 0.3;
    return Math.min(score, 1.0);
  }
  /**
   * Get security warnings for session
   */
  getSecurityWarnings(session) {
    const warnings = [];
    if (session.riskScore > 0.5) {
      warnings.push('High risk score detected');
    }
    if (session.attempts > 3) {
      warnings.push('Multiple completion attempts');
    }
    if (session.securityEvents.length > 2) {
      warnings.push('Multiple security events');
    }
    return warnings;
  }
  /**
   * Get security recommendations
   */
  getSecurityRecommendations(riskScore) {
    const recommendations = [];
    if (riskScore > 0.7) {
      recommendations.push('Consider additional verification steps');
      recommendations.push('Monitor session closely');
    } else if (riskScore > 0.4) {
      recommendations.push('Increase monitoring frequency');
    }
    return recommendations;
  }
  /**
   * Get session security summary
   */
  getSessionSummary(sessionId) {
    const session = this.sessionData.get(sessionId);
    if (!session) {
      return null;
    }
    return {
      sessionId,
      riskScore: session.riskScore,
      isBlocked: session.isBlocked,
      attempts: session.attempts,
      completedSteps: Array.from(session.completedSteps),
      securityEvents: session.securityEvents.length,
      warnings: this.getSecurityWarnings(session),
      behaviorProfile: session.behaviorProfile,
      sessionDuration: Date.now() - session.startTime
    };
  }
  /**
   * Block session manually
   */
  blockSession(sessionId, reason) {
    const session = this.sessionData.get(sessionId);
    if (session) {
      session.isBlocked = true;
      this.addSecurityEvent(session, 'manual_block', { reason });
    }
    this.blockedSessions.add(sessionId);
    lootlabsAnalytics.trackError('manual_block', reason, null, { sessionId });
  }
  /**
   * Unblock session
   */
  unblockSession(sessionId) {
    const session = this.sessionData.get(sessionId);
    if (session) {
      session.isBlocked = false;
      this.addSecurityEvent(session, 'manual_unblock', {});
    }
    this.blockedSessions.delete(sessionId);
  }
  /**
   * Clean up old session data
   */
  cleanup() {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    for (const [sessionId, session] of this.sessionData.entries()) {
      if (now - session.startTime > maxAge) {
        this.sessionData.delete(sessionId);
        this.requestHistory.delete(sessionId);
      }
    }
  }
  /**
   * Export security data for analysis
   */
  exportSecurityData() {
    return {
      sessions: Array.from(this.sessionData.entries()).map(([id, data]) => ({
        sessionId: id,
        ...this.getSessionSummary(id)
      })),
      blockedSessions: Array.from(this.blockedSessions),
      suspiciousIPs: Array.from(this.suspiciousIPs),
      config: this.securityConfig
    };
  }
}
// Create and export singleton instance
export const lootlabsSecurity = new LootlabsSecurity();
// Auto-cleanup every hour
if (typeof window !== 'undefined') {
  setInterval(() => {
    lootlabsSecurity.cleanup();
  }, 60 * 60 * 1000);
}
export default LootlabsSecurity;
