import CryptoJS from 'crypto-js';
// Cache canvas fingerprint to avoid creating multiple canvas elements
let cachedCanvasFingerprint = null;

const getCanvasFingerprint = () => {
  if (cachedCanvasFingerprint) return cachedCanvasFingerprint;

  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  ctx.textBaseline = 'top';
  ctx.font = '14px Arial';
  ctx.fillText('Device fingerprint canvas test 🔒', 2, 2);
  ctx.fillStyle = 'rgba(255,0,255,0.5)';
  ctx.fillRect(0, 0, 100, 100);
  cachedCanvasFingerprint = canvas.toDataURL();
  return cachedCanvasFingerprint;
};
// Cache WebGL fingerprint to avoid creating multiple contexts
let cachedWebGLFingerprint = null;

const getWebGLFingerprint = () => {
  if (cachedWebGLFingerprint) return cachedWebGLFingerprint;

  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
  if (!gl) {
    cachedWebGLFingerprint = 'no-webgl';
    return cachedWebGLFingerprint;
  }

  const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
  cachedWebGLFingerprint = {
    vendor: gl.getParameter(gl.VENDOR),
    renderer: gl.getParameter(gl.RENDERER),
    version: gl.getParameter(gl.VERSION),
    shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
    unmaskedVendor: debugInfo ? gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) : 'unknown',
    unmaskedRenderer: debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 'unknown'
  };

  // Properly dispose of the WebGL context
  const loseContext = gl.getExtension('WEBGL_lose_context');
  if (loseContext) {
    loseContext.loseContext();
  }

  return cachedWebGLFingerprint;
};
const getAudioFingerprint = () => {
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const analyser = audioContext.createAnalyser();
    const gainNode = audioContext.createGain();
    oscillator.type = 'triangle';
    oscillator.frequency.value = 10000;
    gainNode.gain.value = 0;
    oscillator.connect(analyser);
    analyser.connect(gainNode);
    gainNode.connect(audioContext.destination);
    oscillator.start(0);
    const frequencyData = new Uint8Array(analyser.frequencyBinCount);
    analyser.getByteFrequencyData(frequencyData);
    oscillator.stop();
    audioContext.close();
    return Array.from(frequencyData).join(',');
  } catch (e) {
    return 'audio-error';
  }
};
const detectVirtualEnvironment = () => {
  const vmIndicators = {
    lowCoreCount: navigator.hardwareConcurrency <= 2,
    lowMemory: navigator.deviceMemory <= 4,
    vmUserAgent: /VMware|VirtualBox|QEMU|Xen|Hyper-V|Docker|Virtual/i.test(navigator.userAgent),
    commonVMResolutions: ['1024x768', '1280x720', '800x600', '1280x800'].includes(`${screen.width}x${screen.height}`),
    automationFlags: navigator.webdriver || window.phantom || window._phantom || window.callPhantom || window.chrome?.runtime?.onConnect,
    headlessChrome: /HeadlessChrome/i.test(navigator.userAgent),
    suspiciousWebGL: (() => {
      try {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl');
        if (!gl) return true;
        const vendor = gl.getParameter(gl.VENDOR);
        return /VMware|VirtualBox|Microsoft Basic|SwiftShader|llvmpipe/i.test(vendor);
      } catch {
        return true;
      }
    })(),
    defaultLanguage: navigator.language === 'en-US' && navigator.languages.length === 1,
    suspiciousTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone === 'UTC'
  };
  const vmScore = Object.values(vmIndicators).filter(Boolean).length;
  return { isVM: vmScore >= 3, score: vmScore, indicators: vmIndicators };
};
export const getUnbreakableFingerprint = async () => {
  const components = {};
  components.gpuRenderer = await getGPUFingerprint();
  components.cpuFingerprint = getCPUFingerprint();
  components.memoryFingerprint = getMemoryFingerprint();
  components.batteryFingerprint = await getBatteryFingerprint();
  components.engineQuirks = getBrowserEngineQuirks();
  components.renderingFingerprint = await getAdvancedRenderingFingerprint();
  components.performanceFingerprint = getPerformanceFingerprint();
  components.filesystemFingerprint = await getFilesystemFingerprint();
  components.networkFingerprint = getNetworkFingerprint();
  components.securityFingerprint = getSecurityFingerprint();
  components.timingFingerprint = getTimingFingerprint();
  components.inputDeviceFingerprint = getInputDeviceFingerprint();
  components.hardwareFingerprint = getHardwareFingerprint();
  components.mediaDevicesFingerprint = await getMediaDevicesFingerprint();
  components.fontFingerprint = getFontFingerprint();
  components.pluginFingerprint = getPluginFingerprint();
  components.permissionsFingerprint = await getPermissionsFingerprint();
  components.vmDetection = detectVirtualEnvironment();
  components.behaviorFingerprint = getBehaviorFingerprint();
  components.integrityChecks = getIntegrityChecks();
  const fingerprint = await generateMultiLayerHash(components);
  return {
    fingerprint,
    components,
    confidence: calculateFingerprintConfidence(components),
    integrity: calculateIntegrityScore(components),
    vmDetection: components.vmDetection
  };
};
// Cache GPU fingerprint to avoid creating multiple contexts
let cachedGPUFingerprint = null;

const getGPUFingerprint = async () => {
  if (cachedGPUFingerprint) return cachedGPUFingerprint;

  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
  if (!gl) {
    cachedGPUFingerprint = 'no-webgl';
    return cachedGPUFingerprint;
  }

  const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
  const vendor = gl.getParameter(gl.VENDOR);
  const renderer = gl.getParameter(gl.RENDERER);
  const version = gl.getParameter(gl.VERSION);
  const shadingLangVersion = gl.getParameter(gl.SHADING_LANGUAGE_VERSION);
  const unmaskedVendor = debugInfo ? gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) : vendor;
  const unmaskedRenderer = debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : renderer;
  const maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);
  const maxVertexAttribs = gl.getParameter(gl.MAX_VERTEX_ATTRIBS);
  const maxRenderBufferSize = gl.getParameter(gl.MAX_RENDERBUFFER_SIZE);
  const extensions = gl.getSupportedExtensions();
  const parameters = getWebGLParameters(gl);

  cachedGPUFingerprint = {
    vendor, renderer, version, shadingLangVersion,
    unmaskedVendor, unmaskedRenderer,
    maxTextureSize, maxVertexAttribs, maxRenderBufferSize,
    extensions: extensions ? extensions.sort() : [],
    parameters
  };

  // Properly dispose of the WebGL context
  const loseContext = gl.getExtension('WEBGL_lose_context');
  if (loseContext) {
    loseContext.loseContext();
  }

  return cachedGPUFingerprint;
};
const getCPUFingerprint = () => {
  const start = performance.now();
  let result = 0;
  for (let i = 0; i < 100000; i++) {
    result += Math.sqrt(i) * Math.sin(i);
  }
  const calculationTime = performance.now() - start;
  return {
    hardwareConcurrency: navigator.hardwareConcurrency,
    calculationTime: Math.round(calculationTime * 1000) / 1000,
    result: result.toString().slice(0, 10)
  };
};
const getMemoryFingerprint = () => {
  return {
    deviceMemory: navigator.deviceMemory || 'unknown',
    jsHeapSizeLimit: window.performance && window.performance.memory ? window.performance.memory.jsHeapSizeLimit : 'unknown',
  };
};
const getBatteryFingerprint = async () => {
  try {
    if ('getBattery' in navigator) {
      const battery = await navigator.getBattery();
      return {
        charging: battery.charging,
        level: Math.round(battery.level * 100),
        chargingTime: battery.chargingTime,
        dischargingTime: battery.dischargingTime
      };
    }
  } catch (e) {}
  return 'no-battery-api';
};
const getBrowserEngineQuirks = () => {
  return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
    product: navigator.product,
    vendor: navigator.vendor,
    languages: navigator.languages,
      cookieEnabled: navigator.cookieEnabled,
    doNotTrack: navigator.doNotTrack
  };
};
// Cache advanced rendering fingerprint
let cachedAdvancedRenderingFingerprint = null;

const getAdvancedRenderingFingerprint = async () => {
  if (cachedAdvancedRenderingFingerprint) return cachedAdvancedRenderingFingerprint;

  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  canvas.width = 280;
  canvas.height = 60;
  const fonts = ['Arial', 'Times New Roman', 'Courier New', 'Helvetica', 'Georgia'];
  const texts = ['Security Test 🔒', 'Fingerprint ABC123', '日本語テスト'];
  let fingerprint = '';
  fonts.forEach(font => {
    texts.forEach(text => {
      ctx.font = `14px ${font}`;
      ctx.fillText(text, 10, 20);
      fingerprint += ctx.getImageData(0, 0, canvas.width, canvas.height).data.slice(0, 100).join('');
      ctx.clearRect(0, 0, canvas.width, canvas.height);
    });
  });
  cachedAdvancedRenderingFingerprint = CryptoJS.SHA256(fingerprint).toString();
  return cachedAdvancedRenderingFingerprint;
};
const getFilesystemFingerprint = async () => {
  try {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate();
      return {
        quota: estimate.quota,
        usage: estimate.usage,
        quotaRatio: estimate.usage / estimate.quota
      };
    }
  } catch (e) {}
  return 'no-storage-api';
};
const getWebGLParameters = (gl) => {
  const params = {};
  [
    'ALIASED_LINE_WIDTH_RANGE',
    'ALIASED_POINT_SIZE_RANGE',
    'MAX_COMBINED_TEXTURE_IMAGE_UNITS',
    'MAX_CUBE_MAP_TEXTURE_SIZE',
    'MAX_FRAGMENT_UNIFORM_VECTORS',
    'MAX_RENDERBUFFER_SIZE',
    'MAX_TEXTURE_IMAGE_UNITS',
    'MAX_TEXTURE_SIZE',
    'MAX_VARYING_VECTORS',
    'MAX_VERTEX_ATTRIBS',
    'MAX_VERTEX_TEXTURE_IMAGE_UNITS',
    'MAX_VERTEX_UNIFORM_VECTORS',
    'MAX_VIEWPORT_DIMS',
    'RED_BITS',
    'GREEN_BITS',
    'BLUE_BITS',
    'ALPHA_BITS',
    'DEPTH_BITS',
    'STENCIL_BITS'
  ].forEach(param => {
    try {
      params[param] = gl.getParameter(gl[param]);
    } catch (e) {}
  });
  return params;
};
const getPerformanceFingerprint = () => {
  return {
    navigation: window.performance && window.performance.navigation ? window.performance.navigation : {},
    timing: window.performance && window.performance.timing ? window.performance.timing : {},
  };
};
const getNetworkFingerprint = () => {
  return {
    connection: navigator.connection ? {
      downlink: navigator.connection.downlink,
      effectiveType: navigator.connection.effectiveType,
      rtt: navigator.connection.rtt,
      saveData: navigator.connection.saveData
    } : 'no-connection-api',
  };
};
const getSecurityFingerprint = () => {
  return {
    isSecureContext: window.isSecureContext,
    cookieStore: 'cookieStore' in window,
    serviceWorker: 'serviceWorker' in navigator,
    deviceMemory: navigator.deviceMemory || 'unknown',
  };
};
const getTimingFingerprint = () => {
    return {
    now: performance.now(),
    timeOrigin: performance.timeOrigin || 0,
    date: Date.now(),
  };
};
const getInputDeviceFingerprint = () => {
    return {
    maxTouchPoints: navigator.maxTouchPoints,
    pointerEnabled: window.PointerEvent !== undefined,
    touchEvent: 'ontouchstart' in window,
    keyboard: 'onkeydown' in window,
  };
};
const generateMultiLayerHash = async (components) => {
  const layer1 = CryptoJS.SHA256(JSON.stringify(components)).toString();
  const layer2 = CryptoJS.SHA256(layer1 + JSON.stringify(components.cpuFingerprint)).toString();
  const layer3 = CryptoJS.SHA256(layer2 + JSON.stringify(components.gpuRenderer)).toString();
  return layer3;
};
const getHardwareFingerprint = () => {
  return {
    screen: {
      width: screen.width,
      height: screen.height,
      availWidth: screen.availWidth,
      availHeight: screen.availHeight,
      colorDepth: screen.colorDepth,
      pixelDepth: screen.pixelDepth,
      orientation: screen.orientation ? screen.orientation.type : 'unknown'
    },
    devicePixelRatio: window.devicePixelRatio,
    hardwareConcurrency: navigator.hardwareConcurrency,
    maxTouchPoints: navigator.maxTouchPoints,
    deviceMemory: navigator.deviceMemory || 'unknown'
  };
};
const getMediaDevicesFingerprint = async () => {
  try {
    if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return {
        audioInputCount: devices.filter(d => d.kind === 'audioinput').length,
        audioOutputCount: devices.filter(d => d.kind === 'audiooutput').length,
        videoInputCount: devices.filter(d => d.kind === 'videoinput').length,
        deviceIds: devices.map(d => d.deviceId.slice(0, 8)).sort()
      };
    }
  } catch (e) {}
  return 'no-media-devices-api';
};
const getFontFingerprint = () => {
  const testFonts = [
    'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana',
    'Georgia', 'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS',
    'Trebuchet MS', 'Arial Black', 'Impact', 'Lucida Console',
    'Tahoma', 'Geneva', 'Lucida Sans Unicode', 'Franklin Gothic Medium',
    'Arial Narrow', 'Brush Script MT', 'Lucida Bright', 'Copperplate'
  ];
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  const testString = 'mmmmmmmmmmlli';
  const testSize = '72px';
  const baseline = ctx.measureText(testString).width;
  const availableFonts = testFonts.filter(font => {
    ctx.font = `${testSize} ${font}, monospace`;
    return ctx.measureText(testString).width !== baseline;
  });
  return {
    availableFonts: availableFonts.sort(),
    fontCount: availableFonts.length
  };
};
const getPluginFingerprint = () => {
  const plugins = Array.from(navigator.plugins).map(plugin => ({
    name: plugin.name,
    filename: plugin.filename,
    description: plugin.description,
    mimeTypes: Array.from(plugin).map(mt => mt.type).sort()
  }));
  return {
    plugins: plugins.sort((a, b) => a.name.localeCompare(b.name)),
    pluginCount: plugins.length,
    mimeTypes: Array.from(navigator.mimeTypes).map(mt => mt.type).sort()
  };
};
const getPermissionsFingerprint = async () => {
  const permissions = ['camera', 'microphone', 'geolocation', 'notifications', 'persistent-storage'];
  const results = {};
  for (const permission of permissions) {
    try {
      if (navigator.permissions && navigator.permissions.query) {
        const result = await navigator.permissions.query({ name: permission });
        results[permission] = result.state;
      }
    } catch (e) {
      results[permission] = 'unknown';
    }
  }
  return results;
};
const getBehaviorFingerprint = () => {
  return {
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    timezoneOffset: new Date().getTimezoneOffset(),
    language: navigator.language,
    languages: navigator.languages,
    platform: navigator.platform,
    userAgent: navigator.userAgent,
    cookieEnabled: navigator.cookieEnabled,
    doNotTrack: navigator.doNotTrack,
    onLine: navigator.onLine
  };
};
const getIntegrityChecks = () => {
  return {
    webdriver: navigator.webdriver || false,
    phantom: !!(window.phantom || window._phantom || window.callPhantom),
    selenium: !!(window._selenium || window.__selenium_unwrapped || window.__selenium_evaluate),
    webdriverActive: !!(window.chrome && window.chrome.runtime && window.chrome.runtime.onConnect),
    headlessChrome: /HeadlessChrome/i.test(navigator.userAgent),
    automationFlags: !!(window.domAutomation || window.domAutomationController),
    suspiciousGlobals: !!(window.Buffer || window.emit || window.spawn)
  };
};
const calculateIntegrityScore = (components) => {
  let score = 1.0;
  const integrity = components.integrityChecks;
  if (integrity.webdriver) score -= 0.3;
  if (integrity.phantom) score -= 0.3;
  if (integrity.selenium) score -= 0.3;
  if (integrity.webdriverActive) score -= 0.2;
  if (integrity.headlessChrome) score -= 0.2;
  if (integrity.automationFlags) score -= 0.2;
  if (integrity.suspiciousGlobals) score -= 0.1;
  const vm = components.vmDetection;
  if (vm && vm.isVM) score -= 0.2;
  return Math.max(score, 0);
};
const calculateFingerprintConfidence = (components) => {
  let score = 1.0;
  if (!components.gpuRenderer || components.gpuRenderer === 'no-webgl') score -= 0.15;
  if (!components.cpuFingerprint) score -= 0.1;
  if (!components.memoryFingerprint) score -= 0.1;
  if (!components.batteryFingerprint || components.batteryFingerprint === 'no-battery-api') score -= 0.05;
  if (!components.renderingFingerprint) score -= 0.1;
  if (!components.filesystemFingerprint || components.filesystemFingerprint === 'no-storage-api') score -= 0.05;
  if (!components.networkFingerprint) score -= 0.05;
  if (!components.securityFingerprint) score -= 0.05;
  if (!components.timingFingerprint) score -= 0.05;
  if (!components.inputDeviceFingerprint) score -= 0.05;
  if (!components.hardwareFingerprint) score -= 0.05;
  if (!components.mediaDevicesFingerprint || components.mediaDevicesFingerprint === 'no-media-devices-api') score -= 0.03;
  if (!components.fontFingerprint) score -= 0.05;
  if (!components.pluginFingerprint) score -= 0.03;
  if (!components.permissionsFingerprint) score -= 0.02;
  return Math.max(score, 0);
};