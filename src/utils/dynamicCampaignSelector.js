/**
 * Dynamic Campaign Selector
 * Intelligently selects the best Lootlabs campaign based on user characteristics
 */
import { lootlabsAnalytics } from './lootlabsAnalytics';
class DynamicCampaignSelector {
  constructor() {
    this.selectionHistory = new Map();
    this.performanceData = new Map();
    this.userProfiles = new Map();
  }
  /**
   * Select the best campaign for a user
   */
  async selectOptimalCampaign(campaigns, userCharacteristics = {}) {
    if (!campaigns || campaigns.length === 0) {
      throw new Error('No campaigns available');
    }
    if (campaigns.length === 1) {
      return campaigns[0];
    }
    try {
      // Analyze user characteristics
      const userProfile = this.analyzeUserProfile(userCharacteristics);
      // Score each campaign
      const scoredCampaigns = await Promise.all(
        campaigns.map(campaign => this.scoreCampaign(campaign, userProfile))
      );
      // Sort by score (highest first)
      scoredCampaigns.sort((a, b) => b.score - a.score);
      // Apply randomization to prevent always selecting the same campaign
      const selectedCampaign = this.applyRandomization(scoredCampaigns);
      // Track selection
      this.trackCampaignSelection(selectedCampaign, userProfile);
      return selectedCampaign.campaign;
    } catch (error) {
      // Fallback to first campaign
      return campaigns[0];
    }
  }
  /**
   * Analyze user profile from characteristics
   */
  analyzeUserProfile(characteristics) {
    const {
      location = null,
      deviceType = 'desktop',
      userAgent = '',
      timeZone = null,
      language = 'en',
      sessionHistory = [],
      previousConversions = 0,
      averageSessionDuration = 0
    } = characteristics;
    return {
      location,
      deviceType,
      userAgent,
      timeZone,
      language,
      sessionHistory,
      previousConversions,
      averageSessionDuration,
      isReturningUser: sessionHistory.length > 0,
      conversionRate: sessionHistory.length > 0 ? previousConversions / sessionHistory.length : 0,
      riskLevel: this.calculateUserRiskLevel(characteristics),
      preferredTier: this.getPreferredTier(characteristics),
      optimalTaskCount: this.getOptimalTaskCount(characteristics)
    };
  }
  /**
   * Score a campaign for a specific user profile
   */
  async scoreCampaign(campaign, userProfile) {
    let score = 0;
    const factors = {};
    // Location-based scoring
    const locationScore = this.scoreByLocation(campaign, userProfile.location);
    score += locationScore * 0.25;
    factors.location = locationScore;
    // Device type scoring
    const deviceScore = this.scoreByDevice(campaign, userProfile.deviceType);
    score += deviceScore * 0.20;
    factors.device = deviceScore;
    // Tier preference scoring
    const tierScore = this.scoreByTier(campaign, userProfile.preferredTier);
    score += tierScore * 0.20;
    factors.tier = tierScore;
    // Task count scoring
    const taskScore = this.scoreByTaskCount(campaign, userProfile.optimalTaskCount);
    score += taskScore * 0.15;
    factors.task = taskScore;
    // Historical performance scoring
    const performanceScore = await this.scoreByPerformance(campaign, userProfile);
    score += performanceScore * 0.15;
    factors.performance = performanceScore;
    // User risk level scoring
    const riskScore = this.scoreByRiskLevel(campaign, userProfile.riskLevel);
    score += riskScore * 0.05;
    factors.risk = riskScore;
    return {
      campaign,
      score: Math.max(0, Math.min(1, score)), // Normalize to 0-1
      factors
    };
  }
  /**
   * Score campaign by user location
   */
  scoreByLocation(campaign, location) {
    if (!location) return 0.5; // Neutral score for unknown location
    // High-value regions prefer higher tiers
    const highValueRegions = ['US', 'CA', 'GB', 'AU', 'DE', 'FR', 'NL', 'SE'];
    const mediumValueRegions = ['ES', 'IT', 'JP', 'KR', 'SG', 'HK'];
    if (highValueRegions.includes(location.toUpperCase())) {
      return campaign.tier_id >= 2 ? 0.9 : 0.6;
    }
    if (mediumValueRegions.includes(location.toUpperCase())) {
      return campaign.tier_id === 2 ? 0.8 : 0.7;
    }
    // Lower-value regions prefer tier 1
    return campaign.tier_id === 1 ? 0.8 : 0.5;
  }
  /**
   * Score campaign by device type
   */
  scoreByDevice(campaign, deviceType) {
    // Mobile users typically have better conversion with higher tiers
    if (deviceType === 'mobile') {
      return campaign.tier_id >= 2 ? 0.9 : 0.6;
    }
    // Desktop users are more tolerant of multiple tasks
    if (deviceType === 'desktop') {
      return campaign.number_of_tasks <= 3 ? 0.8 : 0.6;
    }
    // Tablet users prefer medium complexity
    if (deviceType === 'tablet') {
      return campaign.tier_id === 2 && campaign.number_of_tasks <= 2 ? 0.9 : 0.7;
    }
    return 0.5; // Default score
  }
  /**
   * Score campaign by tier preference
   */
  scoreByTier(campaign, preferredTier) {
    if (campaign.tier_id === preferredTier) {
      return 1.0;
    }
    // Penalize distance from preferred tier
    const distance = Math.abs(campaign.tier_id - preferredTier);
    return Math.max(0.3, 1.0 - (distance * 0.3));
  }
  /**
   * Score campaign by task count
   */
  scoreByTaskCount(campaign, optimalTaskCount) {
    if (campaign.number_of_tasks === optimalTaskCount) {
      return 1.0;
    }
    // Penalize distance from optimal task count
    const distance = Math.abs(campaign.number_of_tasks - optimalTaskCount);
    return Math.max(0.3, 1.0 - (distance * 0.2));
  }
  /**
   * Score campaign by historical performance
   */
  async scoreByPerformance(campaign, userProfile) {
    try {
      // Get performance data for this campaign
      const performanceKey = `${campaign.id}_${userProfile.location}_${userProfile.deviceType}`;
      let performance = this.performanceData.get(performanceKey);
      if (!performance) {
        // Fetch performance data from analytics
        performance = await this.fetchCampaignPerformance(campaign.id, {
          location: userProfile.location,
          deviceType: userProfile.deviceType
        });
        this.performanceData.set(performanceKey, performance);
      }
      // Score based on conversion rate and completion rate
      const conversionScore = performance.conversionRate || 0.5;
      const completionScore = performance.completionRate || 0.5;
      return (conversionScore + completionScore) / 2;
    } catch (error) {
      return 0.5; // Default score
    }
  }
  /**
   * Score campaign by user risk level
   */
  scoreByRiskLevel(campaign, riskLevel) {
    // High-risk users get simpler campaigns
    if (riskLevel > 0.7) {
      return campaign.tier_id === 1 && campaign.number_of_tasks === 1 ? 1.0 : 0.3;
    }
    // Medium-risk users get medium complexity
    if (riskLevel > 0.4) {
      return campaign.tier_id <= 2 && campaign.number_of_tasks <= 2 ? 0.8 : 0.6;
    }
    // Low-risk users can handle any campaign
    return 0.8;
  }
  /**
   * Apply randomization to prevent always selecting the same campaign
   */
  applyRandomization(scoredCampaigns) {
    // Use weighted random selection based on scores
    const totalScore = scoredCampaigns.reduce((sum, item) => sum + item.score, 0);
    if (totalScore === 0) {
      // If all scores are 0, select randomly
      return scoredCampaigns[Math.floor(Math.random() * scoredCampaigns.length)];
    }
    // Weighted random selection
    let random = Math.random() * totalScore;
    for (const item of scoredCampaigns) {
      random -= item.score;
      if (random <= 0) {
        return item;
      }
    }
    // Fallback to first campaign
    return scoredCampaigns[0];
  }
  /**
   * Calculate user risk level
   */
  calculateUserRiskLevel(characteristics) {
    let riskScore = 0;
    // New users are slightly riskier
    if (characteristics.sessionHistory.length === 0) {
      riskScore += 0.2;
    }
    // Low conversion rate increases risk
    const conversionRate = characteristics.previousConversions / Math.max(1, characteristics.sessionHistory.length);
    if (conversionRate < 0.3) {
      riskScore += 0.3;
    }
    // Very short sessions increase risk
    if (characteristics.averageSessionDuration < 30000) { // 30 seconds
      riskScore += 0.2;
    }
    // Suspicious user agent patterns
    if (characteristics.userAgent.includes('bot') || characteristics.userAgent.includes('crawler')) {
      riskScore += 0.5;
    }
    return Math.min(1.0, riskScore);
  }
  /**
   * Get preferred tier for user
   */
  getPreferredTier(characteristics) {
    // High-value locations prefer higher tiers
    const highValueRegions = ['US', 'CA', 'GB', 'AU', 'DE'];
    if (characteristics.location && highValueRegions.includes(characteristics.location.toUpperCase())) {
      return 3;
    }
    // Mobile users prefer higher tiers
    if (characteristics.deviceType === 'mobile') {
      return 2;
    }
    return 2; // Default tier
  }
  /**
   * Get optimal task count for user
   */
  getOptimalTaskCount(characteristics) {
    // Mobile users prefer fewer tasks
    if (characteristics.deviceType === 'mobile') {
      return 1;
    }
    // New users prefer fewer tasks
    if (characteristics.sessionHistory.length === 0) {
      return 1;
    }
    // Users with good conversion history can handle more tasks
    const conversionRate = characteristics.previousConversions / Math.max(1, characteristics.sessionHistory.length);
    if (conversionRate > 0.7) {
      return 3;
    }
    return 2; // Default task count
  }
  /**
   * Fetch campaign performance data
   */
  async fetchCampaignPerformance(campaignId, filters = {}) {
    try {
      const analytics = await lootlabsAnalytics.getAnalytics('7d', campaignId);
      // Calculate performance metrics
      const campaignData = analytics.campaigns?.find(c => c.id === campaignId);
      if (!campaignData) {
        return { conversionRate: 0.5, completionRate: 0.5 };
      }
      const conversionRate = parseFloat(campaignData.conversionRate) / 100 || 0.5;
      const completionRate = campaignData.clicks > 0 
        ? campaignData.completions / campaignData.clicks 
        : 0.5;
      return { conversionRate, completionRate };
    } catch (error) {
      return { conversionRate: 0.5, completionRate: 0.5 };
    }
  }
  /**
   * Track campaign selection for learning
   */
  trackCampaignSelection(selectedCampaign, userProfile) {
    const selectionKey = `${userProfile.location}_${userProfile.deviceType}`;
    if (!this.selectionHistory.has(selectionKey)) {
      this.selectionHistory.set(selectionKey, []);
    }
    this.selectionHistory.get(selectionKey).push({
      campaignId: selectedCampaign.campaign.id,
      score: selectedCampaign.score,
      factors: selectedCampaign.factors,
      timestamp: Date.now(),
      userProfile
    });
    // Keep only recent selections
    const history = this.selectionHistory.get(selectionKey);
    if (history.length > 100) {
      this.selectionHistory.set(selectionKey, history.slice(-100));
    }
    // Track in analytics
    lootlabsAnalytics.trackEvent('campaign_selected', null, {
      campaignId: selectedCampaign.campaign.id,
      score: selectedCampaign.score,
      factors: selectedCampaign.factors,
      userProfile: {
        location: userProfile.location,
        deviceType: userProfile.deviceType,
        riskLevel: userProfile.riskLevel
      }
    });
  }
  /**
   * Get selection statistics
   */
  getSelectionStats() {
    const stats = {
      totalSelections: 0,
      campaignDistribution: {},
      locationDistribution: {},
      deviceDistribution: {}
    };
    for (const [key, selections] of this.selectionHistory.entries()) {
      stats.totalSelections += selections.length;
      selections.forEach(selection => {
        // Campaign distribution
        const campaignId = selection.campaignId;
        stats.campaignDistribution[campaignId] = (stats.campaignDistribution[campaignId] || 0) + 1;
        // Location distribution
        const location = selection.userProfile.location || 'unknown';
        stats.locationDistribution[location] = (stats.locationDistribution[location] || 0) + 1;
        // Device distribution
        const device = selection.userProfile.deviceType || 'unknown';
        stats.deviceDistribution[device] = (stats.deviceDistribution[device] || 0) + 1;
      });
    }
    return stats;
  }
  /**
   * Clear selection history
   */
  clearHistory() {
    this.selectionHistory.clear();
    this.performanceData.clear();
  }
}
// Create and export singleton instance
export const dynamicCampaignSelector = new DynamicCampaignSelector();
export default DynamicCampaignSelector;
