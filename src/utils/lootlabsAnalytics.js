/**
 * Lootlabs Analytics Tracking Utility
 * Provides client-side analytics tracking for Lootlabs integration
 */
class LootlabsAnalytics {
  constructor() {
    this.sessionId = null;
    this.campaignId = null;
    this.events = [];
    this.isEnabled = true;
    this.lastEventTime = 0;
    this.minEventInterval = 1000; // Minimum 1 second between events
  }
  /**
   * Initialize analytics with session and campaign data
   */
  initialize(sessionId, campaignId) {
    this.sessionId = sessionId;
    this.campaignId = campaignId;
    this.trackEvent('session_started', null, {
      userAgent: navigator.userAgent,
      timestamp: Date.now(),
      url: window.location.href
    });
  }
  /**
   * Track a Lootlabs event
   */
  async trackEvent(eventType, step = null, data = {}) {
    if (!this.isEnabled || !this.sessionId) {
      return;
    }

    // Rate limiting - prevent too frequent events
    const now = Date.now();
    if (now - this.lastEventTime < this.minEventInterval) {
      return; // Skip this event to prevent spam
    }
    this.lastEventTime = now;

    const event = {
      eventType,
      sessionId: this.sessionId,
      campaignId: this.campaignId,
      step,
      data: {
        ...data,
        timestamp: now,
        url: window.location.href,
        userAgent: navigator.userAgent
      }
    };
    // Store locally for potential retry
    this.events.push(event);
    try {
      await this.sendEvent(event);
    } catch (error) {
      // Event is already stored locally for retry
    }
  }
  /**
   * Send event to analytics endpoint
   */
  async sendEvent(event) {
    try {
      const response = await fetch('/.netlify/functions/lootlabs-analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(event)
      });
      if (!response.ok) {
        // Don't throw error for analytics failures to prevent spam
        console.warn(`Analytics request failed: ${response.status}`);
        return null;
      }
      return response.json();
    } catch (error) {
      // Silently fail analytics to prevent error spam
      console.warn('Analytics request failed:', error.message);
      return null;
    }
  }
  /**
   * Track link creation
   */
  trackLinkCreated(step, linkUrl) {
    this.trackEvent('link_created', step, {
      linkUrl,
      createdAt: Date.now()
    });
  }
  /**
   * Track link click
   */
  trackLinkClick(step, linkUrl) {
    this.trackEvent('link_click', step, {
      linkUrl,
      clickedAt: Date.now(),
      referrer: document.referrer
    });
  }
  /**
   * Track step completion
   */
  trackStepCompleted(step, verificationData = {}) {
    this.trackEvent('step_completed', step, {
      completedAt: Date.now(),
      verificationScore: verificationData.score,
      verificationDetails: verificationData.details
    });
  }
  /**
   * Track verification attempt
   */
  trackVerificationAttempt(step, verificationResult) {
    this.trackEvent('verification_attempt', step, {
      attemptedAt: Date.now(),
      score: verificationResult.score,
      verified: verificationResult.verified,
      details: verificationResult.details,
      method: verificationResult.method
    });
  }
  /**
   * Track error
   */
  trackError(errorType, errorMessage, step = null, additionalData = {}) {
    this.trackEvent('error', step, {
      type: errorType,
      message: errorMessage,
      stack: additionalData.stack,
      occurredAt: Date.now(),
      ...additionalData
    });
  }
  /**
   * Track user behavior data
   */
  trackUserBehavior(behaviorData) {
    this.trackEvent('user_behavior', null, {
      mouseMovement: behaviorData.mouseMovement,
      keyboardActivity: behaviorData.keyboardActivity,
      scrollActivity: behaviorData.scrollActivity,
      focusEvents: behaviorData.focusEvents,
      sessionDuration: Date.now() - behaviorData.startTime,
      recordedAt: Date.now()
    });
  }
  /**
   * Track campaign performance
   */
  trackCampaignPerformance(performanceData) {
    this.trackEvent('campaign_performance', null, {
      ...performanceData,
      recordedAt: Date.now()
    });
  }
  /**
   * Track retry attempts
   */
  trackRetryAttempt(step, retryCount, reason) {
    this.trackEvent('retry_attempt', step, {
      retryCount,
      reason,
      attemptedAt: Date.now()
    });
  }
  /**
   * Track timing data
   */
  trackTiming(eventName, duration, step = null) {
    this.trackEvent('timing', step, {
      eventName,
      duration,
      recordedAt: Date.now()
    });
  }
  /**
   * Get analytics data for a specific period
   */
  async getAnalytics(period = '7d', campaignId = null) {
    try {
      const params = new URLSearchParams({ period });
      if (campaignId) {
        params.append('campaignId', campaignId);
      }
      const response = await fetch(`/.netlify/functions/lootlabs-analytics?${params}`);
      if (!response.ok) {
        throw new Error(`Analytics request failed: ${response.status}`);
      }
      return response.json();
    } catch (error) {
      throw error;
    }
  }
  /**
   * Retry failed events
   */
  async retryFailedEvents() {
    const failedEvents = this.events.filter(event => !event.sent);
    for (const event of failedEvents) {
      try {
        await this.sendEvent(event);
        event.sent = true;
      } catch (error) {
      }
    }
    // Clean up sent events
    this.events = this.events.filter(event => !event.sent);
  }
  /**
   * Enable/disable analytics tracking
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
  }
  /**
   * Get current session analytics summary
   */
  getSessionSummary() {
    const sessionEvents = this.events.filter(e => e.sessionId === this.sessionId);
    return {
      sessionId: this.sessionId,
      campaignId: this.campaignId,
      totalEvents: sessionEvents.length,
      eventTypes: sessionEvents.reduce((acc, event) => {
        acc[event.eventType] = (acc[event.eventType] || 0) + 1;
        return acc;
      }, {}),
      duration: sessionEvents.length > 0 
        ? Date.now() - sessionEvents[0].data.timestamp 
        : 0,
      lastActivity: sessionEvents.length > 0 
        ? Math.max(...sessionEvents.map(e => e.data.timestamp))
        : null
    };
  }
  /**
   * Export analytics data for debugging
   */
  exportData() {
    return {
      sessionId: this.sessionId,
      campaignId: this.campaignId,
      events: this.events,
      summary: this.getSessionSummary(),
      isEnabled: this.isEnabled
    };
  }
  /**
   * Clear all stored events
   */
  clearEvents() {
    this.events = [];
  }
  /**
   * Track page visibility changes
   */
  trackVisibilityChange() {
    document.addEventListener('visibilitychange', () => {
      this.trackEvent('visibility_change', null, {
        hidden: document.hidden,
        visibilityState: document.visibilityState,
        timestamp: Date.now()
      });
    });
  }
  /**
   * Track page unload
   */
  trackPageUnload() {
    window.addEventListener('beforeunload', () => {
      // Send any pending events synchronously
      if (this.events.length > 0) {
        navigator.sendBeacon(
          '/.netlify/functions/lootlabs-analytics',
          JSON.stringify({
            eventType: 'page_unload',
            sessionId: this.sessionId,
            campaignId: this.campaignId,
            data: {
              pendingEvents: this.events.length,
              timestamp: Date.now()
            }
          })
        );
      }
    });
  }
  /**
   * Start automatic behavior tracking
   */
  startBehaviorTracking() {
    let mouseMovement = 0;
    let keyboardActivity = 0;
    let scrollActivity = 0;
    let focusEvents = 0;
    // Track mouse movement
    document.addEventListener('mousemove', () => {
      mouseMovement++;
    });
    // Track keyboard activity
    document.addEventListener('keypress', () => {
      keyboardActivity++;
    });
    // Track scroll activity
    document.addEventListener('scroll', () => {
      scrollActivity++;
    });
    // Track focus events
    window.addEventListener('focus', () => {
      focusEvents++;
    });
    window.addEventListener('blur', () => {
      focusEvents++;
    });
    // Send behavior data every 2 minutes (reduced frequency)
    setInterval(() => {
      if (mouseMovement > 0 || keyboardActivity > 0 || scrollActivity > 0 || focusEvents > 0) {
        this.trackUserBehavior({
          mouseMovement,
          keyboardActivity,
          scrollActivity,
          focusEvents,
          startTime: Date.now() - 120000 // 2 minutes
        });
        // Reset counters
        mouseMovement = 0;
        keyboardActivity = 0;
        scrollActivity = 0;
        focusEvents = 0;
      }
    }, 120000); // 2 minutes instead of 30 seconds
  }
}
// Create and export singleton instance
export const lootlabsAnalytics = new LootlabsAnalytics();
// Auto-initialize tracking features
if (typeof window !== 'undefined') {
  lootlabsAnalytics.trackVisibilityChange();
  lootlabsAnalytics.trackPageUnload();
  lootlabsAnalytics.startBehaviorTracking();
}
export default LootlabsAnalytics;
