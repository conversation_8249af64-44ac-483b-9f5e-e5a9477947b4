/**
 * IP Address Anonymization Utility
 * 
 * Provides GDPR-compliant IP address anonymization using salted SHA-256 hashing.
 * Maintains security functionality while protecting user privacy.
 * 
 * Features:
 * - Hash-based anonymization with application salt
 * - Subnet-level hashing for geographic analysis
 * - Consistent hashing for repeat offender detection
 * - Performance optimized with caching
 * - GDPR Article 4(1) compliant
 */

import CryptoJS from 'crypto-js';

class IPAnonymizer {
  constructor() {
    // Use environment variable or fallback to default salt
    // In production, this should be a strong, unique salt
    this.salt = process.env.REACT_APP_IP_ANONYMIZATION_SALT || 'MADARA-IP-SALT-2024-SECURE';
    
    // Cache for performance optimization
    this.hashCache = new Map();
    this.maxCacheSize = 1000;
    
    // Validation patterns
    this.ipv4Pattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    this.ipv6Pattern = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
  }

  /**
   * Anonymize an IP address using salted SHA-256 hashing
   * @param {string} ipAddress - The IP address to anonymize
   * @param {boolean} useCache - Whether to use caching (default: true)
   * @returns {string} - Anonymized IP hash or 'unknown' for invalid inputs
   */
  anonymizeIP(ipAddress, useCache = true) {
    // Handle edge cases
    if (!ipAddress || ipAddress === 'unknown' || ipAddress === 'localhost' || ipAddress === '127.0.0.1') {
      return 'unknown';
    }

    // Normalize IP address
    const normalizedIP = this.normalizeIP(ipAddress);
    if (!normalizedIP) {
      return 'unknown';
    }

    // Check cache first
    if (useCache && this.hashCache.has(normalizedIP)) {
      return this.hashCache.get(normalizedIP);
    }

    // Create salted hash
    const saltedIP = normalizedIP + this.salt;
    const hash = CryptoJS.SHA256(saltedIP).toString();

    // Cache the result
    if (useCache) {
      this.addToCache(normalizedIP, hash);
    }

    return hash;
  }

  /**
   * Anonymize IP address at subnet level for geographic analysis
   * @param {string} ipAddress - The IP address to anonymize
   * @param {number} subnetMask - Subnet mask bits (default: 24 for IPv4, 64 for IPv6)
   * @returns {string} - Subnet-level anonymized hash
   */
  anonymizeIPSubnet(ipAddress, subnetMask = null) {
    if (!ipAddress || ipAddress === 'unknown') {
      return 'unknown';
    }

    const normalizedIP = this.normalizeIP(ipAddress);
    if (!normalizedIP) {
      return 'unknown';
    }

    let subnetIP;
    
    if (this.isIPv4(normalizedIP)) {
      const mask = subnetMask || 24; // Default /24 for IPv4
      subnetIP = this.getIPv4Subnet(normalizedIP, mask);
    } else if (this.isIPv6(normalizedIP)) {
      const mask = subnetMask || 64; // Default /64 for IPv6
      subnetIP = this.getIPv6Subnet(normalizedIP, mask);
    } else {
      return 'unknown';
    }

    // Hash the subnet
    const saltedSubnet = subnetIP + this.salt + '_subnet';
    return CryptoJS.SHA256(saltedSubnet).toString();
  }

  /**
   * Check if two IP addresses would produce the same hash (for comparison)
   * @param {string} ip1 - First IP address
   * @param {string} ip2 - Second IP address
   * @returns {boolean} - Whether the IPs would hash to the same value
   */
  compareIPs(ip1, ip2) {
    return this.anonymizeIP(ip1) === this.anonymizeIP(ip2);
  }

  /**
   * Validate if a string is a valid IP address
   * @param {string} ipAddress - IP address to validate
   * @returns {boolean} - Whether the IP is valid
   */
  isValidIP(ipAddress) {
    if (!ipAddress || typeof ipAddress !== 'string') {
      return false;
    }
    return this.isIPv4(ipAddress) || this.isIPv6(ipAddress);
  }

  /**
   * Check if IP is IPv4
   * @param {string} ip - IP address
   * @returns {boolean}
   */
  isIPv4(ip) {
    return this.ipv4Pattern.test(ip);
  }

  /**
   * Check if IP is IPv6
   * @param {string} ip - IP address
   * @returns {boolean}
   */
  isIPv6(ip) {
    return this.ipv6Pattern.test(ip);
  }

  /**
   * Normalize IP address (trim, lowercase, etc.)
   * @param {string} ipAddress - Raw IP address
   * @returns {string|null} - Normalized IP or null if invalid
   */
  normalizeIP(ipAddress) {
    if (!ipAddress || typeof ipAddress !== 'string') {
      return null;
    }

    const trimmed = ipAddress.trim().toLowerCase();
    
    // Handle IPv6 shorthand and IPv4-mapped IPv6
    if (trimmed.includes('::ffff:') && this.isIPv4(trimmed.split('::ffff:')[1])) {
      // IPv4-mapped IPv6, extract IPv4 part
      return trimmed.split('::ffff:')[1];
    }

    // Validate the IP
    if (!this.isValidIP(trimmed)) {
      return null;
    }

    return trimmed;
  }

  /**
   * Get IPv4 subnet
   * @param {string} ip - IPv4 address
   * @param {number} mask - Subnet mask bits
   * @returns {string} - Subnet representation
   */
  getIPv4Subnet(ip, mask) {
    const parts = ip.split('.').map(Number);
    const subnetParts = [...parts];
    
    // Zero out host bits based on mask
    const hostBits = 32 - mask;
    const bytesToZero = Math.floor(hostBits / 8);
    const bitsInLastByte = hostBits % 8;
    
    // Zero complete bytes
    for (let i = 4 - bytesToZero; i < 4; i++) {
      subnetParts[i] = 0;
    }
    
    // Zero partial bits in the boundary byte
    if (bitsInLastByte > 0 && 4 - bytesToZero - 1 >= 0) {
      const byteIndex = 4 - bytesToZero - 1;
      const maskByte = 0xFF << bitsInLastByte;
      subnetParts[byteIndex] = subnetParts[byteIndex] & maskByte;
    }
    
    return subnetParts.join('.') + '/' + mask;
  }

  /**
   * Get IPv6 subnet (simplified)
   * @param {string} ip - IPv6 address
   * @param {number} mask - Subnet mask bits
   * @returns {string} - Subnet representation
   */
  getIPv6Subnet(ip, mask) {
    // Simplified IPv6 subnet calculation
    const groups = ip.split(':');
    const subnetGroups = Math.ceil(mask / 16);
    
    return groups.slice(0, subnetGroups).join(':') + '::/' + mask;
  }

  /**
   * Add hash to cache with size management
   * @param {string} ip - Original IP
   * @param {string} hash - Computed hash
   */
  addToCache(ip, hash) {
    // Manage cache size
    if (this.hashCache.size >= this.maxCacheSize) {
      // Remove oldest entries (simple FIFO)
      const firstKey = this.hashCache.keys().next().value;
      this.hashCache.delete(firstKey);
    }
    
    this.hashCache.set(ip, hash);
  }

  /**
   * Clear the hash cache
   */
  clearCache() {
    this.hashCache.clear();
  }

  /**
   * Get cache statistics
   * @returns {object} - Cache stats
   */
  getCacheStats() {
    return {
      size: this.hashCache.size,
      maxSize: this.maxCacheSize,
      hitRate: this.cacheHits / (this.cacheHits + this.cacheMisses) || 0
    };
  }

  /**
   * Create a display-friendly version of anonymized IP
   * @param {string} hash - Anonymized IP hash
   * @param {boolean} truncate - Whether to truncate for display
   * @returns {string} - Display-friendly hash
   */
  formatHashForDisplay(hash, truncate = true) {
    if (!hash || hash === 'unknown') {
      return 'Unknown';
    }
    
    if (truncate) {
      // Show first 8 and last 4 characters with ellipsis
      return `${hash.substring(0, 8)}...${hash.substring(hash.length - 4)}`;
    }
    
    return hash;
  }

  /**
   * Generate a short identifier from hash for UI purposes
   * @param {string} hash - Full hash
   * @returns {string} - Short identifier
   */
  generateShortId(hash) {
    if (!hash || hash === 'unknown') {
      return 'UNK';
    }
    
    // Use first 6 characters as short ID
    return hash.substring(0, 6).toUpperCase();
  }
}

// Create singleton instance
const ipAnonymizer = new IPAnonymizer();

// Export both the class and singleton instance
export { IPAnonymizer, ipAnonymizer };

// Export convenience functions
export const anonymizeIP = (ip) => ipAnonymizer.anonymizeIP(ip);
export const anonymizeIPSubnet = (ip, mask) => ipAnonymizer.anonymizeIPSubnet(ip, mask);
export const compareIPs = (ip1, ip2) => ipAnonymizer.compareIPs(ip1, ip2);
export const isValidIP = (ip) => ipAnonymizer.isValidIP(ip);
export const formatHashForDisplay = (hash, truncate) => ipAnonymizer.formatHashForDisplay(hash, truncate);
export const generateShortId = (hash) => ipAnonymizer.generateShortId(hash);

export default ipAnonymizer;
