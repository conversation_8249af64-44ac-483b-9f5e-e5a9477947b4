/**
 * Key Manager Utility for Project Madara
 * Handles secure storage and validation of license keys
 */

class KeyManager {
  constructor() {
    this.keyStorageKey = 'madara_key';
    this.hwidStorageKey = 'madara_hwid';
    this.keyExpiryKey = 'madara_key_expiry';
  }

  /**
   * Store a license key securely
   */
  storeKey(keyCode, expiryDate = null, hwid = null) {
    try {
      localStorage.setItem(this.keyStorageKey, keyCode);
      
      if (expiryDate) {
        localStorage.setItem(this.keyExpiryKey, expiryDate);
      }
      
      if (hwid) {
        localStorage.setItem(this.hwidStorageKey, hwid);
      } else {
        // Generate a browser-specific HWID if none provided
        const browserHwid = this.generateBrowserHWID();
        localStorage.setItem(this.hwidStorageKey, browserHwid);
      }
      
      return true;
    } catch (error) {
      console.error('Failed to store key:', error);
      return false;
    }
  }

  /**
   * Retrieve stored license key
   */
  getKey() {
    try {
      return localStorage.getItem(this.keyStorageKey);
    } catch (error) {
      console.error('Failed to retrieve key:', error);
      return null;
    }
  }

  /**
   * Get stored HWID
   */
  getHWID() {
    try {
      return localStorage.getItem(this.hwidStorageKey) || this.generateBrowserHWID();
    } catch (error) {
      console.error('Failed to retrieve HWID:', error);
      return this.generateBrowserHWID();
    }
  }

  /**
   * Check if stored key is expired
   */
  isKeyExpired() {
    try {
      const expiryDate = localStorage.getItem(this.keyExpiryKey);
      if (!expiryDate) return false; // No expiry set
      
      return new Date() > new Date(expiryDate);
    } catch (error) {
      console.error('Failed to check key expiry:', error);
      return true; // Assume expired on error
    }
  }

  /**
   * Validate key with server
   */
  async validateKey(keyCode = null) {
    const key = keyCode || this.getKey();
    const hwid = this.getHWID();
    
    if (!key) {
      return { valid: false, error: 'No key found' };
    }

    try {
      const response = await fetch('/.netlify/functions/validate-key', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          keyCode: key,
          hwid: hwid,
          action: 'validate'
        })
      });

      const data = await response.json();
      
      if (response.ok && data.valid) {
        // Update expiry if provided
        if (data.expires_at) {
          localStorage.setItem(this.keyExpiryKey, data.expires_at);
        }
        return { valid: true, data };
      } else {
        return { valid: false, error: data.error || 'Key validation failed' };
      }
    } catch (error) {
      console.error('Key validation error:', error);
      return { valid: false, error: 'Network error during validation' };
    }
  }

  /**
   * Clear stored key data
   */
  clearKey() {
    try {
      localStorage.removeItem(this.keyStorageKey);
      localStorage.removeItem(this.keyExpiryKey);
      localStorage.removeItem(this.hwidStorageKey);
      return true;
    } catch (error) {
      console.error('Failed to clear key:', error);
      return false;
    }
  }

  /**
   * Check if user has a valid key
   */
  async hasValidKey() {
    const key = this.getKey();
    if (!key) return false;
    
    if (this.isKeyExpired()) {
      this.clearKey();
      return false;
    }

    // Quick validation with server
    const validation = await this.validateKey();
    if (!validation.valid) {
      this.clearKey();
      return false;
    }

    return true;
  }

  /**
   * Generate a browser-specific HWID
   */
  generateBrowserHWID() {
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Browser fingerprint', 2, 2);
      
      const fingerprint = [
        navigator.userAgent,
        navigator.language,
        screen.width + 'x' + screen.height,
        new Date().getTimezoneOffset(),
        canvas.toDataURL(),
        navigator.hardwareConcurrency || 'unknown',
        navigator.deviceMemory || 'unknown'
      ].join('|');
      
      // Simple hash function
      let hash = 0;
      for (let i = 0; i < fingerprint.length; i++) {
        const char = fingerprint.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
      }
      
      return 'WEB_' + Math.abs(hash).toString(16).toUpperCase();
    } catch (error) {
      console.error('Failed to generate browser HWID:', error);
      return 'WEB_' + Date.now().toString(16).toUpperCase();
    }
  }

  /**
   * Get key status for UI display
   */
  async getKeyStatus() {
    const key = this.getKey();
    
    if (!key) {
      return {
        hasKey: false,
        valid: false,
        expired: false,
        message: 'No key found. Please generate a key.'
      };
    }

    if (this.isKeyExpired()) {
      return {
        hasKey: true,
        valid: false,
        expired: true,
        message: 'Key has expired. Please generate a new key.'
      };
    }

    const validation = await this.validateKey();
    
    return {
      hasKey: true,
      valid: validation.valid,
      expired: false,
      message: validation.valid ? 'Key is valid' : (validation.error || 'Key validation failed'),
      data: validation.data
    };
  }

  /**
   * Redirect to key generation if needed
   */
  async ensureValidKey() {
    const hasValid = await this.hasValidKey();
    if (!hasValid) {
      window.location.href = '/generate-key';
      return false;
    }
    return true;
  }
}

// Create singleton instance
export const keyManager = new KeyManager();

// Convenience functions
export const storeKey = (keyCode, expiryDate, hwid) => keyManager.storeKey(keyCode, expiryDate, hwid);
export const getKey = () => keyManager.getKey();
export const getHWID = () => keyManager.getHWID();
export const validateKey = (keyCode) => keyManager.validateKey(keyCode);
export const clearKey = () => keyManager.clearKey();
export const hasValidKey = () => keyManager.hasValidKey();
export const getKeyStatus = () => keyManager.getKeyStatus();
export const ensureValidKey = () => keyManager.ensureValidKey();

export default keyManager;
