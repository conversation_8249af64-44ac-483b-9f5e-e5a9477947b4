export class SecurityConfig {
  constructor() {
    this.config = {
      securityLevel: 'MAXIMUM',
      adminBypass: false,
      ownerBypass: false,
      botDetectionThreshold: 0.7,
      anomalyThreshold: 0.7,
      suspiciousThreshold: 0.7,
      enableRealTimeMonitoring: true,
      enableBehaviorLogging: false,
      enableSecurityAlerts: true,
      enableTemporaryBans: true,
      consoleAccessBanDuration: 60000,
      codeInjectionBanDuration: 10800000,
      enableDevToolsDetection: true,
      enableConsoleProtection: true,
      enableScriptInjectionProtection: true,
      enableHoneypotTraps: true,
      enablePerformanceMonitoring: true,
      logLevel: 'MINIMAL',
      enableSecurityDashboard: true,
      enableViolationReporting: true
    };
    this.adminRoles = ['admin', 'owner', 'moderator'];
    this.ownerRoles = ['owner'];
  }
  getConfig() {
    return { ...this.config };
  }
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }
  isAdminUser() {
    try {
      if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
        return false;
      }
      const adminToken = localStorage.getItem('adminToken');
      if (adminToken) {
        try {
          const tokenData = JSON.parse(atob(adminToken.split('.')[1]));
          return this.adminRoles.includes(tokenData.role);
        } catch (e) {
          return false;
        }
      }
      const userRole = sessionStorage.getItem('userRole');
      return this.adminRoles.includes(userRole);
    } catch (error) {
      return false;
    }
  }
  isOwnerUser() {
    try {
      if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
        return false;
      }
      const adminToken = localStorage.getItem('adminToken');
      if (adminToken) {
        try {
          const tokenData = JSON.parse(atob(adminToken.split('.')[1]));
          return this.ownerRoles.includes(tokenData.role);
        } catch (e) {
          return false;
        }
      }
      const userRole = sessionStorage.getItem('userRole');
      return this.ownerRoles.includes(userRole);
    } catch (error) {
      return false;
    }
  }
  shouldBypassSecurity(checkType = 'general') {
    try {
      if (!this.config) {
        return { bypass: false, reason: 'config-unavailable', userType: 'user' };
      }
      const isOwner = this.isOwnerUser();
      const isAdmin = this.isAdminUser();
      if (isOwner && this.config.ownerBypass) {
        return { bypass: true, reason: 'owner-privileges', userType: 'owner' };
      }
      if (isAdmin && this.config.adminBypass) {
        return { bypass: true, reason: 'admin-privileges', userType: 'admin' };
      }
      return { bypass: false, reason: 'no-privileges', userType: 'user' };
    } catch (error) {
      return { bypass: false, reason: 'error', userType: 'user' };
    }
  }
  getSecurityLevelConfig(level = null) {
    const levels = {
      LOW: {
        botDetectionThreshold: 0.9,
        anomalyThreshold: 0.95,
        enableRealTimeMonitoring: false,
        enableDevToolsDetection: false,
        logLevel: 'MINIMAL'
      },
      MEDIUM: {
        botDetectionThreshold: 0.8,
        anomalyThreshold: 0.85,
        enableRealTimeMonitoring: true,
        enableDevToolsDetection: true,
        logLevel: 'NORMAL'
      },
      HIGH: {
        botDetectionThreshold: 0.7,
        anomalyThreshold: 0.8,
        enableRealTimeMonitoring: true,
        enableDevToolsDetection: true,
        logLevel: 'DETAILED'
      },
      MAXIMUM: {
        botDetectionThreshold: 0.7,
        anomalyThreshold: 0.7,
        enableRealTimeMonitoring: true,
        enableDevToolsDetection: true,
        enableConsoleProtection: true,
        enableScriptInjectionProtection: true,
        enableHoneypotTraps: true,
        enablePerformanceMonitoring: true,
        logLevel: 'VERBOSE'
      }
    };
    const targetLevel = level || this.config.securityLevel;
    return levels[targetLevel] || levels.HIGH;
  }
  applySecurityLevel(level) {
    const levelConfig = this.getSecurityLevelConfig(level);
    this.updateConfig({ ...levelConfig, securityLevel: level });
  }
  logSecurityEvent(event, data = {}) {
    try {
      if (!this.config) {
        return;
      }
      const logLevels = ['MINIMAL', 'NORMAL', 'DETAILED', 'VERBOSE'];
      const currentLevelIndex = logLevels.indexOf(this.config.logLevel);
      const eventLevelIndex = logLevels.indexOf(data.level || 'NORMAL');
      if (eventLevelIndex <= currentLevelIndex) {
        const timestamp = new Date().toISOString();
        const logData = {
          timestamp,
          event,
          securityLevel: this.config.securityLevel,
          userType: this.isOwnerUser() ? 'owner' : this.isAdminUser() ? 'admin' : 'user',
          ...data
        };
        if (this.config.enableSecurityDashboard) {
          this.storeSecurityLog(logData);
        }
      }
    } catch (error) {
    }
  }
  storeSecurityLog(logData) {
    try {
      if (typeof window === 'undefined' || typeof sessionStorage === 'undefined') {
        return;
      }
      const logs = JSON.parse(sessionStorage.getItem('securityLogs') || '[]');
      logs.push(logData);
      if (logs.length > 100) {
        logs.splice(0, logs.length - 100);
      }
      sessionStorage.setItem('securityLogs', JSON.stringify(logs));
    } catch (error) {
    }
  }
  getSecurityLogs() {
    try {
      if (typeof window === 'undefined' || typeof sessionStorage === 'undefined') {
        return [];
      }
      return JSON.parse(sessionStorage.getItem('securityLogs') || '[]');
    } catch (error) {
      return [];
    }
  }
  clearSecurityLogs() {
    try {
      if (typeof window === 'undefined' || typeof sessionStorage === 'undefined') {
        return;
      }
      sessionStorage.removeItem('securityLogs');
    } catch (error) {
    }
  }
  initialize() {
    const userStatus = this.shouldBypassSecurity();
    this.logSecurityEvent('SYSTEM_INITIALIZED', {
      level: 'NORMAL',
      securityLevel: this.config.securityLevel,
      userPrivileges: userStatus
    });
  }
}
let _securityConfigInstance = null;
let _initializationPromise = null;
export function getSecurityConfig() {
  if (!_securityConfigInstance) {
    _securityConfigInstance = new SecurityConfig();
  }
  return _securityConfigInstance;
}
export function initializeSecurityConfig() {
  if (_initializationPromise) {
    return _initializationPromise;
  }
  _initializationPromise = new Promise((resolve) => {
    const instance = getSecurityConfig();
    if (typeof window !== 'undefined') {
      setTimeout(() => {
        try {
          instance.initialize();
          resolve(instance);
        } catch (error) {
          resolve(instance);
        }
      }, 0);
    } else {
      resolve(instance);
    }
  });
  return _initializationPromise;
}
const createSecurityConfigProxy = () => {
  const instance = getSecurityConfig();
  return new Proxy(instance, {
    get(target, prop) {
      if (typeof target[prop] === 'function') {
        return function(...args) {
          try {
            return target[prop].apply(target, args);
          } catch (error) {
            if (prop === 'shouldBypassSecurity') {
              return { bypass: false, reason: 'error', userType: 'user' };
            }
            if (prop === 'getConfig') {
              return {};
            }
            if (prop === 'getSecurityLogs') {
              return [];
            }
            return undefined;
          }
        };
      }
      return target[prop];
    }
  });
};
export const securityConfig = createSecurityConfigProxy();
