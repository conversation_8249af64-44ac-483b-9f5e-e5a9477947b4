let trackerInitialized = false;
class BehaviorTracker {
  constructor() {
    this.behaviorData = {
      mouseMovements: [],
      clickPatterns: [],
      typingSpeed: [],
      sessionStartTime: Date.now(),
      pageVisits: [],
      scrollBehavior: [],
      focusLost: 0,
      touchEvents: [],
      orientationChanges: [],
      deviceMotion: [],
      gestureEvents: [],
      mobileQuirks: {}
    };
    if (!trackerInitialized) {
      this.init();
      trackerInitialized = true;
    }
  }
  init() {
    if (typeof window === 'undefined') return;
    this.behaviorData.pageVisits.push({
      url: window.location.href,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      referrer: document.referrer || 'direct'
    });
    document.addEventListener('mousemove', this.trackMouseMovement.bind(this));
    document.addEventListener('click', this.trackClick.bind(this));
    document.addEventListener('keydown', this.trackKeypress.bind(this));
    window.addEventListener('scroll', this.trackScroll.bind(this));
    window.addEventListener('blur', this.trackFocusLoss.bind(this));
    window.addEventListener('focus', this.trackFocusGain.bind(this));
    document.addEventListener('visibilitychange', this.trackVisibilityChange.bind(this));
    this.initializeMobileTracking();
  }
  trackMouseMovement(event) {
    const now = Date.now();
    this.behaviorData.mouseMovements.push({
      x: event.clientX,
      y: event.clientY,
      timestamp: now
    });
    if (this.behaviorData.mouseMovements.length > 100) {
      this.behaviorData.mouseMovements.shift();
    }
  }
  trackClick(event) {
    const now = Date.now();
    this.behaviorData.clickPatterns.push({
      x: event.clientX,
      y: event.clientY,
      timestamp: now,
      target: event.target.tagName
    });
    if (this.behaviorData.clickPatterns.length > 50) {
      this.behaviorData.clickPatterns.shift();
    }
  }
  trackKeypress(event) {
    const now = Date.now();
    this.behaviorData.typingSpeed.push(now);
    if (this.behaviorData.typingSpeed.length > 50) {
      this.behaviorData.typingSpeed.shift();
    }
  }
  trackScroll(event) {
    const now = Date.now();
    this.behaviorData.scrollBehavior.push({
      scrollY: window.scrollY,
      timestamp: now
    });
    if (this.behaviorData.scrollBehavior.length > 30) {
      this.behaviorData.scrollBehavior.shift();
    }
  }
  trackFocusLoss() {
    this.behaviorData.focusLost++;
  }
  trackFocusGain() {
    if (this.behaviorData.focusLost > 10) {
      this.flagSuspiciousBehavior('excessive_focus_loss');
    }
  }
  trackVisibilityChange() {
    if (document.hidden) {
      this.behaviorData.pageVisits.push({
        type: 'hidden',
        timestamp: Date.now()
      });
    } else {
      this.behaviorData.pageVisits.push({
        type: 'visible',
        timestamp: Date.now()
      });
    }
  }
  calculateMetrics() {
    const now = Date.now();
    const sessionDuration = now - this.behaviorData.sessionStartTime;
    const mouseMetrics = this.analyzeMouseMovements();
    const clickMetrics = this.analyzeClickPatterns();
    const typingMetrics = this.analyzeTypingSpeed();
    const scrollMetrics = this.analyzeScrollBehavior();
    return {
      sessionDuration,
      mouseMetrics,
      clickMetrics,
      typingMetrics,
      scrollMetrics,
      focusLossCount: this.behaviorData.focusLost,
      anomalyScore: this.calculateAnomalyScore(mouseMetrics, clickMetrics, typingMetrics, scrollMetrics, sessionDuration, this.behaviorData.focusLost)
    };
  }
  analyzeMouseMovements() {
    const movements = this.behaviorData.mouseMovements;
    if (movements.length < 2) return { variance: 0, speed: 0, straightLine: false };
    let totalDistance = 0;
    let totalTime = 0;
    let deltaX = 0;
    let deltaY = 0;
    for (let i = 1; i < movements.length; i++) {
      const prev = movements[i - 1];
      const curr = movements[i];
      const dx = curr.x - prev.x;
      const dy = curr.y - prev.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      const time = curr.timestamp - prev.timestamp;
      totalDistance += distance;
      totalTime += time;
      deltaX += Math.abs(dx);
      deltaY += Math.abs(dy);
    }
    const avgSpeed = totalTime > 0 ? totalDistance / totalTime : 0;
    const straightLine = (deltaX + deltaY) > 0 ? totalDistance / (deltaX + deltaY) > 0.9 : false;
    return {
      variance: this.calculateVariance(movements.map(m => ({ x: m.x, y: m.y }))),
      speed: avgSpeed,
      straightLine: straightLine // Suspicious if too many straight line movements
    };
  }
  analyzeClickPatterns() {
    const clicks = this.behaviorData.clickPatterns;
    if (clicks.length < 2) return { interval: 0, precision: 0 };
    const intervals = [];
    for (let i = 1; i < clicks.length; i++) {
      intervals.push(clicks[i].timestamp - clicks[i - 1].timestamp);
    }
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const intervalVariance = this.calculateVariance(intervals);
    return {
      interval: avgInterval,
      variance: intervalVariance,
      tooRegular: intervalVariance < 100 // Suspicious if clicks are too regular
    };
  }
  analyzeTypingSpeed() {
    const keypresses = this.behaviorData.typingSpeed;
    if (keypresses.length < 2) return { wpm: 0, variance: 0 };
    const intervals = [];
    for (let i = 1; i < keypresses.length; i++) {
      intervals.push(keypresses[i] - keypresses[i - 1]);
    }
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const wpm = avgInterval > 0 ? (60000 / avgInterval) / 5 : 0; // Rough WPM calculation
    return {
      wpm: wpm,
      variance: this.calculateVariance(intervals),
      tooFast: wpm > 200, // Suspicious if typing too fast
      tooRegular: this.calculateVariance(intervals) < 50
    };
  }
  analyzeScrollBehavior() {
    const scrolls = this.behaviorData.scrollBehavior;
    if (scrolls.length < 2) return { speed: 0, variance: 0 };
    const speeds = [];
    for (let i = 1; i < scrolls.length; i++) {
      const prev = scrolls[i - 1];
      const curr = scrolls[i];
      const distance = Math.abs(curr.scrollY - prev.scrollY);
      const time = curr.timestamp - prev.timestamp;
      speeds.push(time > 0 ? distance / time : 0);
    }
    const avgSpeed = speeds.reduce((a, b) => a + b, 0) / speeds.length;
    return {
      speed: avgSpeed,
      variance: this.calculateVariance(speeds),
      tooFast: avgSpeed > 10 // Suspicious if scrolling too fast
    };
  }
  calculateVariance(data) {
    if (data.length === 0) return 0;
    const mean = data.reduce((a, b) => {
      if (typeof b === 'object') {
        return a + Math.sqrt(b.x * b.x + b.y * b.y);
      }
      return a + b;
    }, 0) / data.length;
    const variance = data.reduce((a, b) => {
      const value = typeof b === 'object' ? Math.sqrt(b.x * b.x + b.y * b.y) : b;
      return a + Math.pow(value - mean, 2);
    }, 0) / data.length;
    return variance;
  }
  calculateAnomalyScore(mouseMetrics, clickMetrics, typingMetrics, scrollMetrics, sessionDuration, focusLossCount) {
    let score = 0;
    if (mouseMetrics.straightLine) score += 0.25;
    if (mouseMetrics.perfectCircles) score += 0.2;
    if (mouseMetrics.identicalPatterns) score += 0.3;
    if (mouseMetrics.impossibleSpeed) score += 0.4;
    if (mouseMetrics.noVariation) score += 0.2;
    if (clickMetrics.tooRegular) score += 0.2;
    if (clickMetrics.perfectTiming) score += 0.25;
    if (clickMetrics.impossiblePrecision) score += 0.3;
    if (clickMetrics.noHumanDelay) score += 0.2;
    if (typingMetrics.tooFast) score += 0.2;
    if (typingMetrics.tooRegular) score += 0.15;
    if (typingMetrics.impossibleConsistency) score += 0.3;
    if (typingMetrics.noTypos) score += 0.1;
    if (scrollMetrics.tooFast) score += 0.1;
    if (scrollMetrics.perfectPixels) score += 0.2;
    if (scrollMetrics.noInertia) score += 0.15;
    if (sessionDuration < 5000) score += 0.4; // Extremely fast completion
    if (sessionDuration < 10000) score += 0.2; // Very fast completion
    if (focusLossCount > 10) score += 0.2;
    if (focusLossCount === 0 && sessionDuration > 30000) score += 0.1; // No focus loss in long session
    const advancedPatterns = this.detectAdvancedPatterns();
    score += advancedPatterns.automationScore;
    return Math.min(score, 1.0);
  }
  flagSuspiciousBehavior(reason) {
    if (!this.behaviorData.suspiciousFlags) {
      this.behaviorData.suspiciousFlags = {};
    }
    this.behaviorData.suspiciousFlags[reason] = true;
    this.behaviorData.suspiciousCount = (this.behaviorData.suspiciousCount || 0) + 1;
  }
  detectAdvancedPatterns() {
    let automationScore = 0;
    const patterns = {
      mouseAutomation: false,
      clickAutomation: false,
      typingAutomation: false,
      scrollAutomation: false,
      timingAutomation: false
    };
    const mousePatterns = this.analyzeMouseAutomation();
    if (mousePatterns.isAutomated) {
      patterns.mouseAutomation = true;
      automationScore += 0.3;
    }
    const clickPatterns = this.analyzeClickAutomation();
    if (clickPatterns.isAutomated) {
      patterns.clickAutomation = true;
      automationScore += 0.25;
    }
    const typingPatterns = this.analyzeTypingAutomation();
    if (typingPatterns.isAutomated) {
      patterns.typingAutomation = true;
      automationScore += 0.2;
    }
    const scrollPatterns = this.analyzeScrollAutomation();
    if (scrollPatterns.isAutomated) {
      patterns.scrollAutomation = true;
      automationScore += 0.15;
    }
    const timingPatterns = this.analyzeTimingAutomation();
    if (timingPatterns.isAutomated) {
      patterns.timingAutomation = true;
      automationScore += 0.2;
    }
    return {
      automationScore: Math.min(automationScore, 1.0),
      patterns,
      confidence: this.calculatePatternConfidence(patterns)
    };
  }
  analyzeMouseAutomation() {
    const movements = this.behaviorData.mouseMovements;
    if (!movements || movements.length < 10) {
      return { isAutomated: false, confidence: 0 };
    }
    let suspiciousIndicators = 0;
    const totalMovements = movements.length;
    let straightLineCount = 0;
    for (let i = 2; i < movements.length; i++) {
      const prev = movements[i-2];
      const curr = movements[i-1];
      const next = movements[i];
      const slope1 = (curr.y - prev.y) / (curr.x - prev.x);
      const slope2 = (next.y - curr.y) / (next.x - curr.x);
      if (Math.abs(slope1 - slope2) < 0.01) {
        straightLineCount++;
      }
    }
    if (straightLineCount / totalMovements > 0.7) {
      suspiciousIndicators++;
    }
    const patterns = this.findRepeatingPatterns(movements);
    if (patterns.repeatingCount > 3) {
      suspiciousIndicators++;
    }
    let impossibleSpeedCount = 0;
    for (let i = 1; i < movements.length; i++) {
      const prev = movements[i-1];
      const curr = movements[i];
      const distance = Math.sqrt(Math.pow(curr.x - prev.x, 2) + Math.pow(curr.y - prev.y, 2));
      const timeDiff = curr.timestamp - prev.timestamp;
      const speed = distance / timeDiff; // pixels per ms
      if (speed > 5) { // Impossibly fast movement
        impossibleSpeedCount++;
      }
    }
    if (impossibleSpeedCount / totalMovements > 0.1) {
      suspiciousIndicators++;
    }
    return {
      isAutomated: suspiciousIndicators >= 2,
      confidence: suspiciousIndicators / 3,
      indicators: {
        straightLines: straightLineCount / totalMovements,
        repeatingPatterns: patterns.repeatingCount,
        impossibleSpeeds: impossibleSpeedCount / totalMovements
      }
    };
  }
  analyzeClickAutomation() {
    const clicks = this.behaviorData.clickPatterns;
    if (!clicks || clicks.length < 5) {
      return { isAutomated: false, confidence: 0 };
    }
    let suspiciousIndicators = 0;
    const intervals = [];
    for (let i = 1; i < clicks.length; i++) {
      intervals.push(clicks[i].timestamp - clicks[i-1].timestamp);
    }
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
    const stdDev = Math.sqrt(variance);
    if (stdDev < avgInterval * 0.1) {
      suspiciousIndicators++;
    }
    let precisionCount = 0;
    for (let i = 1; i < clicks.length; i++) {
      const prev = clicks[i-1];
      const curr = clicks[i];
      if (prev.x === curr.x && prev.y === curr.y) {
        precisionCount++;
      }
    }
    if (precisionCount / clicks.length > 0.5) {
      suspiciousIndicators++;
    }
    return {
      isAutomated: suspiciousIndicators >= 1,
      confidence: suspiciousIndicators / 2,
      indicators: {
        timingVariance: stdDev / avgInterval,
        pixelPrecision: precisionCount / clicks.length
      }
    };
  }
  analyzeTypingAutomation() {
    const typing = this.behaviorData.typingSpeed;
    if (!typing || typing.length < 10) {
      return { isAutomated: false, confidence: 0 };
    }
    let suspiciousIndicators = 0;
    const intervals = [];
    for (let i = 1; i < typing.length; i++) {
      intervals.push(typing[i] - typing[i-1]);
    }
    if (intervals.length > 0) {
      const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
      const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
      const stdDev = Math.sqrt(variance);
      if (stdDev < 10) { // Less than 10ms variation
        suspiciousIndicators++;
      }
      if (avgInterval < 50) { // Less than 50ms between keystrokes
        suspiciousIndicators++;
      }
    }
    return {
      isAutomated: suspiciousIndicators >= 1,
      confidence: suspiciousIndicators / 2
    };
  }
  analyzeScrollAutomation() {
    const scrolls = this.behaviorData.scrollBehavior;
    if (!scrolls || scrolls.length < 5) {
      return { isAutomated: false, confidence: 0 };
    }
    let suspiciousIndicators = 0;
    let perfectIncrements = 0;
    for (let i = 1; i < scrolls.length; i++) {
      const diff = Math.abs(scrolls[i].scrollY - scrolls[i-1].scrollY);
      if (diff > 0 && diff % 10 === 0) { // Perfect 10-pixel increments
        perfectIncrements++;
      }
    }
    if (perfectIncrements / scrolls.length > 0.8) {
      suspiciousIndicators++;
    }
    return {
      isAutomated: suspiciousIndicators >= 1,
      confidence: suspiciousIndicators
    };
  }
  analyzeTimingAutomation() {
    const now = Date.now();
    const sessionStart = this.behaviorData.sessionStart || now;
    const sessionDuration = now - sessionStart;
    let suspiciousIndicators = 0;
    if (sessionDuration < 3000) { // Less than 3 seconds
      suspiciousIndicators += 2;
    } else if (sessionDuration < 8000) { // Less than 8 seconds
      suspiciousIndicators += 1;
    }
    const activities = [
      ...this.behaviorData.mouseMovements,
      ...this.behaviorData.clickPatterns,
      ...this.behaviorData.scrollBehavior
    ].sort((a, b) => a.timestamp - b.timestamp);
    let longPauses = 0;
    for (let i = 1; i < activities.length; i++) {
      const gap = activities[i].timestamp - activities[i-1].timestamp;
      if (gap > 1000) { // Pause longer than 1 second
        longPauses++;
      }
    }
    if (longPauses === 0 && activities.length > 20) {
      suspiciousIndicators++;
    }
    return {
      isAutomated: suspiciousIndicators >= 2,
      confidence: Math.min(suspiciousIndicators / 3, 1.0)
    };
  }
  findRepeatingPatterns(movements) {
    let repeatingCount = 0;
    const patternLength = 5;
    for (let i = 0; i <= movements.length - patternLength * 2; i++) {
      const pattern1 = movements.slice(i, i + patternLength);
      const pattern2 = movements.slice(i + patternLength, i + patternLength * 2);
      let matches = 0;
      for (let j = 0; j < patternLength; j++) {
        const diff = Math.sqrt(
          Math.pow(pattern1[j].x - pattern2[j].x, 2) +
          Math.pow(pattern1[j].y - pattern2[j].y, 2)
        );
        if (diff < 5) { // Within 5 pixels
          matches++;
        }
      }
      if (matches >= patternLength * 0.8) {
        repeatingCount++;
      }
    }
    return { repeatingCount };
  }
  calculatePatternConfidence(patterns) {
    const patternCount = Object.values(patterns).filter(Boolean).length;
    return Math.min(patternCount / 5, 1.0);
  }
  initializeMobileTracking() {
    if (typeof window === 'undefined') return;
    const isMobile = this.isMobileDevice();
    if (isMobile) {
      document.addEventListener('touchstart', this.trackTouchStart.bind(this));
      document.addEventListener('touchmove', this.trackTouchMove.bind(this));
      document.addEventListener('touchend', this.trackTouchEnd.bind(this));
      window.addEventListener('orientationchange', this.trackOrientationChange.bind(this));
      if (window.DeviceMotionEvent) {
        window.addEventListener('devicemotion', this.trackDeviceMotion.bind(this));
      }
      document.addEventListener('gesturestart', this.trackGestureStart.bind(this));
      document.addEventListener('gesturechange', this.trackGestureChange.bind(this));
      document.addEventListener('gestureend', this.trackGestureEnd.bind(this));
      this.detectMobileQuirks();
    }
  }
  isMobileDevice() {
    const userAgent = navigator.userAgent || '';
    const isMobileUA = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS/i.test(userAgent);
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const isSmallScreen = window.screen.width <= 768 || window.screen.height <= 768;
    return isMobileUA || (isTouchDevice && isSmallScreen);
  }
  trackTouchStart(event) {
    const now = Date.now();
    const touch = event.touches[0];
    this.behaviorData.touchEvents.push({
      type: 'touchstart',
      x: touch.clientX,
      y: touch.clientY,
      timestamp: now,
      touchCount: event.touches.length
    });
    if (this.behaviorData.touchEvents.length > 50) {
      this.behaviorData.touchEvents.shift();
    }
  }
  trackTouchMove(event) {
    const now = Date.now();
    const touch = event.touches[0];
    this.behaviorData.touchEvents.push({
      type: 'touchmove',
      x: touch.clientX,
      y: touch.clientY,
      timestamp: now,
      touchCount: event.touches.length
    });
    if (this.behaviorData.touchEvents.length > 50) {
      this.behaviorData.touchEvents.shift();
    }
  }
  trackTouchEnd(event) {
    const now = Date.now();
    this.behaviorData.touchEvents.push({
      type: 'touchend',
      timestamp: now,
      touchCount: event.changedTouches.length
    });
    if (this.behaviorData.touchEvents.length > 50) {
      this.behaviorData.touchEvents.shift();
    }
  }
  trackOrientationChange(event) {
    const now = Date.now();
    this.behaviorData.orientationChanges.push({
      orientation: window.orientation || screen.orientation?.angle || 0,
      timestamp: now,
      innerWidth: window.innerWidth,
      innerHeight: window.innerHeight
    });
    if (this.behaviorData.orientationChanges.length > 20) {
      this.behaviorData.orientationChanges.shift();
    }
  }
  trackDeviceMotion(event) {
    const now = Date.now();
    this.behaviorData.deviceMotion.push({
      acceleration: {
        x: event.acceleration?.x || 0,
        y: event.acceleration?.y || 0,
        z: event.acceleration?.z || 0
      },
      accelerationIncludingGravity: {
        x: event.accelerationIncludingGravity?.x || 0,
        y: event.accelerationIncludingGravity?.y || 0,
        z: event.accelerationIncludingGravity?.z || 0
      },
      rotationRate: {
        alpha: event.rotationRate?.alpha || 0,
        beta: event.rotationRate?.beta || 0,
        gamma: event.rotationRate?.gamma || 0
      },
      timestamp: now
    });
    if (this.behaviorData.deviceMotion.length > 30) {
      this.behaviorData.deviceMotion.shift();
    }
  }
  trackGestureStart(event) {
    const now = Date.now();
    this.behaviorData.gestureEvents.push({
      type: 'gesturestart',
      scale: event.scale || 1,
      rotation: event.rotation || 0,
      timestamp: now
    });
    if (this.behaviorData.gestureEvents.length > 20) {
      this.behaviorData.gestureEvents.shift();
    }
  }
  trackGestureChange(event) {
    const now = Date.now();
    this.behaviorData.gestureEvents.push({
      type: 'gesturechange',
      scale: event.scale || 1,
      rotation: event.rotation || 0,
      timestamp: now
    });
    if (this.behaviorData.gestureEvents.length > 20) {
      this.behaviorData.gestureEvents.shift();
    }
  }
  trackGestureEnd(event) {
    const now = Date.now();
    this.behaviorData.gestureEvents.push({
      type: 'gestureend',
      scale: event.scale || 1,
      rotation: event.rotation || 0,
      timestamp: now
    });
    if (this.behaviorData.gestureEvents.length > 20) {
      this.behaviorData.gestureEvents.shift();
    }
  }
  detectMobileQuirks() {
    const userAgent = navigator.userAgent || '';
    this.behaviorData.mobileQuirks = {
      isIOS: /iPad|iPhone|iPod/.test(userAgent),
      isAndroid: /Android/.test(userAgent),
      isSafari: /Safari/.test(userAgent) && !/Chrome/.test(userAgent),
      isChrome: /Chrome/.test(userAgent),
      isFirefox: /Firefox/.test(userAgent),
      isEdge: /Edge/.test(userAgent),
      hasTouch: 'ontouchstart' in window,
      maxTouchPoints: navigator.maxTouchPoints || 0,
      hasDeviceMotion: 'DeviceMotionEvent' in window,
      hasDeviceOrientation: 'DeviceOrientationEvent' in window,
      hasGeolocation: 'geolocation' in navigator,
      hasVibration: 'vibrate' in navigator,
      screenWidth: screen.width,
      screenHeight: screen.height,
      pixelRatio: window.devicePixelRatio || 1,
      viewportWidth: window.innerWidth,
      viewportHeight: window.innerHeight
    };
  }
  getBehaviorSummary() {
    const metrics = this.calculateMetrics();
    return {
      sessionDuration: metrics.sessionDuration,
      mouseMovementCount: Array.isArray(this.behaviorData.mouseMovements) ? this.behaviorData.mouseMovements.length : 0,
      clickCount: Array.isArray(this.behaviorData.clickPatterns) ? this.behaviorData.clickPatterns.length : 0,
      typingSpeedWPM: Math.round(metrics.typingMetrics.wpm),
      focusLossCount: this.behaviorData.focusLost,
      anomalyScore: metrics.anomalyScore,
      suspiciousFlags: {
        straightLineMovement: metrics.mouseMetrics.straightLine,
        regularClicks: metrics.clickMetrics.tooRegular,
        fastTyping: metrics.typingMetrics.tooFast,
        fastScrolling: metrics.scrollMetrics.tooFast
      },
      touchEventCount: Array.isArray(this.behaviorData.touchEvents) ? this.behaviorData.touchEvents.length : 0,
      orientationChangeCount: Array.isArray(this.behaviorData.orientationChanges) ? this.behaviorData.orientationChanges.length : 0,
      deviceMotionEventCount: Array.isArray(this.behaviorData.deviceMotion) ? this.behaviorData.deviceMotion.length : 0,
      gestureEventCount: Array.isArray(this.behaviorData.gestureEvents) ? this.behaviorData.gestureEvents.length : 0,
      mobileQuirks: this.behaviorData.mobileQuirks || {}
    };
  }
}
export const behaviorTracker = new BehaviorTracker();
export const getBehaviorData = () => {
  return behaviorTracker.getBehaviorSummary();
};
export const isSuspiciousBehavior = () => {
  const summary = behaviorTracker.getBehaviorSummary();
  return summary.anomalyScore > 0.7;
};