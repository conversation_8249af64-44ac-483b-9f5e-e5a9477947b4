import CryptoJS from 'crypto-js';
class RequestSigner {
  constructor() {
    this.signingKey = this.generateSigningKey();
    this.nonces = new Set();
    this.maxNonceAge = 5 * 60 * 1000; // 5 minutes
  }
  generateSigningKey() {
    const timestamp = Date.now();
    const userAgent = navigator.userAgent;
    const screenInfo = `${screen.width}x${screen.height}`;
    const timezoneOffset = new Date().getTimezoneOffset();
    const keyMaterial = `${timestamp}-${userAgent}-${screenInfo}-${timezoneOffset}`;
    return CryptoJS.SHA256(keyMaterial).toString();
  }
  generateNonce() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    return `${timestamp}-${random}`;
  }
  createSignature(method, url, body, nonce, timestamp) {
    const bodyHash = body ? CryptoJS.SHA256(JSON.stringify(body)).toString() : '';
    const message = `${method}|${url}|${bodyHash}|${nonce}|${timestamp}`;
    return CryptoJS.HmacSHA256(message, this.signingKey).toString();
  }
  signRequest(method, url, body = null) {
    const timestamp = Date.now();
    const nonce = this.generateNonce();
    const signature = this.createSignature(method, url, body, nonce, timestamp);
    this.nonces.add(nonce);
    // Clean old nonces
    setTimeout(() => {
      this.nonces.delete(nonce);
    }, this.maxNonceAge);
    return {
      'X-Request-Signature': signature,
      'X-Request-Nonce': nonce,
      'X-Request-Timestamp': timestamp.toString(),
      'X-Client-Fingerprint': this.getClientFingerprint()
    };
  }
  getClientFingerprint() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Security fingerprint', 2, 2);
    const fingerprint = {
      canvas: canvas.toDataURL(),
      screen: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      doNotTrack: navigator.doNotTrack
    };
    return CryptoJS.SHA256(JSON.stringify(fingerprint)).toString();
  }
  validateRequestIntegrity(headers, method, url, body) {
    const requiredHeaders = ['X-Request-Signature', 'X-Request-Nonce', 'X-Request-Timestamp'];
    for (const header of requiredHeaders) {
      if (!headers[header]) {
        return { valid: false, reason: `Missing required header: ${header}` };
      }
    }
    const timestamp = parseInt(headers['X-Request-Timestamp']);
    const now = Date.now();
    // Check timestamp is within acceptable range (5 minutes)
    if (Math.abs(now - timestamp) > this.maxNonceAge) {
      return { valid: false, reason: 'Request timestamp too old or too far in future' };
    }
    // Check nonce hasn't been used
    if (this.nonces.has(headers['X-Request-Nonce'])) {
      return { valid: false, reason: 'Nonce already used (replay attack detected)' };
    }
    // Verify signature
    const expectedSignature = this.createSignature(
      method, 
      url, 
      body, 
      headers['X-Request-Nonce'], 
      timestamp
    );
    if (expectedSignature !== headers['X-Request-Signature']) {
      return { valid: false, reason: 'Invalid signature' };
    }
    return { valid: true };
  }
}
// Enhanced fetch wrapper with automatic request signing
class SecureFetch {
  constructor() {
    this.signer = new RequestSigner();
  }
  async fetch(url, options = {}) {
    const method = options.method || 'GET';
    const body = options.body;
    // Generate signature headers
    const signatureHeaders = this.signer.signRequest(method, url, body);
    // Merge with existing headers
    const headers = {
      ...options.headers,
      ...signatureHeaders
    };
    // Add additional security headers
    headers['X-Requested-With'] = 'XMLHttpRequest';
    headers['X-Client-Version'] = '1.0.0';
    headers['X-Security-Level'] = 'maximum';
    const secureOptions = {
      ...options,
      headers,
      credentials: 'same-origin',
      mode: 'cors'
    };
    try {
      const response = await fetch(url, secureOptions);
      // Validate response integrity if possible
      if (response.headers.get('X-Response-Signature')) {
        // Server can optionally sign responses too
        // Implementation would verify server signature here
      }
      return response;
    } catch (error) {
      throw error;
    }
  }
  // Convenience methods
  async get(url, options = {}) {
    return this.fetch(url, { ...options, method: 'GET' });
  }
  async post(url, data, options = {}) {
    return this.fetch(url, {
      ...options,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      body: JSON.stringify(data)
    });
  }
  async put(url, data, options = {}) {
    return this.fetch(url, {
      ...options,
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      body: JSON.stringify(data)
    });
  }
  async delete(url, options = {}) {
    return this.fetch(url, { ...options, method: 'DELETE' });
  }
}
// Global secure fetch instance
export const secureFetch = new SecureFetch();
export const requestSigner = new RequestSigner();
// Export for use in components
export { RequestSigner, SecureFetch };
