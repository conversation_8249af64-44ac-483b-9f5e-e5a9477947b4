export class MLBehaviorAnalyzer {
  constructor() {
    this.behaviorHistory = [];
    this.isModelLoaded = false; // Always false since we're not using TensorFlow.js
  }
  async loadModel() {
    this.isModelLoaded = true; // Mark as loaded for rule-based system
  }
  extractFeatures(behaviorData) {
    return [
      Array.isArray(behaviorData?.mouseMovements) ? behaviorData.mouseMovements.length / 100 : 0,
      Array.isArray(behaviorData?.clickPatterns) ? behaviorData.clickPatterns.length / 50 : 0,
      (behaviorData?.sessionDuration || 0) / 300000, // Normalize to 5 minutes
      (behaviorData?.focusLossCount || 0) / 10,
      behaviorData?.mouseMetrics?.variance || 0,
      behaviorData?.clickMetrics?.variance || 0,
      behaviorData?.typingMetrics?.wpm / 200 || 0,
      behaviorData?.scrollMetrics?.speed / 10 || 0,
      behaviorData?.anomalyScore || 0,
      Date.now() % 1000 / 1000 // Time entropy
    ];
  }
  async predict(behaviorData) {
    return this.fallbackDetection(behaviorData);
  }
  fallbackDetection(behaviorData) {
    let botScore = 0;
    const isAdminUser = this.checkAdminStatus();
    if (isAdminUser) {
      return { isBot: false, confidence: 0, method: 'admin-bypass', adminUser: true };
    }
    const isSafari = typeof navigator !== 'undefined' && /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
    const isMobile = typeof navigator !== 'undefined' && /iPhone|iPad|Android|Mobile/.test(navigator.userAgent);
    if (isSafari || isMobile) {
      return this.analyzeMobileBehavior(behaviorData, isSafari, isMobile);
    }
    const hasActivity = (Array.isArray(behaviorData?.mouseMovements) && behaviorData.mouseMovements.length > 0)
      || (Array.isArray(behaviorData?.clickPatterns) && behaviorData.clickPatterns.length > 0)
      || (Array.isArray(behaviorData?.typingSpeed) && behaviorData.typingSpeed.length > 0);
    if (!hasActivity) {
      return { isBot: true, confidence: 1, method: 'no-activity-detection' };
    }
    const features = this.extractFeatures(behaviorData);
    if (features[0] < 0.1) botScore += 0.3; // Low mouse movement
    if ((features[4] || 0) < 0.01) botScore += 0.2; // Low mouse variance
    if (features[1] < 0.05) botScore += 0.3; // Low click count
    if ((features[5] || 0) < 0.01) botScore += 0.2; // Low click variance
    if (features[2] < 0.1) botScore += 0.3; // Very short session
    if (features[3] > 0.5) botScore += 0.2; // High focus loss
    if ((features[6] || 0) > 0.8) botScore += 0.2; // Very fast typing
    if ((features[7] || 0) > 0.9) botScore += 0.2; // Very fast scrolling
    if ((features[8] || 0) > 0.8) botScore += 0.4; // High anomaly score
    if ((behaviorData?.anomalyScore || 0) > 0.8) botScore += 0.3;
    if ((behaviorData?.sessionDuration || 0) < 10000) botScore += 0.2;
    if ((behaviorData?.focusLossCount || 0) > 5) botScore += 0.1;
    if (!Array.isArray(behaviorData?.mouseMovements) || behaviorData.mouseMovements.length < 10) botScore += 0.2;
    const suspiciousFlags = behaviorData?.suspiciousFlags || {};
    if (suspiciousFlags.straightLineMovement) botScore += 0.2;
    if (suspiciousFlags.regularClicks) botScore += 0.2;
    if (suspiciousFlags.noMouseMovement) botScore += 0.3;
    if (suspiciousFlags.rapidClicks) botScore += 0.2;
    const result = {
      isBot: botScore > 0.85, // More forgiving threshold (was 0.7, now 0.85)
      confidence: Math.min(botScore, 1.0),
      method: 'enhanced-rule-based',
      securityLevel: 'full-protection'
    };
    return result;
  }
  checkAdminStatus() {
    try {
      const adminToken = localStorage.getItem('adminToken');
      if (adminToken) {
        try {
          const tokenData = JSON.parse(atob(adminToken.split('.')[1]));
          const isAdmin = tokenData.role === 'admin' || tokenData.role === 'owner';
          if (isAdmin) {
            return true;
          }
        } catch (e) {
        }
      }
      const userRole = sessionStorage.getItem('userRole');
      if (userRole === 'admin' || userRole === 'owner') {
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }
  analyzeRealtime(behaviorData) {
    const isAdminUser = this.checkAdminStatus();
    if (isAdminUser) {
      return {
        isBot: false,
        confidence: 0,
        anomalyScore: 0,
        riskLevel: 'LOW',
        adminUser: true,
        method: 'admin-bypass'
      };
    }
    const features = this.extractFeatures(behaviorData);
    const anomalyScore = this.calculateAnomalyScore(features);
    const confidence = this.classifyBehavior(features);
    this.behaviorHistory.push(features);
    const result = {
      isBot: confidence > 0.98, // More forgiving threshold (was 0.95, now 0.98)
      confidence: confidence,
      anomalyScore: anomalyScore,
      riskLevel: this.calculateRiskLevel(confidence, anomalyScore),
      method: 'realtime-analysis'
    };
    if (anomalyScore > 0.95) {
      this.behaviorHistory.push({
        timestamp: Date.now(),
        score: anomalyScore,
        features: features,
        alertLevel: 'HIGH'
      });
    }
    return result;
  }
  calculateAnomalyScore(features) {
    let score = 0;
    const weights = {
      mouseVelocityVariance: 0.2,
      mouseAccelerationPattern: 0.15,
      clickIntervalRegularity: 0.25,
      keystrokeRhythm: 0.2,
      sessionProgression: 0.2
    };
    for (const [feature, weight] of Object.entries(weights)) {
      if (features[feature] !== undefined) {
        score += this.compareToBaseline(feature, features[feature]) * weight;
      }
    }
    return Math.min(score, 1.0);
  }
  classifyBehavior(features) {
    let botIndicators = 0;
    if (features[0] < 0.01) botIndicators++;
    if (features[1] < 0.01) botIndicators++;
    if (features[2] < 0.05) botIndicators++;
    if (features[3] > 0.8) botIndicators++;
    if (features[4] < 0.001) botIndicators++;
    if (features[5] < 0.001) botIndicators++;
    if (features[6] > 0.95) botIndicators++;
    if (features[7] > 0.98) botIndicators++;
    if (features[8] > 0.9) botIndicators++;
    const score = botIndicators / 9;
    return Math.max(0, score - 0.2);
  }
  calculateRiskLevel(confidence, anomalyScore) {
    const combinedScore = (confidence + anomalyScore) / 2;
    if (combinedScore > 0.9) return 'CRITICAL';
    if (combinedScore > 0.7) return 'HIGH';
    if (combinedScore > 0.5) return 'MEDIUM';
    return 'LOW';
  }
  compareToBaseline(feature, value) {
    const baselines = {
      mouseVelocityVariance: 0.5,
      mouseAccelerationPattern: 0.3,
      clickIntervalRegularity: 0.4,
      keystrokeRhythm: 0.6,
      sessionProgression: 0.5
    };
    const baseline = baselines[feature] || 0.5;
    return Math.abs(value - baseline) / baseline;
  }
  getBehaviorSummary() {
    return {
      totalSessions: this.behaviorHistory.length,
      averageConfidence: this.behaviorHistory.reduce((sum, h) => sum + h.confidence, 0) / this.behaviorHistory.length,
      riskLevel: this.calculateOverallRiskLevel()
    };
  }
  calculateOverallRiskLevel() {
    const highRiskSessions = this.behaviorHistory.filter(h => h.confidence > 0.7).length;
    const riskRatio = highRiskSessions / this.behaviorHistory.length;
    if (riskRatio > 0.5) return 'HIGH';
    if (riskRatio > 0.2) return 'MEDIUM';
    return 'LOW';
  }
  analyzeMobileBehavior(behaviorData, isSafari, isMobile) {
    const hasTouchActivity = (Array.isArray(behaviorData?.touchEvents) && behaviorData.touchEvents.length > 0)
      || (Array.isArray(behaviorData?.clickPatterns) && behaviorData.clickPatterns.length > 0);
    const hasMotionActivity = (Array.isArray(behaviorData?.deviceMotion) && behaviorData.deviceMotion.length > 0)
      || (Array.isArray(behaviorData?.orientationChanges) && behaviorData.orientationChanges.length > 0);
    const hasScrollActivity = (Array.isArray(behaviorData?.scrollBehavior) && behaviorData.scrollBehavior.length > 0);
    const mobileFeatures = this.extractMobileFeatures(behaviorData);
    let botConfidence = 0;
    let detectionMethod = 'mobile-analysis';
    if (hasTouchActivity) {
      const touchAnalysis = this.analyzeTouchPatterns(behaviorData.clickPatterns || []);
      botConfidence += touchAnalysis.suspiciousScore * 0.3;
      if (touchAnalysis.isSuspicious) {
        detectionMethod += '-touch-suspicious';
      }
    }
    if (hasScrollActivity) {
      const scrollAnalysis = this.analyzeMobileScrollBehavior(behaviorData.scrollBehavior || []);
      botConfidence += scrollAnalysis.suspiciousScore * 0.2;
      if (scrollAnalysis.isSuspicious) {
        detectionMethod += '-scroll-suspicious';
      }
    }
    if (hasMotionActivity) {
      const motionAnalysis = this.analyzeDeviceMotion(behaviorData.deviceMotion || []);
      botConfidence += motionAnalysis.suspiciousScore * 0.2;
      if (motionAnalysis.isSuspicious) {
        detectionMethod += '-motion-suspicious';
      }
    }
    const sessionAnalysis = this.analyzeMobileSessionTiming(behaviorData);
    botConfidence += sessionAnalysis.suspiciousScore * 0.3;
    if (sessionAnalysis.isSuspicious) {
      detectionMethod += '-timing-suspicious';
    }
    const anomalyScore = this.calculateMobileAnomalyScore(mobileFeatures);
    botConfidence = Math.max(botConfidence, anomalyScore);
    if (isSafari) {
      botConfidence *= 0.9;
      detectionMethod += '-safari-adjusted';
    }
    const minimumMobileConfidence = 0.1; // Always maintain some level of scrutiny
    botConfidence = Math.max(botConfidence, minimumMobileConfidence);
    const isBot = botConfidence > 0.95; // More forgiving threshold for mobile (was 0.9, now 0.95)
    return {
      isBot,
      confidence: Math.min(botConfidence, 1.0),
      method: detectionMethod,
      mobileSpecific: true,
      deviceType: isMobile ? 'mobile' : 'safari',
      features: mobileFeatures
    };
  }
  extractMobileFeatures(behaviorData) {
    return {
      touchEventCount: (behaviorData?.touchEvents || []).length,
      clickPatternVariance: this.calculateVariance((behaviorData?.clickPatterns || []).map(c => c.timestamp)),
      scrollVelocityVariance: this.calculateVariance((behaviorData?.scrollBehavior || []).map(s => s.scrollY)),
      orientationChanges: (behaviorData?.orientationChanges || []).length,
      deviceMotionEvents: (behaviorData?.deviceMotion || []).length,
      sessionDuration: Date.now() - (behaviorData?.sessionStartTime || Date.now()),
      focusLossCount: behaviorData?.focusLost || 0
    };
  }
  analyzeTouchPatterns(clickPatterns) {
    if (clickPatterns.length < 3) {
      return { suspiciousScore: 0.3, isSuspicious: false, reason: 'insufficient-data' };
    }
    let suspiciousScore = 0;
    const reasons = [];
    const intervals = [];
    for (let i = 1; i < clickPatterns.length; i++) {
      intervals.push(clickPatterns[i].timestamp - clickPatterns[i-1].timestamp);
    }
    const intervalVariance = this.calculateVariance(intervals);
    if (intervalVariance < 100) { // Very regular intervals
      suspiciousScore += 0.4;
      reasons.push('regular-intervals');
    }
    const positions = clickPatterns.map(c => ({ x: c.x, y: c.y }));
    const positionVariance = this.calculatePositionVariance(positions);
    if (positionVariance < 50) { // Very regular positions
      suspiciousScore += 0.3;
      reasons.push('regular-positions');
    }
    for (let i = 1; i < clickPatterns.length; i++) {
      const distance = Math.sqrt(
        Math.pow(clickPatterns[i].x - clickPatterns[i-1].x, 2) +
        Math.pow(clickPatterns[i].y - clickPatterns[i-1].y, 2)
      );
      const timeDiff = clickPatterns[i].timestamp - clickPatterns[i-1].timestamp;
      const speed = distance / timeDiff; // pixels per ms
      if (speed > 5) { // Impossibly fast for human touch
        suspiciousScore += 0.2;
        reasons.push('impossible-speed');
        break;
      }
    }
    return {
      suspiciousScore: Math.min(suspiciousScore, 1.0),
      isSuspicious: suspiciousScore > 0.5,
      reasons
    };
  }
  analyzeMobileScrollBehavior(scrollBehavior) {
    if (scrollBehavior.length < 3) {
      return { suspiciousScore: 0.2, isSuspicious: false, reason: 'insufficient-data' };
    }
    let suspiciousScore = 0;
    const reasons = [];
    const scrollPositions = scrollBehavior.map(s => s.scrollY);
    const scrollVariance = this.calculateVariance(scrollPositions);
    if (scrollVariance < 10) { // Very linear scrolling
      suspiciousScore += 0.3;
      reasons.push('linear-scrolling');
    }
    for (let i = 1; i < scrollBehavior.length; i++) {
      const scrollDistance = Math.abs(scrollBehavior[i].scrollY - scrollBehavior[i-1].scrollY);
      const timeDiff = scrollBehavior[i].timestamp - scrollBehavior[i-1].timestamp;
      const scrollSpeed = scrollDistance / timeDiff; // pixels per ms
      if (scrollSpeed > 10) { // Very fast scrolling
        suspiciousScore += 0.2;
        reasons.push('fast-scrolling');
        break;
      }
    }
    return {
      suspiciousScore: Math.min(suspiciousScore, 1.0),
      isSuspicious: suspiciousScore > 0.4,
      reasons
    };
  }
  analyzeDeviceMotion(deviceMotion) {
    if (deviceMotion.length < 5) {
      return { suspiciousScore: 0.1, isSuspicious: false, reason: 'insufficient-data' };
    }
    let suspiciousScore = 0;
    const reasons = [];
    const motionVariance = this.calculateVariance(deviceMotion.map(m => m.acceleration?.x || 0));
    if (motionVariance < 0.01) { // Too stable, possibly simulated
      suspiciousScore += 0.4;
      reasons.push('too-stable');
    }
    return {
      suspiciousScore: Math.min(suspiciousScore, 1.0),
      isSuspicious: suspiciousScore > 0.3,
      reasons
    };
  }
  analyzeMobileSessionTiming(behaviorData) {
    const sessionDuration = Date.now() - (behaviorData?.sessionStartTime || Date.now());
    let suspiciousScore = 0;
    const reasons = [];
    if (sessionDuration < 5000) { // Less than 5 seconds
      suspiciousScore += 0.5;
      reasons.push('too-fast');
    }
    const lastInteraction = Math.max(
      (behaviorData?.mouseMovements || []).slice(-1)[0]?.timestamp || 0,
      (behaviorData?.clickPatterns || []).slice(-1)[0]?.timestamp || 0,
      (behaviorData?.scrollBehavior || []).slice(-1)[0]?.timestamp || 0
    );
    if (Date.now() - lastInteraction > 30000) { // No interaction for 30 seconds
      suspiciousScore += 0.3;
      reasons.push('no-interaction');
    }
    return {
      suspiciousScore: Math.min(suspiciousScore, 1.0),
      isSuspicious: suspiciousScore > 0.4,
      reasons
    };
  }
  calculateMobileAnomalyScore(features) {
    let anomalyScore = 0;
    if (features.touchEventCount === 0 && features.clickPatternVariance === 0) {
      anomalyScore += 0.3; // No touch interaction is suspicious on mobile
    }
    if (features.orientationChanges === 0 && features.sessionDuration > 60000) {
      anomalyScore += 0.2; // No orientation changes in long session
    }
    if (features.deviceMotionEvents === 0) {
      anomalyScore += 0.1; // No device motion data
    }
    return Math.min(anomalyScore, 1.0);
  }
  calculateVariance(values) {
    if (!values || values.length < 2) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    const variance = squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
    return variance;
  }
  calculatePositionVariance(positions) {
    if (positions.length < 2) return 0;
    const xValues = positions.map(p => p.x);
    const yValues = positions.map(p => p.y);
    return (this.calculateVariance(xValues) + this.calculateVariance(yValues)) / 2;
  }
}
export const mlAnalyzer = new MLBehaviorAnalyzer();