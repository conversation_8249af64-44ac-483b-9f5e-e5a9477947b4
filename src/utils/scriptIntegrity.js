export class ScriptIntegrityProtector {
  constructor() {
    this.integrityChecks = [];
    this.debuggingDetected = false;
    this.consoleAccessDetected = false;
    this.tamperingDetected = false;
    this.honeypotTriggers = [];
    this.monitoringActive = false;
    this.lastDetectionMethod = '';
    this.violationCount = 0;
    sessionStorage.removeItem('securityViolation');
    sessionStorage.removeItem('violationType');
    sessionStorage.removeItem('violationTime');
    this.startImmediateDetection();
  }
  startImmediateDetection() {
    if (typeof window !== 'undefined') {
      this.startAggressiveDetection();
    }
  }
  startAggressiveDetection() {
    document.addEventListener('keydown', (e) => {
      if (e.key === 'F12' ||
          (e.ctrlKey && e.shiftKey && e.key === 'I') ||
          (e.ctrlKey && e.shiftKey && e.key === 'J')) {
        e.preventDefault();
        this.debuggingDetected = true;
        this.triggerSecurityViolation('devtools_hotkey', `DevTools hotkey pressed: ${e.key}`);
      }
    });
  }
  checkWindowDimensions() {
  }
  checkPerformanceTiming() {
    try {
      const start = Date.now();
      debugger;
      const end = Date.now();
      if (end - start > 100) {
        this.debuggingDetected = true;
        this.triggerSecurityViolation('debugger_pause_detected', `Debugger pause detected: ${end - start}ms`);
      }
    } catch (e) {
      this.debuggingDetected = true;
      this.triggerSecurityViolation('debugger_statement_blocked', 'Debugger statement was blocked');
    }
  }
  createBlockingOverlay(reason) {
    if (document.body) {
      document.body.innerHTML = '';
      const overlay = document.createElement('div');
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #000;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 999999;
        font-family: Arial, sans-serif;
        font-size: 18px;
        text-align: center;
      `;
      overlay.innerHTML = `
        <div>
          <h2>🚨 Security Violation Detected</h2>
          <p>Access has been blocked due to security policy violation.</p>
          <p>Violation Type: ${reason}</p>
          <p>Device: ${this.detectMobileDevice() ? 'Mobile' : 'Desktop'}</p>
          <p>Browser: ${navigator.userAgent.split(' ')[0]}</p>
          <p>Detection Method: ${this.lastDetectionMethod || 'Multiple'}</p>
          <p>Timestamp: ${new Date().toLocaleString()}</p>
          <p>Please close all developer tools and refresh the page.</p>
          <button onclick="window.location.reload()" style="
            margin-top: 20px;
            padding: 10px 20px;
            background: #ff4444;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
          ">Refresh Page</button>
        </div>
      `;
      document.body.appendChild(overlay);
    }
  }
  showRecoverySuccess() {
    if (document.body) {
      document.body.innerHTML = '';
      const successOverlay = document.createElement('div');
      successOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #006400;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 999999;
        font-family: Arial, sans-serif;
        font-size: 18px;
        text-align: center;
      `;
      successOverlay.innerHTML = `
        <div>
          <h2>✅ Console Successfully Closed!</h2>
          <p>Great job! You've properly closed the developer tools.</p>
          <p>Security violation has been cleared.</p>
          <p>You can now refresh the page to continue normally.</p>
          <button onclick="window.location.reload()" style="
            margin-top: 20px;
            padding: 10px 20px;
            background: #228B22;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
          ">Continue - Start Fresh</button>
        </div>
      `;
      document.body.appendChild(successOverlay);
    }
  }
  initialize() {
    try {
      if (typeof window === 'undefined') {
        return;
      }
      this.startTargetedDevToolsDetection();
      this.startBasicIntegrityMonitoring();
      this.startIntegrityMonitoring();
      this.startAntiDebugging();
      this.detectTimingAnomalies();
      this.checkDebuggingAPIs();
      this.monitorScriptInjection();
      this.setupHoneypotTraps();
      this.createFakeEndpoints();
      this.createFakeVulnerabilities();
      this.monitorHoneypotInteractions();
      this.monitoringActive = true;
    } catch (error) {
    }
  }
  initializeForKeyGeneration() {
    try {
      if (typeof window === 'undefined') {
        return;
      }
      this.startImmediateKeyGenDetection();
      this.startKeyGenConsoleMonitoring();
      this.startBasicIntegrityMonitoring();
      this.monitoringActive = true;
    } catch (error) {
    }
  }
  startImmediateKeyGenDetection() {
    const self = this;
    const aggressiveDevToolsCheck = () => {
      const heightDiff = window.outerHeight - window.innerHeight;
      const widthDiff = window.outerWidth - window.innerWidth;
      const heightThreshold = 200; 
      const widthThreshold = 200;
      if (heightDiff > heightThreshold || widthDiff > widthThreshold) {
        self.debuggingDetected = true;
        self.consoleAccessDetected = true;
        self.triggerSecurityViolation('devtools_detected_keygen', 'Developer tools detected during key generation');
        return true;
      }
      try {
        const before = performance.now();
        console.profile();
        console.profileEnd();
        const after = performance.now();
        if (after - before > 50) {
          self.debuggingDetected = true;
          self.consoleAccessDetected = true;
          self.triggerSecurityViolation('console_timing_detected', 'Console timing anomaly detected');
          return true;
        }
      } catch (e) {
        self.debuggingDetected = true;
        self.consoleAccessDetected = true;
        self.triggerSecurityViolation('console_blocked', 'Console methods blocked');
        return true;
      }
      try {
        const debuggerTest = () => {
          const start = Date.now();
          debugger;
          return Date.now() - start;
        };
        if (debuggerTest() > 100) {
          self.debuggingDetected = true;
          self.consoleAccessDetected = true;
          self.triggerSecurityViolation('debugger_detected', 'Debugger statement paused');
          return true;
        }
      } catch (e) {
      }
      return false;
    };
    aggressiveDevToolsCheck();
    this.keyGenDetectionInterval = detectionInterval;
  }
  stopKeyGenDetection() {
    if (this.keyGenDetectionInterval) {
      clearInterval(this.keyGenDetectionInterval);
      this.keyGenDetectionInterval = null;
    }
  }
  startKeyGenConsoleMonitoring() {
    const self = this;
    const originalMethods = {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info,
      debug: console.debug,
      clear: console.clear,
      dir: console.dir,
      table: console.table
    };
    Object.keys(originalMethods).forEach(method => {
      console[method] = function(...args) {
        self.debuggingDetected = true;
        self.consoleAccessDetected = true;
        self.triggerSecurityViolation('console_method_used', `Console.${method} accessed during key generation`);
        return originalMethods[method].apply(console, args);
      };
    });
    this.originalConsoleMethods = originalMethods;
  }
  immediateDevToolsCheck() {
    return;
  }
  startTargetedDevToolsDetection() {
    const self = this;
    let detectionStarted = false;
    setTimeout(() => {
      if (detectionStarted) return;
      detectionStarted = true;
      const initialDimensions = {
        innerHeight: window.innerHeight,
        innerWidth: window.innerWidth,
        outerHeight: window.outerHeight,
        outerWidth: window.outerWidth
      };
      const checkDevTools = () => {
        const heightDiff = window.outerHeight - window.innerHeight;
        const widthDiff = window.outerWidth - window.innerWidth;
        const heightThreshold = 350; 
        const widthThreshold = 350;
        const hasValidDimensions = window.innerHeight > 400 && window.innerWidth > 500;
        const significantChange = Math.abs(window.innerHeight - initialDimensions.innerHeight) > 200;
        if (hasValidDimensions && significantChange &&
            (heightDiff > heightThreshold || widthDiff > widthThreshold)) {
          let consoleDetected = false;
          try {
            const before = performance.now();
            console.profile();
            console.profileEnd();
            const after = performance.now();
            if (after - before > 100) {
              consoleDetected = true;
            }
          } catch (e) {
          }
          if (consoleDetected) {
            self.debuggingDetected = true;
            self.consoleAccessDetected = true;
            self.triggerSecurityViolation('devtools_detected', 'Developer tools detected during key generation');
          }
        }
      };
    }, 3000); // Wait 3 seconds after page load
  }
  startBasicIntegrityMonitoring() {
    this.checkForObviousTampering();
  }
  checkForObviousTampering() {
    const self = this;
    const checkScripts = () => {
      const scripts = document.querySelectorAll('script');
      const suspiciousScripts = Array.from(scripts).filter(script => {
        return script.innerHTML.includes('eval(') ||
               script.innerHTML.includes('Function(') ||
               script.innerHTML.includes('debugger;');
      });
      if (suspiciousScripts.length > 0) {
        this.tamperingDetected = true;
        this.triggerSecurityViolation('script_tampering', 'Suspicious script detected');
      }
    };
    setTimeout(checkScripts, 5000);
  }
  startIntegrityMonitoring() {
    this.monitorScriptInjection();
    this.checkForTamperingIndicators();
  }
  startAntiDebugging() {
    this.checkDeveloperTools();
    this.monitorDebuggingPatterns();
    this.detectTimingAnomalies();
    this.checkDebuggingAPIs();
  }
  startConsoleMonitoring() {
    const self = this;
    const isMobile = this.detectMobileDevice();
    if (isMobile) {
      this.startMobileConsoleDetection();
    } else {
      this.startDesktopConsoleDetection();
    }
  }
  detectMobileDevice() {
    const userAgent = navigator.userAgent || '';
    const isMobileUA = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS/i.test(userAgent);
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const isSmallScreen = window.screen.width <= 768 || window.screen.height <= 768;
    return isMobileUA || (isTouchDevice && isSmallScreen);
  }
  startMobileConsoleDetection() {
    const self = this;
    let lastViewportWidth = window.innerWidth;
    let lastViewportHeight = window.innerHeight;
    const checkMobileDevTools = () => {
      const currentWidth = window.innerWidth;
      const currentHeight = window.innerHeight;
      const widthChange = Math.abs(currentWidth - lastViewportWidth);
      const heightChange = Math.abs(currentHeight - lastViewportHeight);
      if (widthChange > 100 || heightChange > 100) {
        if (!this.isOrientationChange()) {
          self.debuggingDetected = true;
          self.consoleAccessDetected = true;
          self.triggerSecurityViolation('mobile_devtools_detected', 'Mobile developer tools detected via viewport change');
        }
      }
      lastViewportWidth = currentWidth;
      lastViewportHeight = currentHeight;
    };
    const detectMobileConsolePerformance = () => {
      try {
        const start = performance.now();
        console.profile('mobile-detection');
        console.profileEnd('mobile-detection');
        const end = performance.now();
        if (end - start > 50) {
          self.debuggingDetected = true;
          self.consoleAccessDetected = true;
          self.triggerSecurityViolation('mobile_console_performance', 'Mobile console detected via performance timing');
        }
      } catch (e) {
        self.debuggingDetected = true;
        self.consoleAccessDetected = true;
        self.triggerSecurityViolation('mobile_console_blocked', 'Mobile console methods blocked');
      }
    };
    const detectMobileDebugContext = () => {
      const debugIndicators = [
        window.webkit && window.webkit.messageHandlers,
        window.chrome && window.chrome.runtime,
        window.external && window.external.AddSearchProvider,
        document.documentElement.getAttribute('debug'),
        window.location.search.includes('debug')
      ];
      if (debugIndicators.some(indicator => indicator)) {
        self.debuggingDetected = true;
        self.consoleAccessDetected = true;
        self.triggerSecurityViolation('mobile_debug_context', 'Mobile debug context detected');
      }
    };
    window.addEventListener('orientationchange', () => {
      this.lastOrientationChange = Date.now();
    });
  }
  isOrientationChange() {
    return this.lastOrientationChange && (Date.now() - this.lastOrientationChange) < 1000;
  }
  startDesktopConsoleDetection() {
    const self = this;
    const detectConsolePerformance = () => {
      try {
        const before = performance.now();
        console.profile();
        console.profileEnd();
        const after = performance.now();
        if (after - before > 100) {
          self.debuggingDetected = true;
          self.consoleAccessDetected = true;
          self.triggerSecurityViolation('desktop_console_performance', 'Desktop console detected via performance timing');
        }
      } catch (e) {
        self.debuggingDetected = true;
        self.consoleAccessDetected = true;
        self.triggerSecurityViolation('desktop_console_blocked', 'Desktop console methods blocked');
      }
    };
  }
  overrideConsoleMethods() {
  }
  detectConsoleOpening() {
    const self = this;
    const isMobile = this.detectMobileDevice();
    let devtools = {
      open: false,
      orientation: null,
      lastCheck: Date.now()
    };
    const detectDevTools = () => {
      const now = Date.now();
      if (now - devtools.lastCheck < 500) return;
      devtools.lastCheck = now;
      let detected = false;
      const detectionMethods = [];
      if (isMobile) {
        const viewportRatio = window.innerWidth / window.outerWidth;
        if (viewportRatio < 0.8 && !this.isOrientationChange()) {
          detected = true;
          detectionMethods.push('mobile_viewport_ratio');
        }
        try {
          const start = performance.now();
          for (let i = 0; i < 1000; i++) {
            Math.random();
          }
          const end = performance.now();
          if (end - start > 10) { // Mobile DevTools cause performance degradation
            detected = true;
            detectionMethods.push('mobile_performance_degradation');
          }
        } catch (e) {
          detected = true;
          detectionMethods.push('mobile_performance_error');
        }
        if (window.webkit?.messageHandlers?.console ||
            window.chrome?.runtime?.onConnect ||
            window.external?.AddSearchProvider) {
          detected = true;
          detectionMethods.push('mobile_debug_api');
        }
      } else {
        try {
          const before = performance.now();
          console.profile('detection-test');
          console.profileEnd('detection-test');
          const after = performance.now();
          if (after - before > 100) {
            detected = true;
            detectionMethods.push('desktop_console_timing');
          }
        } catch (e) {
          detected = true;
          detectionMethods.push('desktop_console_blocked');
        }
        try {
          if (window.chrome && window.chrome.runtime && window.chrome.runtime.onConnect) {
            detected = true;
            detectionMethods.push('desktop_chrome_extension');
          }
        } catch (e) {
          detected = true;
          detectionMethods.push('desktop_chrome_error');
        }
      }
      try {
        let devtools_detected = false;
        const threshold = 100;
        if (devtools_detected) {
          detected = true;
          detectionMethods.push('universal_debugger');
        }
      } catch (e) {
        detected = true;
        detectionMethods.push('universal_debugger_blocked');
      }
      if (detected && !devtools.open) {
        devtools.open = true;
        self.debuggingDetected = true;
        self.consoleAccessDetected = true;
        self.triggerSecurityViolation('devtools_opened',
          `Developer tools detected via: ${detectionMethods.join(', ')} (${isMobile ? 'mobile' : 'desktop'})`);
      }
    };
    let focusLossCount = 0;
    document.addEventListener('keydown', (e) => {
      if (e.key === 'F12' ||
          (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J')) ||
          (e.ctrlKey && e.key === 'U')) {
        e.preventDefault();
        self.debuggingDetected = true;
        self.consoleAccessDetected = true;
        self.triggerSecurityViolation('devtools_shortcut', 'DevTools shortcut detected');
      }
    });
  }
  setupHoneypotTraps() {
    this.createFakeSensitiveData();
    this.createFakeEndpoints();
    this.createFakeVulnerabilities();
    this.monitorHoneypotInteractions();
  }
  checkDeveloperTools() {
    const checkDevTools = () => {
      const threshold = 160;
      const widthThreshold = window.outerWidth - window.innerWidth > threshold;
      const heightThreshold = window.outerHeight - window.innerHeight > threshold;
      if (widthThreshold || heightThreshold) {
        this.debuggingDetected = true;
      }
    };
    const checkDebugProps = () => {
      const debugProps = [
        'firebug',
        'console.profiles',
        'console.profileEnd',
        'debug',
        'debugger'
      ];
      debugProps.forEach(prop => {
        if (window[prop] || console[prop]) {
          this.debuggingDetected = true;
        }
      });
    };
    const checkDebugFunctions = () => {
      const debugFunctions = [
        'debugger',
        'console.trace',
        'console.profile',
        'console.profileEnd'
      ];
      debugFunctions.forEach(func => {
        try {
          const funcRef = func.split('.').reduce((obj, prop) => obj && obj[prop], window);
          if (typeof funcRef === 'function') {
            this.debuggingDetected = true;
          }
        } catch (e) {
        }
      });
    };
  }
  monitorDebuggingPatterns() {
    const checkBreakpoints = () => {
      const start = performance.now();
      debugger;
      const end = performance.now();
      if (end - start > 100) {
        this.debuggingDetected = true;
      }
    };
    const checkStepThrough = () => {
      let stepCount = 0;
      const originalEval = window.eval;
      if (originalEval) {
        window.eval = function(code) {
          stepCount++;
          if (stepCount > 50) { // Increased threshold
            self.debuggingDetected = true;
          }
          return originalEval.call(this, code);
        };
      }
    };
    checkStepThrough();
  }
  detectTimingAnomalies() {
    try {
      const self = this; // Capture the class instance reference
      const checkPerformanceTiming = () => {
        if (typeof performance !== 'undefined' && performance.now) {
          const originalNow = performance.now;
          let callCount = 0;
          performance.now = function() {
            callCount++;
            if (callCount > 5000) { // Increased threshold
              self.debuggingDetected = true;
            }
            return originalNow.call(this);
          };
        }
      };
    const checkTimerManipulation = () => {
      const originalSetTimeout = window.setTimeout;
      const originalSetInterval = window.setInterval;
      window.setTimeout = function(fn, delay) {
        if (delay < 1) {
          self.debuggingDetected = true;
          self.triggerSecurityViolation('timer_manipulation', 'setTimeout');
        }
        return originalSetTimeout.call(this, fn, delay);
      };
      window.setInterval = function(fn, delay) {
        if (delay < 1) {
          self.debuggingDetected = true;
          self.triggerSecurityViolation('timer_manipulation', 'setInterval');
        }
        return originalSetInterval.call(this, fn, delay);
      };
    };
      checkPerformanceTiming();
      checkTimerManipulation();
    } catch (error) {
    }
  }
  checkDebuggingAPIs() {
    if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'development') {
      this.debuggingDetected = true;
      this.triggerSecurityViolation('node_debugging', 'NODE_ENV');
    }
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      this.debuggingDetected = true;
      this.triggerSecurityViolation('react_devtools', 'global_hook');
    }
    if (window.__REDUX_DEVTOOLS_EXTENSION__) {
      this.debuggingDetected = true;
      this.triggerSecurityViolation('redux_devtools', 'extension');
    }
  }
  monitorScriptInjection() {
    const self = this; // Capture the class instance reference
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
      if (tagName.toLowerCase() === 'script') {
        self.triggerSecurityViolation('script_injection', 'createElement');
      }
      return originalCreateElement.call(this, tagName);
    };
    const originalEval = window.eval;
    if (originalEval) {
      window.eval = function(code) {
        self.triggerSecurityViolation('eval_usage', 'eval');
        return originalEval.call(this, code);
      };
    }
  }
  createFakeSensitiveData() {
    const self = this; // Capture the class instance reference
    window.fakeApiKey = 'sk_test_fake_key_123456789';
    window.fakeSecretKey = 'secret_fake_key_987654321';
    window.fakeEndpoints = {
      admin: '/api/admin/keys',
      debug: '/api/debug/info',
      bypass: '/api/bypass/security'
    };
    Object.keys(window.fakeEndpoints).forEach(key => {
      Object.defineProperty(window.fakeEndpoints, key, {
        get: function() {
          self.triggerSecurityViolation('honeypot_access', `fake_endpoint_${key}`);
          return `/fake/${key}`;
        }
      });
    });
  }
  createFakeEndpoints() {
    const self = this; // Capture the class instance reference
    if (typeof fetch !== 'undefined') {
      const originalFetch = window.fetch;
      window.fetch = function(url, options) {
        if (typeof url === 'string' && url.includes('/api/admin/')) {
          self.triggerSecurityViolation('honeypot_endpoint', 'admin_api');
        }
        if (typeof url === 'string' && url.includes('/api/debug/')) {
          self.triggerSecurityViolation('honeypot_endpoint', 'debug_api');
        }
        return originalFetch.call(this, url, options);
      };
    }
  }
  createFakeVulnerabilities() {
    const self = this; // Capture the class instance reference
    window.fakeXSS = function(input) {
      self.triggerSecurityViolation('honeypot_vulnerability', 'fake_xss');
      return input;
    };
    window.fakeSQLInjection = function(query) {
      self.triggerSecurityViolation('honeypot_vulnerability', 'fake_sql');
      return query;
    };
  }
  monitorHoneypotInteractions() {
    this.honeypotTriggers = [];
  }
  calculateScriptHash(code) {
    let hash = 0;
    for (let i = 0; i < code.length; i++) {
      const char = code.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(16);
  }
  getDeviceFingerprintCode() {
    return 'getUnbreakableFingerprint';
  }
  getMLAnalysisCode() {
    return 'MLBehaviorAnalyzer';
  }
  getBehaviorTrackerCode() {
    return 'getBehaviorData';
  }
  getScriptCode(scriptName) {
    return scriptName;
  }
  triggerSecurityViolation(type, details) {
    this.violationCount++;
    this.lastDetectionMethod = type;
    const violation = {
      type,
      details,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      deviceType: this.detectMobileDevice() ? 'mobile' : 'desktop',
      violationNumber: this.violationCount,
      sessionId: sessionStorage.getItem('sessionId') || 'unknown'
    };
    if (type.startsWith('honeypot_')) {
      this.honeypotTriggers.push(violation);
    }
    this.reportViolation(violation);
    switch (type) {
      case 'console_access_detected':
      case 'console_clear_detected':
      case 'console_tostring_detected':
      case 'devtools_opened':
      case 'devtools_detected_keygen':
      case 'console_timing_detected':
      case 'console_blocked':
      case 'debugger_detected':
      case 'console_method_used':
      case 'devtools_detected':
      case 'devtools_shortcut':
      case 'devtools_focus_loss':
      case 'global_devtools_detection':
        this.invalidateSession();
        this.blockPageAccess(type, details);
        break;
      case 'suspicious_script_injection':
      case 'function_modification':
      case 'script_tampering':
      case 'eval_usage':
      case 'script_injection':
        this.invalidateSession();
        this.shutdown('Script tampering detected');
        break;
      case 'timer_manipulation':
      case 'node_debugging':
      case 'react_devtools':
      case 'redux_devtools':
      case 'honeypot_access':
      case 'honeypot_endpoint':
      case 'honeypot_vulnerability':
        this.invalidateSession();
        this.blockPageAccess(type, details);
        break;
      case 'dev_tools_detected':
      case 'debug_props_detected':
      case 'debug_functions_detected':
        if (/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent)) {
          break;
        }
        this.invalidateSession();
        this.blockPageAccess(type, details);
        break;
      default:
        break;
    }
  }
  invalidateSession() {
    try {
      sessionStorage.clear();
      localStorage.clear();
      const sessionKeys = ['sessionId', 'step1Completed', 'step2Completed', 'keyGenerated'];
      sessionKeys.forEach(key => {
        sessionStorage.removeItem(key);
        localStorage.removeItem(key);
      });
      sessionStorage.setItem('securityViolation', 'true');
      sessionStorage.setItem('violationTime', new Date().toISOString());
    } catch (error) {
    }
  }
  blockPageAccess(violationType, details) {
    const overlay = document.createElement('div');
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #000;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 999999;
      font-family: Arial, sans-serif;
      font-size: 18px;
      text-align: center;
    `;
    overlay.innerHTML = `
      <div>
        <h2>🚨 Security Violation Detected</h2>
        <p>Access has been blocked due to security policy violation.</p>
        <p><strong>Violation Type:</strong> ${violationType}</p>
        <p><strong>Details:</strong> ${details}</p>
        <p><strong>Device:</strong> ${this.detectMobileDevice() ? 'Mobile' : 'Desktop'}</p>
        <p><strong>Browser:</strong> ${navigator.userAgent.split(' ')[0]}</p>
        <p><strong>Detection Method:</strong> ${this.lastDetectionMethod || 'Multiple'}</p>
        <p><strong>Violation #:</strong> ${this.violationCount}</p>
        <p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
        <p><strong>Page:</strong> ${window.location.pathname}</p>
        <hr style="margin: 20px 0; border: 1px solid #444;">
        <p>Please close all developer tools and refresh the page.</p>
        <button onclick="window.location.reload()" style="
          margin-top: 20px;
          padding: 10px 20px;
          background: #ff4444;
          color: white;
          border: none;
          border-radius: 5px;
          cursor: pointer;
          font-size: 16px;
        ">Reload Page</button>
      </div>
    `;
    document.body.innerHTML = '';
    document.body.appendChild(overlay);
    throw new Error(`Security violation: ${violationType} - ${details}`);
  }
  async reportViolation(violation) {
    try {
      await fetch('/.netlify/functions/security-violation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(violation)
      });
    } catch (error) {
    }
  }
  shutdown(reason) {
    if (window.fakeApiKey) delete window.fakeApiKey;
    if (window.fakeSecretKey) delete window.fakeSecretKey;
    if (window.fakeEndpoints) delete window.fakeEndpoints;
    window.keyGenerationDisabled = true;
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = 'position:fixed;top:0;left:0;width:100%;height:100%;background:red;color:white;z-index:9999;display:flex;align-items:center;justify-content:center;font-size:24px;';
    errorDiv.textContent = 'Security violation detected. Access denied.';
    document.body.appendChild(errorDiv);
  }
  checkForTamperingIndicators() {
    const checkScriptInjection = () => {
      const scripts = document.querySelectorAll('script');
      const suspiciousScripts = Array.from(scripts).filter(script => {
        return script.src.includes('eval') || 
               script.src.includes('debugger') ||
               script.innerHTML.includes('eval(') ||
               script.innerHTML.includes('debugger');
      });
      if (suspiciousScripts.length > 0) {
        this.tamperingDetected = true;
        this.triggerSecurityViolation('suspicious_script_injection', 'suspicious_script_injection');
      }
    };
    const checkFunctionModification = () => {
      const criticalFunctions = [
        'getDeviceFingerprint',
        'validateKey',
        'generateKey'
      ];
      criticalFunctions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
          const funcString = window[funcName].toString();
          if (funcString.includes('debugger') || funcString.includes('eval(')) {
            this.tamperingDetected = true;
            this.triggerSecurityViolation('function_modification', `function_${funcName}_modified`);
          }
        }
      });
    };
  }
  getSecurityStatus() {
    return {
      integrityChecks: this.integrityChecks.length,
      debuggingDetected: this.debuggingDetected,
      consoleAccessDetected: this.consoleAccessDetected,
      tamperingDetected: this.tamperingDetected,
      honeypotTriggers: this.honeypotTriggers.length,
      monitoringActive: this.monitoringActive
    };
  }
  startSecurityMonitoring() {
    this.startConsoleMonitoring();
    this.detectConsoleOpening();
    this.checkForObviousTampering();
    this.setupHoneypotTraps();
    this.setupGlobalSecurityEvents();
    this.startContinuousGlobalMonitoring();
    this.monitoringActive = true;
  }
  setupGlobalSecurityEvents() {
    const originalTriggerViolation = this.triggerSecurityViolation.bind(this);
    this.triggerSecurityViolation = (type, message, severity = 'HIGH') => {
      originalTriggerViolation(type, message, severity);
      const securityEvent = new CustomEvent('securityViolation', {
        detail: {
          type,
          message,
          severity,
          timestamp: Date.now(),
          route: window.location.pathname,
          userAgent: navigator.userAgent
        }
      });
      window.dispatchEvent(securityEvent);
    };
  }
  startContinuousGlobalMonitoring() {
  }
  performGlobalSecurityCheck() {
    if (this.consoleAccessDetected) {
      this.triggerSecurityViolation('console_access_detected', 'Console access detected during global monitoring');
    }
    if (this.debuggingDetected) {
      this.triggerSecurityViolation('debugging_detected', 'Debugging detected during global monitoring');
    }
    if (this.scriptInjectionDetected) {
      this.triggerSecurityViolation('script_injection_detected', 'Script injection detected during global monitoring');
    }
  }
}
export const scriptProtector = new ScriptIntegrityProtector();
if (typeof window !== 'undefined') {
  let globalDetectionStarted = false;
  const startGlobalDetection = () => {
    if (globalDetectionStarted) return;
    globalDetectionStarted = true;
    const initializeGlobalDetection = () => {
      if (document.readyState !== 'complete' || !document.body) {
        setTimeout(initializeGlobalDetection, 500);
        return;
      }
      let baselineDimensions = null;
      setTimeout(() => {
        baselineDimensions = {
          innerHeight: window.innerHeight,
          innerWidth: window.innerWidth,
          outerHeight: window.outerHeight,
          outerWidth: window.outerWidth
        };
      }, 2000); // Wait 2 seconds for page to stabilize
      const globalDevToolsCheck = () => {
        if (!baselineDimensions) return; // Not ready yet
        const heightDiff = window.outerHeight - window.innerHeight;
        const widthDiff = window.outerWidth - window.innerWidth;
        const heightThreshold = 400; // Much higher threshold
        const widthThreshold = 400;
        const hasValidDimensions = window.innerHeight > 400 && window.innerWidth > 600;
        const significantHeightChange = Math.abs(window.innerHeight - baselineDimensions.innerHeight) > 250;
        const significantWidthChange = Math.abs(window.innerWidth - baselineDimensions.innerWidth) > 250;
        const aspectRatio = window.innerWidth / window.innerHeight;
        const isReasonableAspectRatio = aspectRatio > 0.4 && aspectRatio < 3.5;
        const currentTime = Date.now();
        const lastResize = window.lastResizeTime || 0;
        const recentResize = currentTime - lastResize < 2000;
        if (hasValidDimensions &&
            isReasonableAspectRatio &&
            !recentResize &&
            (significantHeightChange || significantWidthChange) &&
            (heightDiff > heightThreshold || widthDiff > widthThreshold)) {
          let consoleTimingDetected = false;
          try {
            const before = performance.now();
            console.profile();
            console.profileEnd();
            const after = performance.now();
            if (after - before > 100) {
              consoleTimingDetected = true;
            }
          } catch (e) {
          }
          const extremeRatio = heightDiff > 500 || widthDiff > 500;
          if (consoleTimingDetected || extremeRatio) {
            sessionStorage.setItem('securityViolation', 'true');
            sessionStorage.setItem('violationType', 'global_devtools_detection');
            createSecurityOverlay();
          }
        }
      };
      const createSecurityOverlay = () => {
        if (document.body) {
          document.body.innerHTML = `
            <div style="
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: #000;
              color: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              z-index: 999999;
              font-family: Arial, sans-serif;
              font-size: 18px;
              text-align: center;
            ">
              <div>
                <h2>🚨 Developer Tools Detected</h2>
                <p>Please close developer tools and refresh the page.</p>
                <button onclick="window.location.reload()" style="
                  margin-top: 20px;
                  padding: 10px 20px;
                  background: #ff4444;
                  color: white;
                  border: none;
                  border-radius: 5px;
                  cursor: pointer;
                  font-size: 16px;
                ">Refresh Page</button>
              </div>
            </div>
          `;
        }
      };
    };
    initializeGlobalDetection();
  };
  const hasViolation = sessionStorage.getItem('securityViolation');
  const lastRefresh = sessionStorage.getItem('lastManualRefresh');
  const now = Date.now();
  if (hasViolation && (!lastRefresh || (now - parseInt(lastRefresh)) > 5000)) {
    sessionStorage.removeItem('securityViolation');
    sessionStorage.removeItem('violationType');
    sessionStorage.removeItem('violationTime');
  }
  sessionStorage.setItem('lastManualRefresh', now.toString());
  if (window.aggressiveSecurityInterval) {
    clearInterval(window.aggressiveSecurityInterval);
    window.aggressiveSecurityInterval = null;
  }
  if (window.securityMonitoringInterval) {
    clearInterval(window.securityMonitoringInterval);
    window.securityMonitoringInterval = null;
  }
  startGlobalDetection();
}