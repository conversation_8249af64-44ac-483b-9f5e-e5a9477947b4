import CryptoJS from 'crypto-js';
class HardwareAttestationManager {
  constructor() {
    this.attestationCache = new Map();
    this.cacheTimeout = 10 * 60 * 1000; // 10 minutes
    this.attestationLevel = 'MAXIMUM'; // BASIC, ENHANCED, MAXIMUM
    this.trustedHardwareSignatures = new Set();
    this.suspiciousHardwareSignatures = new Set();
  }
  async performHardwareAttestation(deviceFingerprint) {
    try {
      const attestationId = this.generateAttestationId();
      // Multi-layer hardware verification
      const attestationResults = await Promise.all([
        this.verifyWebGLRenderer(deviceFingerprint),
        this.verifyAudioContext(),
        this.verifyCanvasFingerprint(),
        this.verifyWebRTCFingerprint(),
        this.verifyPerformanceAPI(),
        this.verifyHardwareFeatures(),
        this.verifyTrustedPlatformModule(),
        this.verifySecureEnclaveCapabilities()
      ]);
      const [
        webglResult,
        audioResult,
        canvasResult,
        webrtcResult,
        performanceResult,
        hardwareFeaturesResult,
        tpmResult,
        enclaveResult
      ] = attestationResults;
      // Calculate composite attestation score
      const attestationScore = this.calculateAttestationScore(attestationResults);
      // Determine trust level
      const trustLevel = this.determineTrustLevel(attestationScore, attestationResults);
      // Generate hardware certificate
      const hardwareCertificate = this.generateHardwareCertificate(
        attestationId,
        attestationResults,
        attestationScore,
        trustLevel
      );
      // Cache the attestation
      this.cacheAttestation(attestationId, {
        certificate: hardwareCertificate,
        timestamp: Date.now(),
        trustLevel,
        score: attestationScore
      });
      return {
        attestationId,
        certificate: hardwareCertificate,
        trustLevel,
        attestationScore,
        verified: trustLevel !== 'UNTRUSTED',
        details: {
          webgl: webglResult,
          audio: audioResult,
          canvas: canvasResult,
          webrtc: webrtcResult,
          performance: performanceResult,
          hardwareFeatures: hardwareFeaturesResult,
          tpm: tpmResult,
          enclave: enclaveResult
        },
        recommendations: this.generateAttestationRecommendations(trustLevel, attestationResults)
      };
    } catch (error) {
      return this.fallbackAttestation();
    }
  }
  async verifyWebGLRenderer(deviceFingerprint) {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (!gl) {
        return {
          verified: false,
          reason: 'WebGL not available',
          trustScore: 0.1
        };
      }
      const renderer = gl.getParameter(gl.RENDERER);
      const vendor = gl.getParameter(gl.VENDOR);
      const version = gl.getParameter(gl.VERSION);
      const shadingLanguageVersion = gl.getParameter(gl.SHADING_LANGUAGE_VERSION);
      // Check for known virtualization signatures
      const virtualizationIndicators = [
        'VMware', 'VirtualBox', 'QEMU', 'Parallels', 'Hyper-V',
        'Microsoft Basic Render Driver', 'llvmpipe', 'softpipe'
      ];
      const isVirtualized = virtualizationIndicators.some(indicator => 
        renderer.toLowerCase().includes(indicator.toLowerCase()) ||
        vendor.toLowerCase().includes(indicator.toLowerCase())
      );
      // Verify renderer consistency with device fingerprint
      const rendererConsistent = deviceFingerprint.components?.gpuRenderer?.includes(renderer);
      const trustScore = this.calculateWebGLTrustScore(
        renderer, vendor, version, isVirtualized, rendererConsistent
      );
      return {
        verified: trustScore > 0.6,
        renderer,
        vendor,
        version,
        shadingLanguageVersion,
        isVirtualized,
        rendererConsistent,
        trustScore,
        reason: isVirtualized ? 'Virtualized environment detected' : 'Hardware renderer verified'
      };
    } catch (error) {
      return {
        verified: false,
        reason: 'WebGL verification failed',
        error: error.message,
        trustScore: 0.2
      };
    }
  }
  async verifyAudioContext() {
    try {
      if (!window.AudioContext && !window.webkitAudioContext) {
        return {
          verified: false,
          reason: 'Audio context not available',
          trustScore: 0.3
        };
      }
      const AudioContextClass = window.AudioContext || window.webkitAudioContext;
      const audioContext = new AudioContextClass();
      // Test audio hardware capabilities
      const sampleRate = audioContext.sampleRate;
      const baseLatency = audioContext.baseLatency || 0;
      const outputLatency = audioContext.outputLatency || 0;
      // Create oscillator to test audio processing
      const oscillator = audioContext.createOscillator();
      const analyser = audioContext.createAnalyser();
      oscillator.connect(analyser);
      const bufferLength = analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
      oscillator.start();
      oscillator.stop(audioContext.currentTime + 0.1);
      analyser.getByteFrequencyData(dataArray);
      // Calculate audio fingerprint
      const audioFingerprint = CryptoJS.SHA256(dataArray.toString()).toString();
      await audioContext.close();
      const trustScore = this.calculateAudioTrustScore(sampleRate, baseLatency, outputLatency);
      return {
        verified: trustScore > 0.5,
        sampleRate,
        baseLatency,
        outputLatency,
        audioFingerprint,
        trustScore,
        reason: 'Audio hardware verified'
      };
    } catch (error) {
      return {
        verified: false,
        reason: 'Audio verification failed',
        error: error.message,
        trustScore: 0.2
      };
    }
  }
  async verifyCanvasFingerprint() {
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      // Draw complex pattern for hardware-specific rendering
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillStyle = '#f60';
      ctx.fillRect(125, 1, 62, 20);
      ctx.fillStyle = '#069';
      ctx.fillText('Hardware Attestation Test 🔒', 2, 15);
      ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
      ctx.fillText('Hardware Attestation Test 🔒', 4, 17);
      // Add geometric shapes
      ctx.globalCompositeOperation = 'multiply';
      ctx.fillStyle = 'rgb(255,0,255)';
      ctx.beginPath();
      ctx.arc(50, 50, 50, 0, Math.PI * 2, true);
      ctx.closePath();
      ctx.fill();
      const canvasData = canvas.toDataURL();
      const canvasFingerprint = CryptoJS.SHA256(canvasData).toString();
      // Verify canvas capabilities
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const pixelVariation = this.calculatePixelVariation(imageData.data);
      const trustScore = this.calculateCanvasTrustScore(pixelVariation, canvasData.length);
      return {
        verified: trustScore > 0.6,
        canvasFingerprint,
        pixelVariation,
        dataLength: canvasData.length,
        trustScore,
        reason: 'Canvas hardware rendering verified'
      };
    } catch (error) {
      return {
        verified: false,
        reason: 'Canvas verification failed',
        error: error.message,
        trustScore: 0.2
      };
    }
  }
  async verifyWebRTCFingerprint() {
    try {
      if (!window.RTCPeerConnection) {
        return {
          verified: false,
          reason: 'WebRTC not available',
          trustScore: 0.3
        };
      }
      const pc = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
      });
      // Get local IP addresses and network interfaces
      const localIPs = [];
      const networkInterfaces = [];
      return new Promise((resolve) => {
        pc.createDataChannel('');
        pc.createOffer().then(offer => pc.setLocalDescription(offer));
        pc.onicecandidate = (event) => {
          if (event.candidate) {
            const candidate = event.candidate.candidate;
            const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
            if (ipMatch) {
              localIPs.push(ipMatch[1]);
            }
            // Extract network interface information
            const interfaceMatch = candidate.match(/typ\s+(\w+)/);
            if (interfaceMatch) {
              networkInterfaces.push(interfaceMatch[1]);
            }
          } else {
            // ICE gathering complete
            pc.close();
            const uniqueIPs = [...new Set(localIPs)];
            const uniqueInterfaces = [...new Set(networkInterfaces)];
            const webrtcFingerprint = CryptoJS.SHA256(
              uniqueIPs.join(',') + uniqueInterfaces.join(',')
            ).toString();
            const trustScore = this.calculateWebRTCTrustScore(uniqueIPs, uniqueInterfaces);
            resolve({
              verified: trustScore > 0.5,
              localIPs: uniqueIPs,
              networkInterfaces: uniqueInterfaces,
              webrtcFingerprint,
              trustScore,
              reason: 'WebRTC network fingerprint verified'
            });
          }
        };
        // Timeout after 5 seconds
        setTimeout(() => {
          pc.close();
          resolve({
            verified: false,
            reason: 'WebRTC verification timeout',
            trustScore: 0.3
          });
        }, 5000);
      });
    } catch (error) {
      return {
        verified: false,
        reason: 'WebRTC verification failed',
        error: error.message,
        trustScore: 0.2
      };
    }
  }
  async verifyPerformanceAPI() {
    try {
      if (!window.performance) {
        return {
          verified: false,
          reason: 'Performance API not available',
          trustScore: 0.3
        };
      }
      const timing = performance.timing;
      const navigation = performance.navigation;
      const memory = performance.memory;
      // Test high-resolution timing
      const start = performance.now();
      await new Promise(resolve => setTimeout(resolve, 10));
      const end = performance.now();
      const timingPrecision = end - start;
      // Get performance entries
      const entries = performance.getEntriesByType('navigation');
      const resourceEntries = performance.getEntriesByType('resource');
      const performanceFingerprint = CryptoJS.SHA256(
        JSON.stringify({
          timingPrecision: Math.round(timingPrecision * 1000),
          memoryUsed: memory ? memory.usedJSHeapSize : 0,
          navigationTiming: timing ? timing.loadEventEnd - timing.navigationStart : 0
        })
      ).toString();
      const trustScore = this.calculatePerformanceTrustScore(
        timingPrecision, memory, entries.length
      );
      return {
        verified: trustScore > 0.5,
        timingPrecision,
        memoryInfo: memory ? {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit
        } : null,
        navigationEntries: entries.length,
        resourceEntries: resourceEntries.length,
        performanceFingerprint,
        trustScore,
        reason: 'Performance API verified'
      };
    } catch (error) {
      return {
        verified: false,
        reason: 'Performance API verification failed',
        error: error.message,
        trustScore: 0.2
      };
    }
  }
  async verifyHardwareFeatures() {
    try {
      const features = {
        deviceMemory: navigator.deviceMemory || 'unknown',
        hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
        maxTouchPoints: navigator.maxTouchPoints || 0,
        platform: navigator.platform,
        userAgent: navigator.userAgent,
        languages: navigator.languages,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
        doNotTrack: navigator.doNotTrack
      };
      // Check for hardware-specific capabilities
      const hasGamepad = 'getGamepads' in navigator;
      const hasVibration = 'vibrate' in navigator;
      const hasBattery = 'getBattery' in navigator;
      const hasGeolocation = 'geolocation' in navigator;
      const hardwareScore = this.calculateHardwareFeatureScore(features, {
        hasGamepad, hasVibration, hasBattery, hasGeolocation
      });
      const hardwareFingerprint = CryptoJS.SHA256(JSON.stringify(features)).toString();
      return {
        verified: hardwareScore > 0.6,
        features,
        capabilities: { hasGamepad, hasVibration, hasBattery, hasGeolocation },
        hardwareFingerprint,
        trustScore: hardwareScore,
        reason: 'Hardware features verified'
      };
    } catch (error) {
      return {
        verified: false,
        reason: 'Hardware features verification failed',
        error: error.message,
        trustScore: 0.2
      };
    }
  }
  async verifyTrustedPlatformModule() {
    try {
      // Check for TPM-like capabilities through Web Crypto API
      if (!window.crypto || !window.crypto.subtle) {
        return {
          verified: false,
          reason: 'Web Crypto API not available',
          trustScore: 0.2
        };
      }
      // Test cryptographic capabilities
      const keyPair = await window.crypto.subtle.generateKey(
        {
          name: 'RSA-PSS',
          modulusLength: 2048,
          publicExponent: new Uint8Array([1, 0, 1]),
          hash: 'SHA-256'
        },
        false,
        ['sign', 'verify']
      );
      // Test signing capability
      const data = new TextEncoder().encode('TPM attestation test');
      const signature = await window.crypto.subtle.sign(
        {
          name: 'RSA-PSS',
          saltLength: 32
        },
        keyPair.privateKey,
        data
      );
      // Verify signature
      const verified = await window.crypto.subtle.verify(
        {
          name: 'RSA-PSS',
          saltLength: 32
        },
        keyPair.publicKey,
        signature,
        data
      );
      const trustScore = verified ? 0.8 : 0.2;
      return {
        verified: verified && trustScore > 0.5,
        cryptoCapabilities: true,
        signatureVerified: verified,
        trustScore,
        reason: verified ? 'Cryptographic attestation verified' : 'Cryptographic verification failed'
      };
    } catch (error) {
      return {
        verified: false,
        reason: 'TPM verification failed',
        error: error.message,
        trustScore: 0.1
      };
    }
  }
  async verifySecureEnclaveCapabilities() {
    try {
      // Check for secure enclave-like capabilities
      const hasCredentialsAPI = 'credentials' in navigator;
      const hasWebAuthn = hasCredentialsAPI && 'create' in navigator.credentials;
      if (!hasWebAuthn) {
        return {
          verified: false,
          reason: 'WebAuthn not available',
          trustScore: 0.3
        };
      }
      // Test WebAuthn capability (without actually creating credentials)
      const isWebAuthnSupported = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
      const trustScore = isWebAuthnSupported ? 0.9 : 0.4;
      return {
        verified: isWebAuthnSupported,
        webAuthnSupported: isWebAuthnSupported,
        platformAuthenticator: isWebAuthnSupported,
        trustScore,
        reason: isWebAuthnSupported ? 'Secure enclave capabilities verified' : 'Limited secure capabilities'
      };
    } catch (error) {
      return {
        verified: false,
        reason: 'Secure enclave verification failed',
        error: error.message,
        trustScore: 0.2
      };
    }
  }
  calculateAttestationScore(attestationResults) {
    const weights = {
      webgl: 0.20,
      audio: 0.15,
      canvas: 0.15,
      webrtc: 0.15,
      performance: 0.10,
      hardwareFeatures: 0.10,
      tpm: 0.10,
      enclave: 0.05
    };
    const scores = attestationResults.map(result => result.trustScore || 0);
    const weightValues = Object.values(weights);
    return scores.reduce((total, score, index) => {
      return total + (score * weightValues[index]);
    }, 0);
  }
  determineTrustLevel(attestationScore, attestationResults) {
    if (attestationScore >= 0.8) return 'TRUSTED';
    if (attestationScore >= 0.6) return 'VERIFIED';
    if (attestationScore >= 0.4) return 'SUSPICIOUS';
    return 'UNTRUSTED';
  }
  generateHardwareCertificate(attestationId, attestationResults, score, trustLevel) {
    const certificateData = {
      attestationId,
      timestamp: Date.now(),
      score,
      trustLevel,
      version: '1.0',
      issuer: 'Project-Madara-Hardware-Attestation'
    };
    const certificateHash = CryptoJS.SHA256(JSON.stringify(certificateData)).toString();
    return {
      ...certificateData,
      certificateHash,
      signature: this.signCertificate(certificateData)
    };
  }
  signCertificate(certificateData) {
    // Simple HMAC signature for certificate
    const secret = 'hardware-attestation-secret-key';
    return CryptoJS.HmacSHA256(JSON.stringify(certificateData), secret).toString();
  }
  generateAttestationId() {
    return 'attest-' + Date.now() + '-' + Math.random().toString(36).substring(2);
  }
  cacheAttestation(attestationId, attestationData) {
    this.attestationCache.set(attestationId, attestationData);
    // Auto-cleanup after timeout
    setTimeout(() => {
      this.attestationCache.delete(attestationId);
    }, this.cacheTimeout);
  }
  generateAttestationRecommendations(trustLevel, attestationResults) {
    const recommendations = [];
    switch (trustLevel) {
      case 'UNTRUSTED':
        recommendations.push({
          action: 'BLOCK',
          reason: 'Hardware attestation failed',
          severity: 'HIGH'
        });
        break;
      case 'SUSPICIOUS':
        recommendations.push({
          action: 'CHALLENGE',
          reason: 'Hardware attestation suspicious',
          severity: 'MEDIUM'
        });
        break;
      case 'VERIFIED':
        recommendations.push({
          action: 'MONITOR',
          reason: 'Hardware partially verified',
          severity: 'LOW'
        });
        break;
      case 'TRUSTED':
        recommendations.push({
          action: 'ALLOW',
          reason: 'Hardware fully attested',
          severity: 'NONE'
        });
        break;
    }
    return recommendations;
  }
  // Helper calculation methods
  calculateWebGLTrustScore(renderer, vendor, version, isVirtualized, rendererConsistent) {
    let score = 0.5;
    if (!isVirtualized) score += 0.3;
    if (rendererConsistent) score += 0.2;
    if (renderer && !renderer.includes('Software')) score += 0.1;
    return Math.min(score, 1.0);
  }
  calculateAudioTrustScore(sampleRate, baseLatency, outputLatency) {
    let score = 0.5;
    if (sampleRate >= 44100) score += 0.2;
    if (baseLatency > 0) score += 0.2;
    if (outputLatency > 0) score += 0.1;
    return Math.min(score, 1.0);
  }
  calculateCanvasTrustScore(pixelVariation, dataLength) {
    let score = 0.5;
    if (pixelVariation > 100) score += 0.3;
    if (dataLength > 5000) score += 0.2;
    return Math.min(score, 1.0);
  }
  calculateWebRTCTrustScore(localIPs, networkInterfaces) {
    let score = 0.5;
    if (localIPs.length > 0) score += 0.3;
    if (networkInterfaces.length > 1) score += 0.2;
    return Math.min(score, 1.0);
  }
  calculatePerformanceTrustScore(timingPrecision, memory, entriesCount) {
    let score = 0.5;
    if (timingPrecision > 5 && timingPrecision < 20) score += 0.2;
    if (memory && memory.usedJSHeapSize > 0) score += 0.2;
    if (entriesCount > 0) score += 0.1;
    return Math.min(score, 1.0);
  }
  calculateHardwareFeatureScore(features, capabilities) {
    let score = 0.3;
    if (features.deviceMemory !== 'unknown') score += 0.2;
    if (features.hardwareConcurrency > 1) score += 0.2;
    if (Object.values(capabilities).some(Boolean)) score += 0.3;
    return Math.min(score, 1.0);
  }
  calculatePixelVariation(pixelData) {
    let variation = 0;
    for (let i = 0; i < pixelData.length - 4; i += 4) {
      const r1 = pixelData[i], g1 = pixelData[i + 1], b1 = pixelData[i + 2];
      const r2 = pixelData[i + 4], g2 = pixelData[i + 5], b2 = pixelData[i + 6];
      variation += Math.abs(r1 - r2) + Math.abs(g1 - g2) + Math.abs(b1 - b2);
    }
    return variation;
  }
  fallbackAttestation() {
    return {
      attestationId: 'fallback-' + Date.now(),
      certificate: null,
      trustLevel: 'UNKNOWN',
      attestationScore: 0.3,
      verified: false,
      details: { fallback: true },
      recommendations: [{ action: 'MONITOR', reason: 'Hardware attestation unavailable' }]
    };
  }
}
// Global instance
export const hardwareAttestationManager = new HardwareAttestationManager();
// Export for use in other modules
export { HardwareAttestationManager };
