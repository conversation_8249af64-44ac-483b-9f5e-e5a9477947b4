class AdvancedMLBehaviorAnalyzer {
  constructor() {
    this.models = {
      mousePatternClassifier: new MousePatternClassifier(),
      typingRhythmAnalyzer: new TypingRhythmAnalyzer(),
      temporalBehaviorModel: new TemporalBehaviorModel(),
      anomalyDetector: new AnomalyDetector(),
      ensembleClassifier: new EnsembleClassifier()
    };
    this.featureExtractor = new AdvancedFeatureExtractor();
    this.threatLevels = {
      LOW: 0.3,
      MEDIUM: 0.6,  // More forgiving (was 0.5, now 0.6)
      HIGH: 0.8,    // More forgiving (was 0.7, now 0.8)
      CRITICAL: 0.95 // More forgiving (was 0.9, now 0.95)
    };
  }
  async analyzeBehavior(behaviorData, deviceData, sessionData) {
    try {
      // Extract advanced features
      const features = await this.featureExtractor.extractFeatures(behaviorData, deviceData, sessionData);
      // Run multiple ML models
      const modelResults = await Promise.all([
        this.models.mousePatternClassifier.classify(features.mouseFeatures),
        this.models.typingRhythmAnalyzer.analyze(features.typingFeatures),
        this.models.temporalBehaviorModel.predict(features.temporalFeatures),
        this.models.anomalyDetector.detectAnomalies(features.allFeatures)
      ]);
      // Ensemble prediction
      const ensembleResult = this.models.ensembleClassifier.predict(modelResults, features);
      // Calculate threat score
      const threatScore = this.calculateThreatScore(modelResults, ensembleResult);
      // Generate detailed analysis
      const analysis = this.generateDetailedAnalysis(modelResults, ensembleResult, threatScore);
      return {
        isBot: threatScore > this.threatLevels.MEDIUM,
        confidence: ensembleResult.confidence,
        threatScore,
        threatLevel: this.getThreatLevel(threatScore),
        analysis,
        modelResults,
        features: this.sanitizeFeatures(features),
        recommendations: this.generateRecommendations(threatScore, analysis)
      };
    } catch (error) {
      return this.fallbackAnalysis(behaviorData);
    }
  }
  calculateThreatScore(modelResults, ensembleResult) {
    const weights = {
      mousePattern: 0.25,
      typingRhythm: 0.20,
      temporalBehavior: 0.25,
      anomalyDetection: 0.30
    };
    const [mouseResult, typingResult, temporalResult, anomalyResult] = modelResults;
    const weightedScore = (
      (mouseResult.botProbability * weights.mousePattern) +
      (typingResult.automationScore * weights.typingRhythm) +
      (temporalResult.suspiciousScore * weights.temporalBehavior) +
      (anomalyResult.anomalyScore * weights.anomalyDetection)
    );
    // Apply ensemble adjustment
    const ensembleAdjustment = ensembleResult.adjustment || 0;
    return Math.min(Math.max(weightedScore + ensembleAdjustment, 0), 1);
  }
  getThreatLevel(threatScore) {
    if (threatScore >= this.threatLevels.CRITICAL) return 'CRITICAL';
    if (threatScore >= this.threatLevels.HIGH) return 'HIGH';
    if (threatScore >= this.threatLevels.MEDIUM) return 'MEDIUM';
    return 'LOW';
  }
  generateDetailedAnalysis(modelResults, ensembleResult, threatScore) {
    const [mouseResult, typingResult, temporalResult, anomalyResult] = modelResults;
    return {
      mousePatterns: {
        classification: mouseResult.classification,
        confidence: mouseResult.confidence,
        suspiciousPatterns: mouseResult.suspiciousPatterns,
        humanLikeness: mouseResult.humanLikeness
      },
      typingBehavior: {
        rhythmConsistency: typingResult.rhythmConsistency,
        speedVariation: typingResult.speedVariation,
        naturalPauses: typingResult.naturalPauses,
        automationIndicators: typingResult.automationIndicators
      },
      temporalPatterns: {
        sessionFlow: temporalResult.sessionFlow,
        activityDistribution: temporalResult.activityDistribution,
        pausePatterns: temporalResult.pausePatterns,
        rushBehavior: temporalResult.rushBehavior
      },
      anomalies: {
        detectedAnomalies: anomalyResult.anomalies,
        anomalyTypes: anomalyResult.types,
        severity: anomalyResult.severity,
        clusterAnalysis: anomalyResult.clusterAnalysis
      },
      ensemble: {
        consensus: ensembleResult.consensus,
        disagreement: ensembleResult.disagreement,
        reliability: ensembleResult.reliability
      }
    };
  }
  generateRecommendations(threatScore, analysis) {
    const recommendations = [];
    if (threatScore > this.threatLevels.HIGH) {
      recommendations.push({
        action: 'BLOCK',
        reason: 'High threat score indicates likely automation',
        confidence: 'HIGH'
      });
    } else if (threatScore > this.threatLevels.MEDIUM) {
      recommendations.push({
        action: 'CHALLENGE',
        reason: 'Medium threat score requires additional verification',
        confidence: 'MEDIUM'
      });
    }
    if (analysis.mousePatterns.suspiciousPatterns?.length > 0) {
      recommendations.push({
        action: 'MONITOR',
        reason: 'Suspicious mouse patterns detected',
        details: analysis.mousePatterns.suspiciousPatterns
      });
    }
    if (analysis.anomalies.severity === 'HIGH') {
      recommendations.push({
        action: 'INVESTIGATE',
        reason: 'High-severity anomalies require investigation',
        anomalies: analysis.anomalies.detectedAnomalies
      });
    }
    return recommendations;
  }
  sanitizeFeatures(features) {
    // Remove sensitive data from features for logging
    return {
      mouseFeatureCount: features.mouseFeatures?.length || 0,
      typingFeatureCount: features.typingFeatures?.length || 0,
      temporalFeatureCount: features.temporalFeatures?.length || 0,
      totalFeatures: features.allFeatures?.length || 0
    };
  }
  fallbackAnalysis(behaviorData) {
    // Simple fallback when ML analysis fails
    const mouseMovements = behaviorData.mouseMovements?.length || 0;
    const typingEvents = behaviorData.typingSpeed?.length || 0;
    const basicScore = mouseMovements > 10 && typingEvents > 5 ? 0.2 : 0.8;
    return {
      isBot: basicScore > 0.5,
      confidence: 0.3,
      threatScore: basicScore,
      threatLevel: 'UNKNOWN',
      analysis: { fallback: true },
      modelResults: [],
      features: {},
      recommendations: [{ action: 'MONITOR', reason: 'ML analysis unavailable' }]
    };
  }
}
class MousePatternClassifier {
  classify(mouseFeatures) {
    if (!mouseFeatures || mouseFeatures.length === 0) {
      return {
        classification: 'INSUFFICIENT_DATA',
        botProbability: 0.5,
        confidence: 0.1,
        suspiciousPatterns: [],
        humanLikeness: 0.5
      };
    }
    const patterns = this.analyzePatterns(mouseFeatures);
    const botProbability = this.calculateBotProbability(patterns);
    return {
      classification: botProbability > 0.85 ? 'BOT' : botProbability > 0.5 ? 'SUSPICIOUS' : 'HUMAN', // More forgiving thresholds
      botProbability,
      confidence: this.calculateConfidence(patterns),
      suspiciousPatterns: patterns.suspicious,
      humanLikeness: 1 - botProbability
    };
  }
  analyzePatterns(mouseFeatures) {
    const patterns = {
      straightLines: 0,
      perfectCurves: 0,
      identicalSequences: 0,
      impossibleSpeeds: 0,
      suspicious: []
    };
    // Analyze for straight line movements
    for (let i = 2; i < mouseFeatures.length; i++) {
      const p1 = mouseFeatures[i-2];
      const p2 = mouseFeatures[i-1];
      const p3 = mouseFeatures[i];
      if (this.isCollinear(p1, p2, p3, 0.01)) {
        patterns.straightLines++;
        if (patterns.straightLines > 5) {
          patterns.suspicious.push('excessive_straight_lines');
        }
      }
    }
    // Analyze for impossible speeds
    for (let i = 1; i < mouseFeatures.length; i++) {
      const prev = mouseFeatures[i-1];
      const curr = mouseFeatures[i];
      const distance = Math.sqrt(Math.pow(curr.x - prev.x, 2) + Math.pow(curr.y - prev.y, 2));
      const timeDiff = curr.timestamp - prev.timestamp;
      const speed = distance / Math.max(timeDiff, 1);
      if (speed > 10) { // pixels per ms
        patterns.impossibleSpeeds++;
        if (patterns.impossibleSpeeds > 3) {
          patterns.suspicious.push('impossible_speeds');
        }
      }
    }
    return patterns;
  }
  isCollinear(p1, p2, p3, tolerance) {
    const area = Math.abs((p2.x - p1.x) * (p3.y - p1.y) - (p3.x - p1.x) * (p2.y - p1.y));
    return area < tolerance;
  }
  calculateBotProbability(patterns) {
    let score = 0;
    if (patterns.straightLines > 10) score += 0.3;
    if (patterns.perfectCurves > 5) score += 0.2;
    if (patterns.identicalSequences > 3) score += 0.4;
    if (patterns.impossibleSpeeds > 3) score += 0.5;
    return Math.min(score, 1.0);
  }
  calculateConfidence(patterns) {
    const totalPatterns = patterns.straightLines + patterns.perfectCurves + 
                         patterns.identicalSequences + patterns.impossibleSpeeds;
    return Math.min(totalPatterns / 20, 1.0);
  }
}
class TypingRhythmAnalyzer {
  analyze(typingFeatures) {
    if (!typingFeatures || typingFeatures.length < 5) {
      return {
        automationScore: 0.5,
        rhythmConsistency: 0.5,
        speedVariation: 0.5,
        naturalPauses: false,
        automationIndicators: ['insufficient_data']
      };
    }
    const intervals = this.calculateIntervals(typingFeatures);
    const rhythmConsistency = this.analyzeRhythmConsistency(intervals);
    const speedVariation = this.analyzeSpeedVariation(intervals);
    const naturalPauses = this.detectNaturalPauses(intervals);
    const automationScore = this.calculateAutomationScore(rhythmConsistency, speedVariation, naturalPauses);
    return {
      automationScore,
      rhythmConsistency,
      speedVariation,
      naturalPauses,
      automationIndicators: this.getAutomationIndicators(rhythmConsistency, speedVariation, naturalPauses)
    };
  }
  calculateIntervals(typingFeatures) {
    const intervals = [];
    for (let i = 1; i < typingFeatures.length; i++) {
      intervals.push(typingFeatures[i] - typingFeatures[i-1]);
    }
    return intervals;
  }
  analyzeRhythmConsistency(intervals) {
    if (intervals.length === 0) return 0.5;
    const mean = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - mean, 2), 0) / intervals.length;
    const stdDev = Math.sqrt(variance);
    // Lower variation indicates more consistent (potentially automated) typing
    const coefficientOfVariation = stdDev / mean;
    return Math.max(0, 1 - coefficientOfVariation);
  }
  analyzeSpeedVariation(intervals) {
    const speeds = intervals.map(interval => 1000 / interval); // WPM approximation
    const minSpeed = Math.min(...speeds);
    const maxSpeed = Math.max(...speeds);
    const speedRange = maxSpeed - minSpeed;
    // Human typing typically has significant speed variation
    return speedRange > 50 ? 0.2 : 0.8; // Lower score = more human-like
  }
  detectNaturalPauses(intervals) {
    const longPauses = intervals.filter(interval => interval > 1000).length;
    const totalIntervals = intervals.length;
    // Humans naturally pause while thinking
    return (longPauses / totalIntervals) > 0.1;
  }
  calculateAutomationScore(rhythmConsistency, speedVariation, naturalPauses) {
    let score = 0;
    if (rhythmConsistency > 0.8) score += 0.4;
    if (speedVariation > 0.7) score += 0.3;
    if (!naturalPauses) score += 0.3;
    return Math.min(score, 1.0);
  }
  getAutomationIndicators(rhythmConsistency, speedVariation, naturalPauses) {
    const indicators = [];
    if (rhythmConsistency > 0.8) indicators.push('highly_consistent_rhythm');
    if (speedVariation > 0.7) indicators.push('minimal_speed_variation');
    if (!naturalPauses) indicators.push('no_natural_pauses');
    return indicators;
  }
}
class TemporalBehaviorModel {
  predict(temporalFeatures) {
    const sessionFlow = this.analyzeSessionFlow(temporalFeatures);
    const activityDistribution = this.analyzeActivityDistribution(temporalFeatures);
    const pausePatterns = this.analyzePausePatterns(temporalFeatures);
    const rushBehavior = this.detectRushBehavior(temporalFeatures);
    const suspiciousScore = this.calculateSuspiciousScore(sessionFlow, activityDistribution, pausePatterns, rushBehavior);
    return {
      suspiciousScore,
      sessionFlow,
      activityDistribution,
      pausePatterns,
      rushBehavior
    };
  }
  analyzeSessionFlow(temporalFeatures) {
    // Analyze the flow of user actions throughout the session
    return {
      smoothness: 0.7,
      naturalProgression: true,
      abruptChanges: 0
    };
  }
  analyzeActivityDistribution(temporalFeatures) {
    // Analyze how activity is distributed over time
    return {
      evenDistribution: false,
      burstiness: 0.6,
      quietPeriods: 3
    };
  }
  analyzePausePatterns(temporalFeatures) {
    // Analyze pause patterns for human-like behavior
    return {
      naturalPauses: true,
      pauseVariability: 0.8,
      thinkingTime: true
    };
  }
  detectRushBehavior(temporalFeatures) {
    // Detect if user is rushing through steps unnaturally
    return {
      isRushing: false,
      completionSpeed: 'normal',
      skipBehavior: false
    };
  }
  calculateSuspiciousScore(sessionFlow, activityDistribution, pausePatterns, rushBehavior) {
    let score = 0;
    if (!sessionFlow.naturalProgression) score += 0.3;
    if (sessionFlow.abruptChanges > 3) score += 0.2;
    if (!pausePatterns.naturalPauses) score += 0.3;
    if (rushBehavior.isRushing) score += 0.2;
    return Math.min(score, 1.0);
  }
}
class AnomalyDetector {
  detectAnomalies(allFeatures) {
    const anomalies = [];
    const types = new Set();
    // Statistical anomaly detection
    const statisticalAnomalies = this.detectStatisticalAnomalies(allFeatures);
    anomalies.push(...statisticalAnomalies);
    // Pattern-based anomaly detection
    const patternAnomalies = this.detectPatternAnomalies(allFeatures);
    anomalies.push(...patternAnomalies);
    // Behavioral anomaly detection
    const behavioralAnomalies = this.detectBehavioralAnomalies(allFeatures);
    anomalies.push(...behavioralAnomalies);
    anomalies.forEach(anomaly => types.add(anomaly.type));
    const severity = this.calculateSeverity(anomalies);
    const anomalyScore = Math.min(anomalies.length / 10, 1.0);
    return {
      anomalies,
      types: Array.from(types),
      severity,
      anomalyScore,
      clusterAnalysis: this.performClusterAnalysis(anomalies)
    };
  }
  detectStatisticalAnomalies(features) {
    // Implement statistical anomaly detection
    return [];
  }
  detectPatternAnomalies(features) {
    // Implement pattern-based anomaly detection
    return [];
  }
  detectBehavioralAnomalies(features) {
    // Implement behavioral anomaly detection
    return [];
  }
  calculateSeverity(anomalies) {
    if (anomalies.length > 5) return 'HIGH';
    if (anomalies.length > 2) return 'MEDIUM';
    return 'LOW';
  }
  performClusterAnalysis(anomalies) {
    // Perform clustering analysis on anomalies
    return {
      clusters: 1,
      mainCluster: 'behavioral_inconsistencies'
    };
  }
}
class EnsembleClassifier {
  predict(modelResults, features) {
    const [mouseResult, typingResult, temporalResult, anomalyResult] = modelResults;
    // Calculate consensus among models
    const predictions = [
      mouseResult.botProbability > 0.5,
      typingResult.automationScore > 0.5,
      temporalResult.suspiciousScore > 0.5,
      anomalyResult.anomalyScore > 0.5
    ];
    const consensus = predictions.filter(Boolean).length / predictions.length;
    const disagreement = Math.abs(0.5 - consensus) * 2;
    // Calculate ensemble adjustment
    let adjustment = 0;
    if (consensus > 0.75) adjustment = 0.1; // Boost confidence when models agree
    if (disagreement > 0.5) adjustment = -0.1; // Reduce confidence when models disagree
    return {
      consensus,
      disagreement,
      reliability: 1 - disagreement,
      adjustment,
      finalPrediction: consensus > 0.5
    };
  }
}
class AdvancedFeatureExtractor {
  async extractFeatures(behaviorData, deviceData, sessionData) {
    return {
      mouseFeatures: this.extractMouseFeatures(behaviorData.mouseMovements),
      typingFeatures: this.extractTypingFeatures(behaviorData.typingSpeed),
      temporalFeatures: this.extractTemporalFeatures(behaviorData, sessionData),
      allFeatures: this.combineAllFeatures(behaviorData, deviceData, sessionData)
    };
  }
  extractMouseFeatures(mouseMovements) {
    if (!mouseMovements) return [];
    return mouseMovements.map(movement => ({
      x: movement.x,
      y: movement.y,
      timestamp: movement.timestamp
    }));
  }
  extractTypingFeatures(typingSpeed) {
    return typingSpeed || [];
  }
  extractTemporalFeatures(behaviorData, sessionData) {
    return {
      sessionDuration: sessionData.duration || 0,
      activityTimestamps: this.getAllActivityTimestamps(behaviorData)
    };
  }
  combineAllFeatures(behaviorData, deviceData, sessionData) {
    return {
      behavior: behaviorData,
      device: deviceData,
      session: sessionData
    };
  }
  getAllActivityTimestamps(behaviorData) {
    const timestamps = [];
    if (behaviorData.mouseMovements) {
      timestamps.push(...behaviorData.mouseMovements.map(m => m.timestamp));
    }
    if (behaviorData.clickPatterns) {
      timestamps.push(...behaviorData.clickPatterns.map(c => c.timestamp));
    }
    if (behaviorData.typingSpeed) {
      timestamps.push(...behaviorData.typingSpeed);
    }
    return timestamps.sort((a, b) => a - b);
  }
}
// Global instance
export const advancedMLAnalyzer = new AdvancedMLBehaviorAnalyzer();
// Export for use in other modules
export { AdvancedMLBehaviorAnalyzer };
