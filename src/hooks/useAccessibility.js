import { useEffect, useRef, useCallback, useState } from 'react';
import { a11y, keyboard } from '../lib/utils';
// Hook for managing focus
export const useFocus = (shouldFocus = false, deps = []) => {
  const elementRef = useRef(null);
  useEffect(() => {
    if (shouldFocus && elementRef.current) {
      elementRef.current.focus();
    }
  }, [shouldFocus, ...deps]);
  return elementRef;
};
// Hook for managing announcements to screen readers
export const useAnnouncement = () => {
  const announce = useCallback((message, priority = 'polite') => {
    a11y.announce(message, priority);
  }, []);
  return announce;
};
// Hook for managing keyboard navigation
export const useKeyboardNavigation = (items, options = {}) => {
  const {
    orientation = 'vertical',
    loop = true,
    onActivate,
    initialIndex = 0
  } = options;
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const itemRefs = useRef([]);
  const handleKeyDown = useCallback((event) => {
    const { key } = event;
    let newIndex = currentIndex;
    switch (key) {
      case keyboard.keys.ARROW_DOWN:
        if (orientation === 'vertical') {
          newIndex = loop 
            ? (currentIndex + 1) % items.length
            : Math.min(currentIndex + 1, items.length - 1);
          event.preventDefault();
        }
        break;
      case keyboard.keys.ARROW_UP:
        if (orientation === 'vertical') {
          newIndex = loop
            ? currentIndex === 0 ? items.length - 1 : currentIndex - 1
            : Math.max(currentIndex - 1, 0);
          event.preventDefault();
        }
        break;
      case keyboard.keys.ARROW_RIGHT:
        if (orientation === 'horizontal') {
          newIndex = loop 
            ? (currentIndex + 1) % items.length
            : Math.min(currentIndex + 1, items.length - 1);
          event.preventDefault();
        }
        break;
      case keyboard.keys.ARROW_LEFT:
        if (orientation === 'horizontal') {
          newIndex = loop
            ? currentIndex === 0 ? items.length - 1 : currentIndex - 1
            : Math.max(currentIndex - 1, 0);
          event.preventDefault();
        }
        break;
      case keyboard.keys.HOME:
        newIndex = 0;
        event.preventDefault();
        break;
      case keyboard.keys.END:
        newIndex = items.length - 1;
        event.preventDefault();
        break;
      case keyboard.keys.ENTER:
      case keyboard.keys.SPACE:
        if (onActivate) {
          onActivate(currentIndex, items[currentIndex]);
          event.preventDefault();
        }
        break;
    }
    if (newIndex !== currentIndex) {
      setCurrentIndex(newIndex);
      itemRefs.current[newIndex]?.focus();
    }
  }, [currentIndex, items, orientation, loop, onActivate]);
  const getItemProps = useCallback((index) => ({
    ref: (el) => {
      itemRefs.current[index] = el;
    },
    tabIndex: index === currentIndex ? 0 : -1,
    onKeyDown: handleKeyDown,
    'aria-selected': index === currentIndex,
  }), [currentIndex, handleKeyDown]);
  return {
    currentIndex,
    setCurrentIndex,
    getItemProps,
    handleKeyDown
  };
};
// Hook for managing focus trap
export const useFocusTrap = (active = false, containerRef) => {
  const previousActiveElement = useRef(null);
  useEffect(() => {
    if (!active || !containerRef.current) return;
    // Store the currently focused element
    previousActiveElement.current = document.activeElement;
    // Set up focus trap
    const cleanup = a11y.trapFocus(containerRef.current);
    return () => {
      cleanup();
      // Restore focus to previous element
      if (previousActiveElement.current) {
        previousActiveElement.current.focus();
      }
    };
  }, [active, containerRef]);
};
// Hook for managing reduced motion preferences
export const useReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);
    const handleChange = (event) => {
      setPrefersReducedMotion(event.matches);
    };
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);
  return prefersReducedMotion;
};
// Hook for managing high contrast preferences
export const useHighContrast = () => {
  const [prefersHighContrast, setPrefersHighContrast] = useState(false);
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    setPrefersHighContrast(mediaQuery.matches);
    const handleChange = (event) => {
      setPrefersHighContrast(event.matches);
    };
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);
  return prefersHighContrast;
};
// Hook for managing touch target validation
export const useTouchTarget = () => {
  const validateTouchTargets = useCallback(() => {
    const interactiveElements = document.querySelectorAll(
      'button, a, input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const invalidTargets = [];
    interactiveElements.forEach((element) => {
      if (!a11y.isTouchTargetValid(element)) {
        invalidTargets.push(element);
      }
    });
    if (invalidTargets.length > 0) {
    }
    return invalidTargets;
  }, []);
  return { validateTouchTargets };
};
// Hook for managing ARIA live regions
export const useLiveRegion = (initialMessage = '') => {
  const [message, setMessage] = useState(initialMessage);
  const liveRegionRef = useRef(null);
  const announce = useCallback((newMessage, priority = 'polite') => {
    setMessage(newMessage);
    if (liveRegionRef.current) {
      liveRegionRef.current.setAttribute('aria-live', priority);
    }
  }, []);
  const clear = useCallback(() => {
    setMessage('');
  }, []);
  return {
    message,
    announce,
    clear,
    liveRegionRef
  };
};
export default {
  useFocus,
  useAnnouncement,
  useKeyboardNavigation,
  useFocusTrap,
  useReducedMotion,
  useHighContrast,
  useTouchTarget,
  useLiveRegion
};
