import { useState, useEffect } from 'react';
import { responsive } from '../lib/utils';
// Hook for managing responsive breakpoints
export const useBreakpoint = () => {
  const [breakpoint, setBreakpoint] = useState(() => responsive.getCurrentBreakpoint());
  const [windowSize, setWindowSize] = useState(() => ({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0
  }));
  useEffect(() => {
    const handleResize = () => {
      const newWidth = window.innerWidth;
      const newHeight = window.innerHeight;
      setWindowSize({ width: newWidth, height: newHeight });
      setBreakpoint(responsive.getCurrentBreakpoint());
    };
    // Throttle resize events for better performance
    let timeoutId = null;
    const throttledResize = () => {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(handleResize, 100);
    };
    window.addEventListener('resize', throttledResize);
    return () => {
      window.removeEventListener('resize', throttledResize);
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, []);
  return {
    breakpoint,
    windowSize,
    isMobile: responsive.isMobile(),
    isTablet: responsive.isTablet(),
    isDesktop: responsive.isDesktop(),
    isXs: breakpoint === 'xs',
    isSm: breakpoint === 'sm',
    isMd: breakpoint === 'md',
    isLg: breakpoint === 'lg',
    isXl: breakpoint === 'xl',
    is2Xl: breakpoint === '2xl'
  };
};
// Hook for managing media queries
export const useMediaQuery = (query) => {
  const [matches, setMatches] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.matchMedia(query).matches;
    }
    return false;
  });
  useEffect(() => {
    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);
    const handleChange = (event) => {
      setMatches(event.matches);
    };
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [query]);
  return matches;
};
// Hook for managing orientation
export const useOrientation = () => {
  const [orientation, setOrientation] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
    }
    return 'portrait';
  });
  useEffect(() => {
    const handleOrientationChange = () => {
      setOrientation(window.innerHeight > window.innerWidth ? 'portrait' : 'landscape');
    };
    window.addEventListener('resize', handleOrientationChange);
    window.addEventListener('orientationchange', handleOrientationChange);
    return () => {
      window.removeEventListener('resize', handleOrientationChange);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);
  return {
    orientation,
    isPortrait: orientation === 'portrait',
    isLandscape: orientation === 'landscape'
  };
};
// Hook for managing touch device detection
export const useTouchDevice = () => {
  const [isTouchDevice, setIsTouchDevice] = useState(() => {
    if (typeof window !== 'undefined') {
      return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }
    return false;
  });
  useEffect(() => {
    const checkTouchDevice = () => {
      setIsTouchDevice('ontouchstart' in window || navigator.maxTouchPoints > 0);
    };
    // Check on mount and when the window gains focus
    checkTouchDevice();
    window.addEventListener('focus', checkTouchDevice);
    return () => {
      window.removeEventListener('focus', checkTouchDevice);
    };
  }, []);
  return isTouchDevice;
};
// Hook for managing container queries (when supported)
export const useContainerQuery = (containerRef, query) => {
  const [matches, setMatches] = useState(false);
  useEffect(() => {
    if (!containerRef.current) return;
    const container = containerRef.current;
    // Fallback for browsers without container query support
    const checkQuery = () => {
      const rect = container.getBoundingClientRect();
      // Simple width-based container queries
      if (query.includes('min-width')) {
        const minWidth = parseInt(query.match(/min-width:\s*(\d+)px/)?.[1] || '0');
        setMatches(rect.width >= minWidth);
      } else if (query.includes('max-width')) {
        const maxWidth = parseInt(query.match(/max-width:\s*(\d+)px/)?.[1] || '9999');
        setMatches(rect.width <= maxWidth);
      }
    };
    // Use ResizeObserver for better performance
    if (window.ResizeObserver) {
      const resizeObserver = new ResizeObserver(checkQuery);
      resizeObserver.observe(container);
      return () => {
        resizeObserver.disconnect();
      };
    } else {
      // Fallback to window resize
      checkQuery();
      window.addEventListener('resize', checkQuery);
      return () => {
        window.removeEventListener('resize', checkQuery);
      };
    }
  }, [containerRef, query]);
  return matches;
};
// Hook for managing responsive values
export const useResponsiveValue = (values) => {
  const { breakpoint } = useBreakpoint();
  // Values should be an object like { xs: 1, sm: 2, md: 3, lg: 4, xl: 5, '2xl': 6 }
  const breakpointOrder = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];
  const currentIndex = breakpointOrder.indexOf(breakpoint);
  // Find the appropriate value for current breakpoint or closest smaller one
  for (let i = currentIndex; i >= 0; i--) {
    const bp = breakpointOrder[i];
    if (values[bp] !== undefined) {
      return values[bp];
    }
  }
  // Fallback to the first available value
  return values[breakpointOrder.find(bp => values[bp] !== undefined)] || values.default;
};
export default {
  useBreakpoint,
  useMediaQuery,
  useOrientation,
  useTouchDevice,
  useContainerQuery,
  useResponsiveValue
};
