// Key system config
export const GET_KEY_URL = import.meta.env.VITE_GET_KEY_URL || '/generate-key?step=1';
export const GENERATE_KEY_URL = import.meta.env.VITE_GENERATE_KEY_URL || '/generate-key?step=2';
// Lootlabs configuration
export const LOOTLABS_CONFIG = {
  API_BASE_URL: 'https://creators.lootlabs.gg/api/public',
  CONTENT_LOCKER_ENDPOINT: '/content_locker',
  URL_ENCRYPTOR_ENDPOINT: '/url_encryptor',
  TIERS: {
    MCPEDL_SAFE: 1,
    GAMING_OFFERS: 2,
    PROFIT_MAX: 3
  },
  THEMES: {
    CLASSIC: 1,
    SIMS: 2,
    MINECRAFT: 3,
    GTA: 4,
    SPACE: 5
  },
  MAX_TASKS: 5,
  MIN_TASKS: 1
};