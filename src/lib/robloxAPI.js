/**
 * Extracts the Universe ID from a Roblox game URL.
 * @param {string} url - The Roblox game URL (e.g., https://www.roblox.com/games/123456789/Game-Name).
 * @returns {string|null} The Universe ID if found, otherwise null.
 */
export const extractUniverseIdFromUrl = (url) => {
  try {
    const match = url.match(/roblox\.com\/games\/(\\d+)/);
    return match ? match[1] : null;
  } catch (error) {
    return null;
  }
};
/**
 * Fetches a Roblox game thumbnail using the Universe ID.
 * Currently provides a placeholder/mock URL for design purposes.
 * In a real application, this would make an actual API call to Roblox.
 * @param {string} universeId - The Roblox Universe ID.
 * @returns {string} A URL to a placeholder thumbnail image.
 */
export const getGameThumbnail = (universeId) => {
  // In a real scenario, you would use an API call like:
  // const thumbnailUrl = `https://thumbnails.roblox.com/v1/assets?assetIds=${universeId}&size=420x420&format=Png`;
  // return thumbnailUrl;
  // For now, return a placeholder based on the ID for visual consistency
  const placeholders = [
    `https://via.placeholder.com/420x420/FF5733/FFFFFF?text=Script+${universeId.substring(0, 4)}`,
    `https://via.placeholder.com/420x420/33FF57/FFFFFF?text=Game+${universeId.substring(0, 4)}`,
    `https://via.placeholder.com/420x420/3357FF/FFFFFF?text=Roblox+${universeId.substring(0, 4)}`,
    `https://via.placeholder.com/420x420/FF33FF/FFFFFF?text=Script+${universeId.substring(0, 4)}`,
  ];
  const index = parseInt(universeId.slice(-1), 10) % placeholders.length;
  return placeholders[index];
}; 