// Utility function to combine class names
export function cn(...args) {
  return args.filter(Boolean).join(' ');
}
// Accessibility utilities
export const a11y = {
  // Generate unique IDs for form elements
  generateId: (prefix = 'element') => `${prefix}-${Math.random().toString(36).substr(2, 9)}`,
  // Check if element meets minimum touch target size (44x44px)
  isTouchTargetValid: (element) => {
    if (!element) return false;
    const rect = element.getBoundingClientRect();
    return rect.width >= 44 && rect.height >= 44;
  },
  // Announce to screen readers
  announce: (message, priority = 'polite') => {
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', priority);
    announcer.setAttribute('aria-atomic', 'true');
    announcer.className = 'sr-only';
    announcer.textContent = message;
    document.body.appendChild(announcer);
    setTimeout(() => {
      document.body.removeChild(announcer);
    }, 1000);
  },
  // Trap focus within an element
  trapFocus: (element) => {
    const focusableElements = element.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    const handleTabKey = (e) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      }
    };
    element.addEventListener('keydown', handleTabKey);
    return () => element.removeEventListener('keydown', handleTabKey);
  }
};
// Responsive design utilities
export const responsive = {
  // Breakpoints
  breakpoints: {
    xs: '320px',
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px'
  },
  // Check if device is mobile
  isMobile: () => window.innerWidth < 768,
  // Check if device is tablet
  isTablet: () => window.innerWidth >= 768 && window.innerWidth < 1024,
  // Check if device is desktop
  isDesktop: () => window.innerWidth >= 1024,
  // Get current breakpoint
  getCurrentBreakpoint: () => {
    const width = window.innerWidth;
    if (width < 640) return 'xs';
    if (width < 768) return 'sm';
    if (width < 1024) return 'md';
    if (width < 1280) return 'lg';
    if (width < 1536) return 'xl';
    return '2xl';
  }
};
// Color contrast utilities
export const contrast = {
  // Calculate relative luminance
  getLuminance: (hex) => {
    const rgb = parseInt(hex.slice(1), 16);
    const r = (rgb >> 16) & 0xff;
    const g = (rgb >> 8) & 0xff;
    const b = (rgb >> 0) & 0xff;
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  },
  // Calculate contrast ratio between two colors
  getContrastRatio: (color1, color2) => {
    const lum1 = contrast.getLuminance(color1);
    const lum2 = contrast.getLuminance(color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    return (brightest + 0.05) / (darkest + 0.05);
  },
  // Check if contrast meets WCAG AA standards
  meetsWCAG_AA: (color1, color2, isLargeText = false) => {
    const ratio = contrast.getContrastRatio(color1, color2);
    return isLargeText ? ratio >= 3 : ratio >= 4.5;
  },
  // Check if contrast meets WCAG AAA standards
  meetsWCAG_AAA: (color1, color2, isLargeText = false) => {
    const ratio = contrast.getContrastRatio(color1, color2);
    return isLargeText ? ratio >= 4.5 : ratio >= 7;
  }
};
// Keyboard navigation utilities
export const keyboard = {
  // Common key codes
  keys: {
    ENTER: 'Enter',
    SPACE: ' ',
    ESCAPE: 'Escape',
    TAB: 'Tab',
    ARROW_UP: 'ArrowUp',
    ARROW_DOWN: 'ArrowDown',
    ARROW_LEFT: 'ArrowLeft',
    ARROW_RIGHT: 'ArrowRight',
    HOME: 'Home',
    END: 'End'
  },
  // Handle keyboard activation (Enter/Space)
  handleActivation: (callback) => (event) => {
    if (event.key === keyboard.keys.ENTER || event.key === keyboard.keys.SPACE) {
      event.preventDefault();
      callback(event);
    }
  },
  // Handle arrow key navigation
  handleArrowNavigation: (elements, currentIndex, callback) => (event) => {
    let newIndex = currentIndex;
    switch (event.key) {
      case keyboard.keys.ARROW_UP:
      case keyboard.keys.ARROW_LEFT:
        newIndex = currentIndex > 0 ? currentIndex - 1 : elements.length - 1;
        break;
      case keyboard.keys.ARROW_DOWN:
      case keyboard.keys.ARROW_RIGHT:
        newIndex = currentIndex < elements.length - 1 ? currentIndex + 1 : 0;
        break;
      case keyboard.keys.HOME:
        newIndex = 0;
        break;
      case keyboard.keys.END:
        newIndex = elements.length - 1;
        break;
      default:
        return;
    }
    event.preventDefault();
    callback(newIndex);
    elements[newIndex]?.focus();
  }
};