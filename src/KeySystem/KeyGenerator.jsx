import React, { useState, useEffect, useCallback, useRef } from 'react';
import { <PERSON><PERSON>ey, <PERSON>Clock, FiCheckCircle, FiAlertCircle, FiExternalLink, FiCopy } from 'react-icons/fi';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { cn } from '../lib/utils';
import { getUnbreakableFingerprint } from '../utils/deviceFingerprint';
import { getBehaviorData } from '../utils/behaviorTracker';

import { mlAnalyzer } from '../utils/mlBehaviorAnalysis';
import { advancedMLAnalyzer } from '../utils/advancedMLAnalyzer';
import { scriptProtector } from '../utils/scriptIntegrity';
import { secureFetch } from '../utils/requestSigning';
import { useLocation, useNavigate } from 'react-router-dom';
import { lootlabsAnalytics } from '../utils/lootlabsAnalytics';
import { lootlabsSecurity } from '../utils/lootlabsSecurity';
import { dynamicCampaignSelector } from '../utils/dynamicCampaignSelector';
import { keyManager } from '../utils/keyManager';
const ERROR_MESSAGES = {
  vm: 'Virtual machines and automated environments are not supported. Please use a real device.',
  suspicious: 'Your environment appears to be suspicious. Please try from a regular device.',
  behavior: 'Suspicious activity detected. Please try again with normal user behavior.',
  tooFast: 'Please take your time to complete the process naturally.',
  campaign: 'Campaign data not loaded. Please try again in a moment.',
  fetchCampaigns: 'Failed to load campaigns',
  copy: 'Failed to copy key. Please copy manually.'
};
const KeyGenerator = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const sessionIdRef = useRef(null);
  const [sessionId, _setSessionId] = useState(null);
  const setSessionId = useCallback((newId) => {
    sessionIdRef.current = newId;
    _setSessionId(newId);
    setDebugInfo((prev) => ({ ...prev, sessionId: newId || '' }));
  }, []);
  const [currentStep, setCurrentStep] = useState(1);
  const [, setCampaigns] = useState([]);
  const [selectedCampaign, setSelectedCampaign] = useState(null);
  const [step1Completed, setStep1Completed] = useState(false);
  const [step2Completed, setStep2Completed] = useState(false);
  const [generatedKey, setGeneratedKey] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [copied, setCopied] = useState(false);
  const [, setDebugInfo] = useState({});
  const [isSessionFetching, setIsSessionFetching] = useState(false);
  const [hasCheckedSession, setHasCheckedSession] = useState(false);
  const [sessionResetCount, setSessionResetCount] = useState(0);
  const keyGeneratedRef = useRef(false);
  const [lootlabsLinks, setLootlabsLinks] = useState({ step1_url: null, step2_url: null });
  const [isCreatingLinks, setIsCreatingLinks] = useState(false);
  const [, setPendingStep] = useState(null);
  const [, setStepClickTime] = useState(null);
  const [userBehavior, setUserBehavior] = useState({
    mouseMovement: 0,
    keyboardActivity: 0,
    scrollActivity: 0,
    focusEvents: 0,
    startTime: Date.now()
  });
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const maxRetries = 3;
  useEffect(() => {
    let mouseCount = 0;
    let keyCount = 0;
    let scrollCount = 0;
    let focusCount = 0;
    const handleMouseMove = () => {
      mouseCount++;
      setUserBehavior(prev => ({ ...prev, mouseMovement: mouseCount }));
    };
    const handleKeyPress = () => {
      keyCount++;
      setUserBehavior(prev => ({ ...prev, keyboardActivity: keyCount }));
    };
    const handleScroll = () => {
      scrollCount++;
      setUserBehavior(prev => ({ ...prev, scrollActivity: scrollCount }));
    };
    const handleFocus = () => {
      focusCount++;
      setUserBehavior(prev => ({ ...prev, focusEvents: focusCount }));
    };
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('keypress', handleKeyPress);
    document.addEventListener('scroll', handleScroll);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleFocus);
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('keypress', handleKeyPress);
      document.removeEventListener('scroll', handleScroll);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleFocus);
    };
  }, []);
  useEffect(() => {
    const storedPendingStep = sessionStorage.getItem('pendingStep');
    const storedClickTime = sessionStorage.getItem('stepClickTime');
    if (storedPendingStep && storedClickTime) {
      setPendingStep(parseInt(storedPendingStep));
      setStepClickTime(parseInt(storedClickTime));
    }
  }, []);
  const fetchCampaigns = useCallback(async () => {
    try {
      const response = await fetch('/.netlify/functions/get-campaigns');
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to parse error response' }));
        throw new Error(errorData.message || 'Failed to fetch campaigns');
      }
      const data = await response.json();
      if (data && data.length > 0) {
        setCampaigns(data);
        const userCharacteristics = {
          deviceType: /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ? 'mobile' : 'desktop',
          userAgent: navigator.userAgent,
          language: navigator.language || 'en',
          timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          sessionHistory: JSON.parse(sessionStorage.getItem('sessionHistory') || '[]'),
          previousConversions: parseInt(sessionStorage.getItem('previousConversions') || '0'),
          averageSessionDuration: parseInt(sessionStorage.getItem('averageSessionDuration') || '0')
        };
        const optimalCampaign = await dynamicCampaignSelector.selectOptimalCampaign(data, userCharacteristics);
        setSelectedCampaign(optimalCampaign);
        setError(null);
      } else {
        setError('No active campaigns found.');
        setCampaigns([]);
        setSelectedCampaign(null);
      }
    } catch (err) {
      setError(ERROR_MESSAGES.fetchCampaigns);
      setCampaigns([]);
      setSelectedCampaign(null);
    }
  }, []);
  const markStepComplete = useCallback(async (step, token, retryAttempt = 0) => {
    if (!sessionIdRef.current) {
      return;
    }
    if (!token) {
      setError('Missing verification token. Please complete the required step.');
      return;
    }
    setDebugInfo((prev) => ({ ...prev, lastToken: token }));
    setIsRetrying(retryAttempt > 0);
    try {
      const isLootlabsReturn = token.includes('manual_step') ||
                              token.includes('lootlabs_return') ||
                              document.referrer.includes('lootlabs') ||
                              document.referrer.includes('lootdest') ||
                              document.referrer.includes('loot-link');
      const params = new URLSearchParams(window.location.search);
      const completedParam = params.get('completed');
      const isCompletionEndpointReturn = completedParam && parseInt(completedParam) === step;
      if (!isCompletionEndpointReturn) {
        const securityValidation = await lootlabsSecurity.validateStepCompletion(
          sessionIdRef.current,
          step,
          {
            startTime: userBehavior.startTime,
            userBehavior,
            referrer: document.referrer,
            isLootlabsReturn: isLootlabsReturn // Pass this to security validation
          }
        );
        const riskThreshold = isLootlabsReturn ? 0.7 : 0.7; // Consistent 70% threshold
        const isSecurityValid = securityValidation.isValid ||
                               (isLootlabsReturn && securityValidation.riskScore <= riskThreshold);
        if (!isSecurityValid) {
          const errorMessage = `Security validation failed. Risk score: ${securityValidation.riskScore.toFixed(2)}`;
          setError(errorMessage);
          lootlabsAnalytics.trackError('security_validation_failed', errorMessage, step, {
            riskScore: securityValidation.riskScore,
            warnings: securityValidation.warnings
          });
          throw new Error(errorMessage);
        }
      }
      const requestBody = {
        action: 'complete',
        step,
        sessionId: sessionIdRef.current,
        token,
        startTime: userBehavior.startTime,
        mouseMovement: userBehavior.mouseMovement,
        keyboardActivity: userBehavior.keyboardActivity,
        scrollActivity: userBehavior.scrollActivity,
        focusEvents: userBehavior.focusEvents,
        retryAttempt
      };
      const res = await fetch('/.netlify/functions/step-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });
      const responseText = await res.text();
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        data = { error: 'Invalid response format', rawResponse: responseText };
      }
      setDebugInfo((prev) => ({ ...prev, stepResponse: data, rawResponse: responseText }));
      if (!res.ok) {
        if (retryAttempt < maxRetries && (res.status === 500 || res.status === 503)) {
          setRetryCount(retryAttempt + 1);
          const delay = Math.pow(2, retryAttempt) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
          return markStepComplete(step, token, retryAttempt + 1);
        }
        const errorMessage = data.error || 'Failed to verify step completion.';
        setError(errorMessage);
        lootlabsAnalytics.trackError('step_verification_failed', errorMessage, step, {
          retryAttempt,
          statusCode: res.status,
          userBehavior
        });
        throw new Error(errorMessage);
      }
      setRetryCount(0);
      setIsRetrying(false);
      lootlabsAnalytics.trackStepCompleted(step, {
        retryAttempt,
        userBehavior
      });
    } catch (err) {
      if (retryAttempt < maxRetries && err.message.includes('network')) {
        setRetryCount(retryAttempt + 1);
        const delay = Math.pow(2, retryAttempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        return markStepComplete(step, token, retryAttempt + 1);
      }
      setError('Failed to verify step due to network error or unrecoverable error.');
      setIsRetrying(false);
      throw err;
    }
  }, [setError, userBehavior, maxRetries]);

  // Function to handle actual key generation - defined early to avoid hoisting issues
  const handleActualKeyGeneration = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // const securityStatus = scriptProtector.getSecurityStatus();
      // if (securityStatus.debuggingDetected || securityStatus.tamperingDetected) {
      //   setError('Security violation detected. Please use a standard browser without debugging tools.');
      //   setLoading(false);
      //   return;
      // }

      const deviceData = await getUnbreakableFingerprint();
      setDebugInfo((prev) => ({ ...prev, deviceData }));

      if (deviceData.vmDetection && deviceData.vmDetection.confidence > 0.75) {
        setError(`Virtual machine detected with ${Math.round(deviceData.vmDetection.confidence * 100)}% confidence`);
        setLoading(false);
        return;
      }

      if (deviceData.integrity < 0.7) {
        setError('Device integrity check failed. Please use a standard browser.');
        setLoading(false);
        return;
      }

      if (deviceData.confidence < 0.8) {
        setError('Device fingerprint confidence too low. Please enable JavaScript fully.');
        setLoading(false);
        return;
      }

      const behaviorData = getBehaviorData();
      setDebugInfo((prev) => ({ ...prev, behaviorData }));

      // const mlAnalysis = await mlAnalyzer.predict(behaviorData);
      // setDebugInfo((prev) => ({ ...prev, mlAnalysis }));
      // const advancedMLAnalysis = await advancedMLAnalyzer.analyzeBehavior(
      //   behaviorData,
      //   deviceData,
      //   {
      //     duration: Date.now() - (behaviorData.sessionStartTime || Date.now() - 30000),
      //     sessionId
      //   }
      // );
      // setDebugInfo((prev) => ({ ...prev, advancedMLAnalysis }));
      // if (mlAnalysis.isBot) {
      //   setError(`Bot behavior detected with ${Math.round(mlAnalysis.confidence * 100)}% confidence (${mlAnalysis.method})`);
      //   setLoading(false);
      //   return;
      // }
      // if (advancedMLAnalysis.isBot) {
      //   setError(`Advanced threat detection: ${advancedMLAnalysis.threatLevel} risk (${Math.round(advancedMLAnalysis.confidence * 100)}% confidence)`);
      //   setLoading(false);
      //   return;
      // }
      // if (advancedMLAnalysis.threatLevel === 'HIGH' || advancedMLAnalysis.threatLevel === 'CRITICAL') {
      // }
      // const updatedSecurityStatus = scriptProtector.getSecurityStatus();
      // setDebugInfo((prev) => ({ ...prev, securityStatus: updatedSecurityStatus }));

      const response = await fetch('/.netlify/functions/generate-key', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          campaignId: selectedCampaign.id,
          deviceData: deviceData,        // Enhanced fingerprint
          behaviorData: behaviorData,
          // mlAnalysis: mlAnalysis,        // Add ML analysis results
          // securityStatus: updatedSecurityStatus, // Add security status
          sessionId
        })
      });

      const data = await response.json();
      if (!response.ok) {
        setError(data.error || 'Failed to generate key.');
        setLoading(false);
        return;
      }

      setGeneratedKey(data);
      setStep2Completed(true);
      setDebugInfo((prev) => ({ ...prev, keyResponse: data }));

      // Store the key using keyManager for secure access
      if (data.key) {
        const hwid = keyManager.generateBrowserHWID();
        keyManager.storeKey(data.key, data.expires_at, hwid);
        console.log('✅ Key stored securely for script access');
      }

      setLoading(false);
    } catch (err) {
      setError(err.message);
      setLoading(false);
    }
  }, [selectedCampaign, sessionId, setLoading, setError, setDebugInfo, setGeneratedKey, setStep2Completed]);

  const cleanUrlParams = useCallback(() => {
    const newParams = new URLSearchParams(location.search);
    let paramsChanged = false;
    const paramsToRemove = ['step', 'hash', 'token', 'verification', 'completed', 'success', 'data'];
    paramsToRemove.forEach(param => {
      if (newParams.has(param)) {
        newParams.delete(param);
        paramsChanged = true;
      }
    });
    if (sessionIdRef.current && !newParams.has('session')) {
      newParams.set('session', sessionIdRef.current);
      paramsChanged = true;
    }
    const currentUrlSearch = location.search;
    const desiredUrlSearch = newParams.toString() ? `?${newParams.toString()}` : '';
    if (paramsChanged && currentUrlSearch !== desiredUrlSearch) {
      navigate(`${location.pathname}${desiredUrlSearch}`, { replace: true });
    }
  }, [sessionIdRef, location.search, location.pathname, navigate]);
  const createLootlabsLinks = useCallback(async () => {
    if (!selectedCampaign || !sessionIdRef.current || lootlabsLinks.step1_url) {
      return;
    }
    setIsCreatingLinks(true);
    try {
      const response = await fetch('/.netlify/functions/create-lootlabs-links', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          campaignId: selectedCampaign.id,
          sessionId: sessionIdRef.current,
          returnUrl: window.location.origin + location.pathname
        })
      });
      const data = await response.json();
      const checkDataParam = (url) => {
        if (url) {
          const urlObj = new URL(url);
          const dataParam = urlObj.searchParams.get('data');
          if (dataParam) {
          } else {
          }
        }
      };
      checkDataParam(data.step1_url, 1);
      checkDataParam(data.step2_url, 2);
      if (!response.ok) {
        throw new Error(data.error || `Failed to create Lootlabs links: ${response.status}`);
      }
      setLootlabsLinks({
        step1_url: data.step1_url,
        step2_url: data.step2_url
      });
      lootlabsAnalytics.trackLinkCreated(1, data.step1_url);
      lootlabsAnalytics.trackLinkCreated(2, data.step2_url);
    } catch (error) {
      setError('Failed to create verification tasks. Please try again.');
    } finally {
      setIsCreatingLinks(false);
    }
  }, [selectedCampaign, lootlabsLinks.step1_url, setError, location.pathname]);
  useEffect(() => {
    if (hasCheckedSession || sessionId) {
      return;
    }
    setIsSessionFetching(true);
    fetch('/.netlify/functions/step-session', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'status' })
    })
      .then(res => res.json())
      .then(data => {
        if (data.sessionId && !data.expired) {
          setSessionId(data.sessionId);
          setStep1Completed(data.step1);
          setStep2Completed(data.step2);
          setHasCheckedSession(true);
          setIsSessionFetching(false);
          return;
        }
        return fetch('/.netlify/functions/step-session', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ action: 'create' })
        });
      })
      .then(res => {
        if (!res) return; // Already handled existing session above
        return res.json();
      })
      .then(data => {
        if (!data) return; // Already handled existing session above
        if (data.sessionId) {
          setSessionId(data.sessionId);
          setStep1Completed(data.step1 || false);
          setStep2Completed(data.step2 || false);
        } else {
          setError(`Failed to start session: ${data.error || 'No session ID returned from backend.'}`);
        }
      })
      .catch(() => {
        setError('Failed to start session. Please try again.');
      })
      .finally(() => {
        setIsSessionFetching(false);
        setHasCheckedSession(true);
      });
  }, [sessionId, hasCheckedSession, isSessionFetching]);
  useEffect(() => {
    if (sessionId) {
      fetchCampaigns();
      // mlAnalyzer.loadModel().catch(() => {
      // });
      // scriptProtector.initialize();
      // setDebugInfo((prev) => ({ ...prev, securityStatus: scriptProtector.getSecurityStatus() }));
      lootlabsAnalytics.initialize(sessionId, selectedCampaign?.id);
      lootlabsSecurity.initializeSession(sessionId, selectedCampaign?.id);
    }
  }, [sessionId, fetchCampaigns, selectedCampaign?.id]);
  useEffect(() => {
    if (selectedCampaign && sessionIdRef.current && !lootlabsLinks.step1_url && !isCreatingLinks && currentStep <= 2) {
      createLootlabsLinks().catch(() => {
      });
    }
  }, [selectedCampaign, lootlabsLinks.step1_url, isCreatingLinks, createLootlabsLinks, currentStep]);
  const fetchSessionStatus = useCallback(async () => {
    if (!sessionIdRef.current) {
      return;
    }
    if (sessionResetCount >= 3) {
      return;
    }
    try {
      const res = await fetch('/.netlify/functions/step-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'status', sessionId: sessionIdRef.current })
      });
      const data = await res.json();
      if (res.ok && data.sessionId) {
        const step1Status = data.step1 || false;
        const step2Status = data.step2 || false;
        setStep1Completed(step1Status);
        setStep2Completed(step2Status);
        setError(null); // Clear any previous errors on successful status fetch
      } else {
        if (data.error && data.error.includes('Session not found') && sessionResetCount < 3) {
          sessionStorage.removeItem('sessionId');
          setSessionId(null);
          setHasCheckedSession(false);
          setIsSessionFetching(false);
          setSessionResetCount(prev => prev + 1);
        } else if (sessionResetCount >= 3) {
          setError('Unable to create session. Please refresh the page manually.');
        }
      }
    } catch (err) {
    }
  }, [setStep1Completed, setStep2Completed, sessionIdRef, setError, sessionResetCount]);
  useEffect(() => {
    if (sessionId) {
      fetchSessionStatus();
    }
  }, [sessionId, fetchSessionStatus]);
  useEffect(() => {
    const generateKeyPending = sessionStorage.getItem('generateKeyPending');
    if (generateKeyPending === 'true' && step1Completed && step2Completed) {
      sessionStorage.removeItem('generateKeyPending');
      handleKeyGeneration();
      return;
    } else if (generateKeyPending === 'true') {
      // Clear the flag if steps aren't completed yet
      sessionStorage.removeItem('generateKeyPending');
    }
    const params = new URLSearchParams(location.search);
    const stepParam = params.get('step');
    const lootlabsDataParam = params.get('data'); // Lootlabs anti-bypass data parameter
    const completedParam = params.get('completed'); // New parameter from completion endpoint
    let parsedStep = 1; // Default to step 1
    if (stepParam) {
      const numStep = parseInt(stepParam);
      if (!isNaN(numStep) && numStep >= 1 && numStep <= 3) {
        parsedStep = numStep;
      }
    }
    if (parsedStep === 1 && step1Completed && !step2Completed) {
      parsedStep = 2; // Move to step 2 if step 1 is completed
    } else if (parsedStep <= 2 && step1Completed && step2Completed) {
      parsedStep = 3; // Move to step 3 if both steps are completed
    }
    const storedPendingStep = sessionStorage.getItem('pendingStep');
    const isLootlabsReturn = sessionId && (
      lootlabsDataParam ||
      completedParam || // NEW: Direct completion detection from our endpoint
      (stepParam && storedPendingStep && parseInt(stepParam) === parseInt(storedPendingStep) + 1) ||
      (stepParam === '2' && !step1Completed) || // Redirected to step 2 but step 1 not completed in UI
      (stepParam === '3' && (!step1Completed || !step2Completed)) || // Redirected to step 3 but steps not completed in UI
      (storedPendingStep && !stepParam && !lootlabsDataParam)
    );
    if (isLootlabsReturn) {
      // If we have a completed parameter, the step was already marked complete by lootlabs-complete.js
      if (completedParam) {
        fetchSessionStatus().then(() => {
          const newParams = new URLSearchParams(location.search);
          newParams.delete('completed');
          const newUrl = `${location.pathname}?${newParams.toString()}`;
          window.history.replaceState({}, '', newUrl);
          // Clear any pending step since it's now complete
          sessionStorage.removeItem('pendingStep');
          sessionStorage.removeItem('stepClickTime');
          sessionStorage.removeItem('expectedLootlabsRedirect');
          setPendingStep(null);
        }).catch(() => {
        });
      } else if (storedPendingStep) {
        // Only try to mark complete if we don't have the completed parameter
        const stepNum = parseInt(storedPendingStep);
        const tokenToUse = lootlabsDataParam || `lootlabs_return_step${stepNum}_${sessionId}_${Date.now()}`; // Use data parameter if available
        sessionStorage.removeItem('pendingStep');
        sessionStorage.removeItem('stepClickTime');
        sessionStorage.removeItem('expectedLootlabsRedirect'); // Ensure this is cleared
        markStepComplete(stepNum, tokenToUse)
          .then(async () => {
            await new Promise(resolve => setTimeout(resolve, 500));
            await fetchSessionStatus();
            const params = new URLSearchParams(location.search);
            params.delete('data');
            params.delete('token');
            const newUrl = `${location.pathname}?${params.toString()}`;
            window.history.replaceState({}, '', newUrl);
            setPendingStep(null);
          })
          .catch(() => {
            setError(`Failed to verify step ${stepNum} from Lootlabs return.`);
          });
      } else {
        cleanUrlParams();
      }
    }
    if (stepParam && sessionId && !isLootlabsReturn) {
      fetchSessionStatus().then(() => {
      }).catch(() => {
      });
    }
    if (sessionId && storedPendingStep && !stepParam && !isLootlabsReturn) {
      const clickTime = parseInt(sessionStorage.getItem('stepClickTime') || '0');
      const timeSinceClick = Date.now() - clickTime;
      if (clickTime > 0 && timeSinceClick < 10 * 60 * 1000) {
        fetchSessionStatus().then(() => {
        }).catch(() => {
        });
      }
    }
    if (currentStep !== parsedStep) {
      if (!step1Completed || (step1Completed && !step2Completed && parsedStep >= 2) || (step1Completed && step2Completed && parsedStep >= 3)) {
        setCurrentStep(parsedStep);
      } else {
      }
    }
  }, [location.search, currentStep, sessionId, markStepComplete, cleanUrlParams, setStep1Completed, setStep2Completed, setError, setPendingStep, fetchSessionStatus, handleActualKeyGeneration]);
  useEffect(() => {
    if (!sessionId) return;
    const checkPendingStepOnMount = () => {
      const storedPendingStep = sessionStorage.getItem('pendingStep');
      const storedClickTime = sessionStorage.getItem('stepClickTime');
      const storedExpectedRedirect = sessionStorage.getItem('expectedLootlabsRedirect');
      if (storedPendingStep && storedClickTime) {
        const clickTime = parseInt(storedClickTime);
        const timeSinceClick = Date.now() - clickTime;
        let hasRedirectedBack = false;
        const navigationType = window.performance.getEntriesByType("navigation")[0]?.type;
        if (navigationType === 'navigate' || navigationType === 'reload' || navigationType === 'back_forward') {
          hasRedirectedBack = true;
        }
        if (document.referrer && storedExpectedRedirect) {
          try {
            const referrerUrl = new URL(document.referrer);
            const expectedRedirectUrl = new URL(storedExpectedRedirect);
            if (referrerUrl.hostname.includes(expectedRedirectUrl.hostname) || document.referrer.includes('loot-link.com')) {
              hasRedirectedBack = true;
            }
          } catch (e) {
          }
        }
        if (hasRedirectedBack && timeSinceClick < 10 * 60 * 1000) {
          const stepNum = parseInt(storedPendingStep);
          sessionStorage.removeItem('pendingStep');
          sessionStorage.removeItem('stepClickTime');
          sessionStorage.removeItem('expectedLootlabsRedirect');
          const token = `lootlabs_secondary_return_step${stepNum}_${sessionId}_${Date.now()}`;
          markStepComplete(stepNum, token)
            .then(async () => {
              await new Promise(resolve => setTimeout(resolve, 500));
              await fetchSessionStatus();
              const params = new URLSearchParams(location.search);
              params.delete('data');
              params.delete('token');
              const newUrl = `${location.pathname}?${params.toString()}`;
              window.history.replaceState({}, '', newUrl);
              setPendingStep(null);
              cleanUrlParams();
            })
            .catch(() => {
              setError(`Failed to verify step ${stepNum} from secondary Lootlabs return.`);
              cleanUrlParams();
            });
        } else if (timeSinceClick > 10 * 60 * 1000) { // Time-based fallback (e.g., 10 minutes)
            sessionStorage.removeItem('pendingStep');
            sessionStorage.removeItem('stepClickTime');
            sessionStorage.removeItem('expectedLootlabsRedirect');
        } else {
          if (timeSinceClick > 5000) { // Clear after 5 seconds if not detected
            sessionStorage.removeItem('pendingStep');
            sessionStorage.removeItem('stepClickTime');
            sessionStorage.removeItem('expectedLootlabsRedirect');
          }
        }
      } else {
      }
    };
    checkPendingStepOnMount();
  }, [sessionId, step1Completed, step2Completed, markStepComplete, cleanUrlParams, setStep1Completed, setStep2Completed, setError, setPendingStep]);
  useEffect(() => {
    if (step1Completed && !step2Completed && currentStep === 1) {
      setCurrentStep(2);
      const params = new URLSearchParams(location.search);
      params.set('step', '2');
      if (sessionId) {
        params.set('session', sessionId);
      }
      params.delete('data');
      params.delete('token');
      const newUrl = `${location.pathname}?${params.toString()}`;
      window.history.replaceState({}, '', newUrl);
    } else if (step1Completed && step2Completed && currentStep !== 3) {
      setCurrentStep(3);
      const params = new URLSearchParams(location.search);
      params.set('step', '3');
      if (sessionId) {
        params.set('session', sessionId);
      }
      params.delete('data');
      params.delete('token');
      const newUrl = `${location.pathname}?${params.toString()}`;
      window.history.replaceState({}, '', newUrl);
    }
  }, [step1Completed, step2Completed, currentStep, sessionId, location.search, location.pathname]);

  const handleKeyGeneration = useCallback(async () => {
    // Only allow key generation if both steps are completed
    if (!step1Completed || !step2Completed) {
      setError('Please complete both verification steps first.');
      return;
    }

    try {
      // Direct key generation
      sessionStorage.setItem('generateKeyPending', 'true');
      sessionStorage.setItem('sessionId', sessionId);
      // Proceed directly to key generation
      await handleActualKeyGeneration();
    } catch (error) {
      setError('Failed to generate key. Please try again.');
    }
  }, [sessionId, step1Completed, step2Completed, handleActualKeyGeneration, setError]);
  const handleRetryChecks = () => {
    setError(null);
    setLoading(false);
    setTimeout(() => {
      keyGeneratedRef.current = false;
    }, 500);
  };
  const handleCopyKey = async () => {
    if (generatedKey?.key) {
      try {
        if (navigator.clipboard) {
          await navigator.clipboard.writeText(generatedKey.key);
        } else {
          // Fallback for older browsers or non-secure contexts
          const textarea = document.createElement('textarea');
          textarea.value = generatedKey.key;
          textarea.style.position = 'fixed';
          textarea.style.left = '-999999px';
          textarea.style.top = '-999999px';
          document.body.appendChild(textarea);
          textarea.focus();
          textarea.select();
          try {
            document.execCommand('copy');
          } catch (err) {
            console.warn('Fallback copy method failed:', err);
          }
          document.body.removeChild(textarea);
        }
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        setCopied(false);
        setError('Failed to copy key. Please copy manually.');
      }
    }
  };
  const formatExpiryTime = (expiresAt) => {
    const expiry = new Date(expiresAt);
    const now = new Date();
    const diff = expiry - now;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };
  const handleStep1Continue = async () => {
    if (!lootlabsLinks.step1_url) {
      setError('Verification tasks not ready. Please try again later.');
      return;
    }
    try {
      const token = crypto.randomUUID();
      const expiresAt = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
      const response = await fetch('/.netlify/functions/store-verification-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId,
          step: 1,
          token,
          expiresAt: expiresAt.toISOString()
        })
      });
      if (!response.ok) {
        throw new Error('Failed to store verification token');
      }
      setPendingStep(1);
      setStepClickTime(Date.now());
      sessionStorage.setItem('pendingStep', '1');
      sessionStorage.setItem('stepClickTime', Date.now().toString());
      sessionStorage.setItem('verificationToken', token);
      sessionStorage.setItem('expectedLootlabsRedirect', lootlabsLinks.step1_url);
      // Direct redirect to verification task
      window.location.href = lootlabsLinks.step1_url;
    } catch (error) {
      setError('Failed to generate verification token. Please try again.');
    }
  };
  const handleStep2Continue = async () => {
    if (!lootlabsLinks.step2_url) {
      setError('Verification tasks not ready. Please try again later.');
      return;
    }
    try {
      const token = crypto.randomUUID();
      const expiresAt = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
      const response = await fetch('/.netlify/functions/store-verification-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId,
          step: 2,
          token,
          expiresAt: expiresAt.toISOString()
        })
      });
      if (!response.ok) {
        throw new Error('Failed to store verification token');
      }
      setPendingStep(2);
      setStepClickTime(Date.now());
      sessionStorage.setItem('pendingStep', '2');
      sessionStorage.setItem('stepClickTime', Date.now().toString());
      sessionStorage.setItem('verificationToken', token);
      sessionStorage.setItem('expectedLootlabsRedirect', lootlabsLinks.step2_url);
      // Direct redirect without ads
      window.location.href = lootlabsLinks.step2_url;
    } catch (error) {
      setError('Failed to generate verification token. Please try again.');
    }
  };
  const handleRestart = () => {
    sessionStorage.removeItem('sessionId');
    setCurrentStep(1);
    setStep1Completed(false);
    setStep2Completed(false);
    setGeneratedKey(null);
    setError(null);
    setCopied(false);
    setSessionId(null);
    setIsSessionFetching(false);
    setHasCheckedSession(false);
    keyGeneratedRef.current = false;
    cleanUrlParams(); // Use cleanUrlParams here
  };
  const handleKeyDown = (e, handler) => {
    if (e.key === 'Enter') {
      handler();
    }
  };
  const handleContinueKeyDown = (e, handler) => {
    if (e.key === 'Enter') {
      handler();
    }
  };
  return (
    <div className="min-h-screen pt-16 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
              Generate License Key
            </h1>
            <p className="text-gray-600 dark:text-gray-400 text-lg">
              Complete the steps below to get your 24-hour valid key
            </p>
          </div>
          {}
          <div className="flex items-center justify-center mb-8 space-x-4">
            {[1, 2, 3].map((step, index) => (
              <React.Fragment key={step}>
                <div className={cn(
                  "flex items-center justify-center w-10 h-10 rounded-full border-2 font-semibold transition-all duration-300",
                  step === 1 && step1Completed ? "bg-success border-success text-success-foreground" :
                  step === 2 && step2Completed ? "bg-success border-success text-success-foreground" :
                  currentStep === step ? "bg-primary border-primary text-primary-foreground" :
                  currentStep > step ? "bg-success border-success text-success-foreground" :
                  "border-border text-muted-foreground"
                )}>
                  {(step === 1 && step1Completed) || (step === 2 && step2Completed) || currentStep > step ?
                    <FiCheckCircle className="w-5 h-5" /> : step}
                </div>
                {index < 2 && (
                  <div className={cn(
                    "w-8 h-0.5 transition-all duration-300",
                    currentStep > step + 1 ? "bg-success" : "bg-border"
                  )} />
                )}
              </React.Fragment>
            ))}
          </div>
          {}
          {currentStep <= 2 && isCreatingLinks && (
            <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div className="flex items-center">
                <FiClock className="animate-spin mr-2 text-blue-500" />
                <span className="text-blue-700 dark:text-blue-300">
                  Preparing verification tasks... Please wait.
                </span>
              </div>
            </div>
          )}
          {currentStep <= 2 && selectedCampaign && lootlabsLinks.step1_url && !isCreatingLinks && (
            <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
              <div className="flex items-center">
                <FiCheckCircle className="mr-2 text-green-500" />
                <span className="text-green-700 dark:text-green-300">
                  Verification tasks ready! Campaign: {selectedCampaign.name}
                </span>
              </div>
            </div>
          )}
          {}
          {currentStep === 1 && (
            <Card className="p-6" role="region" aria-label="Step 1: Complete First Link">
              <div className="text-center mb-6">
                <FiExternalLink className="w-12 h-12 text-blue-500 mx-auto mb-4" aria-hidden="true" />
                <h2 className="text-2xl font-bold mb-2">Step 1: Complete First Task</h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Click the button below to start Step 1. You will be redirected to complete a task, and you will be sent back here to continue.
                </p>
              </div>
              <div className="space-y-4">
                <Button
                  onClick={handleStep1Continue}
                  className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3"
                  ariaLabel="Go to Step 1 Linkvertise"
                  aria-label="Continue to Step 1 Linkvertise"
                  tabIndex={0}
                  onKeyDown={(e) => handleContinueKeyDown(e, handleStep1Continue)}
                  disabled={isCreatingLinks || !lootlabsLinks.step1_url}
                  isLoading={isCreatingLinks}
                >
                  <FiExternalLink className="mr-2" aria-hidden="true" />
                  {isCreatingLinks ? 'Creating Links...' : 'Continue to Step 1'}
                </Button>
              </div>
            </Card>
          )}
          {}
          {currentStep === 2 && (
            <Card className="p-6" role="region" aria-label="Step 2: Complete Second Link">
              {}
              <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <div className="flex items-center">
                  <FiCheckCircle className="mr-2 text-green-500" />
                  <span className="text-green-700 dark:text-green-300 font-medium">
                    Step 1 Completed Successfully!
                  </span>
                </div>
              </div>
              <div className="text-center mb-6">
                <FiExternalLink className="w-12 h-12 text-purple-500 mx-auto mb-4" aria-hidden="true" />
                <h2 className="text-2xl font-bold mb-2">Step 2: Complete Second Task</h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Click the button below to start Step 2. You will be redirected to complete a task, and you will be sent back here to generate your key.
                </p>
              </div>
              <div className="space-y-4">
                <Button
                  onClick={handleStep2Continue}
                  className="w-full bg-purple-500 hover:bg-purple-600 text-white py-3"
                  ariaLabel="Go to Step 2 Linkvertise"
                  aria-label="Continue to Step 2 Linkvertise"
                  tabIndex={0}
                  onKeyDown={(e) => handleContinueKeyDown(e, handleStep2Continue)}
                  disabled={isCreatingLinks || !lootlabsLinks.step2_url}
                  isLoading={isCreatingLinks}
                >
                  <FiExternalLink className="mr-2" aria-hidden="true" />
                  {isCreatingLinks ? 'Creating Links...' : 'Continue to Step 2'}
                </Button>
              </div>
            </Card>
          )}
          {}
          {currentStep === 3 && (
            generatedKey ? (
              <Card className="p-6" role="region" aria-label="Key Generated Successfully">
                <div className="text-center mb-6">
                  <FiKey className="w-12 h-12 text-green-500 mx-auto mb-4" aria-hidden="true" />
                  <h2 className="text-2xl font-bold mb-2 text-green-600">Key Generated Successfully!</h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Your key is valid for 24 hours from now
                  </p>
                </div>
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-4">
                  <div className="flex items-center justify-between">
                    <code className="text-lg font-mono text-gray-900 dark:text-white" aria-label="Your generated key">
                      {generatedKey.key}
                    </code>
                    <Button
                      onClick={handleCopyKey}
                      variant="outline"
                      size="sm"
                      className="ml-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      ariaLabel="Copy key to clipboard"
                      aria-label="Copy key to clipboard"
                      tabIndex={0}
                      onKeyDown={(e) => handleKeyDown(e, handleCopyKey)}
                    >
                      <FiCopy className="w-4 h-4" aria-hidden="true" />
                      {copied ? 'Copied!' : 'Copy'}
                    </Button>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <FiClock className="w-6 h-6 text-blue-500 mx-auto mb-2" aria-hidden="true" />
                    <p className="text-sm text-gray-600 dark:text-gray-400">Expires In</p>
                    <p className="font-semibold text-blue-600 dark:text-blue-400">
                      {formatExpiryTime(generatedKey.expires_at)}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <FiCheckCircle className="w-6 h-6 text-green-500 mx-auto mb-2" aria-hidden="true" />
                    <p className="text-sm text-gray-600 dark:text-gray-400">Status</p>
                    <p className="font-semibold text-green-600 dark:text-green-400">Active</p>
                  </div>
                </div>
                <div className="text-center space-y-3">
                  <p className="text-sm text-gray-500">
                    Use this key in your Roblox script to unlock features
                  </p>
                </div>
              </Card>
            ) : (
              <Card className="p-6 text-center" role="region" aria-label="Final Step: Generate Your Key">
                {}
                <div className="mb-6 space-y-2">
                  <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                    <div className="flex items-center justify-center">
                      <FiCheckCircle className="mr-2 text-green-500" />
                      <span className="text-green-700 dark:text-green-300 font-medium">
                        Step 1 & Step 2 Completed Successfully!
                      </span>
                    </div>
                  </div>
                </div>
                <FiKey className="w-12 h-12 text-blue-500 mx-auto mb-4" aria-hidden="true" />
                <h2 className="text-2xl font-bold mb-2">Final Step: Generate Your Key</h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  All steps completed! Click the button below to generate your 24-hour key.
                </p>
                <Button
                  onClick={handleKeyGeneration}
                  className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  ariaLabel="Generate key after completing both steps"
                  aria-label="Generate key after completing both steps"
                  tabIndex={0}
                  onKeyDown={(e) => handleKeyDown(e, handleKeyGeneration)}
                  disabled={loading}
                >
                  {loading ? 'Generating Key...' : 'Generate Key'}
                </Button>
                {}
                {loading && (
                  <div className="text-center mt-6">
                    <span className="text-gray-500">Generating your key, please wait...</span>
                  </div>
                )}
                {}
                {error && (
                  <div className="bg-red-100 text-red-700 p-4 rounded mb-4 mt-4 flex items-center justify-between" role="alert">
                    <div>
                      <FiAlertCircle className="inline mr-2" />
                      {error}
                    </div>
                    {}
                    <Button
                      onClick={handleRestart}
                      variant="outline"
                      aria-label="Restart key generation"
                      className="ml-4 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      tabIndex={0}
                      onKeyDown={(e) => handleKeyDown(e, handleRestart)}
                    >
                      Restart
                    </Button>
                  </div>
                )}
              </Card>
            )
          )}
          {}
          {sessionResetCount >= 3 && (
            <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-red-700 dark:text-red-300 font-medium">Session Creation Failed</h3>
                  <p className="text-red-600 dark:text-red-400 text-sm">
                    The system couldn't create a session after multiple attempts. This might be a temporary server issue.
                  </p>
                </div>
                <Button
                  onClick={() => window.location.reload()}
                  className="bg-red-500 hover:bg-red-600 text-white px-4 py-2"
                >
                  Refresh Page
                </Button>
              </div>
            </div>
          )}
          {}
          {error && (
            <Card className="p-4 mt-4 border-red-200 bg-red-50 dark:bg-red-900/20" role="alert" aria-live="assertive">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <FiAlertCircle className="w-5 h-5 text-red-500 mr-2" aria-hidden="true" />
                  <span className="text-red-700 dark:text-red-300">{error}</span>
                </div>
                {isRetrying && (
                  <div className="flex items-center text-sm text-orange-600 dark:text-orange-400">
                    <FiClock className="animate-spin w-4 h-4 mr-1" />
                    Retrying... ({retryCount}/{maxRetries})
                  </div>
                )}
              </div>
              {}
              {(error.includes('Virtual machines') || error.includes('suspicious') || error.includes('activity') || error.includes('Please take your time')) && (
                <div className="mt-3 flex flex-col items-start gap-2">
                  <Button onClick={handleRetryChecks} variant="outline" aria-label="Retry device and behavior checks"
                    tabIndex={0}
                    onKeyDown={(e) => handleKeyDown(e, handleRetryChecks)}
                  >Retry check</Button>
                  <a
                    href="mailto:<EMAIL>?subject=Key%20System%20Support%20Request"
                    className="text-blue-600 underline text-sm"
                    tabIndex={0}
                    aria-label="Contact support via email"
                  >
                    Contact Support
                  </a>
                </div>
              )}
              {}
              {error.includes('Failed to load campaigns') && (
                <div className="mt-3">
                  <Button onClick={fetchCampaigns} variant="outline" aria-label="Retry loading campaigns"
                    tabIndex={0}
                    onKeyDown={(e) => handleKeyDown(e, fetchCampaigns)}
                  >Retry Loading Campaigns</Button>
                </div>
              )}
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};
export default KeyGenerator; 