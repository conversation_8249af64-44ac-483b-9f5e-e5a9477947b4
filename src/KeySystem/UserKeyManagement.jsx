import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>ey, <PERSON><PERSON>lock, <PERSON>Eye, FiDownload, FiAlertTriangle, FiRefreshCw } from 'react-icons/fi';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { cn } from '../lib/utils';
const UserKeyManagement = ({ onScrollToGeneration, onKeyUpdate }) => {
  const [loading, setLoading] = useState(true);
  const [keys, setKeys] = useState([]);
  const [stats, setStats] = useState({});
  const [error, setError] = useState(null);
  useEffect(() => {
    loadUserKeys();
  }, []);
  const loadUserKeys = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch('/.netlify/functions/user-key-history', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      if (response.ok) {
        const data = await response.json();
        setKeys(data.keys || []);
        setStats(data.stats || {});
        // Notify parent component about key updates
        if (onKeyUpdate) {
          onKeyUpdate(data.keys || []);
        }
      } else {
        setError('Failed to load your keys');
      }
    } catch (error) {
      setError('Failed to load your keys');
    } finally {
      setLoading(false);
    }
  };
  const handleViewKey = async (keyId) => {
    try {
      // Direct view without ads
      window.open(`/key-viewer/${keyId}`, '_blank');
    } catch (error) {
      alert('Failed to open key viewer');
    }
  };
  const handleDownloadKey = async (keyId) => {
    try {
      // Direct download without ads
      const response = await fetch(`/.netlify/functions/get-key/${keyId}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      if (response.ok) {
        const keyData = await response.json();
        // Create download link
        const blob = new Blob([keyData.key_code], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `madara-key-${keyId}.txt`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert('Failed to download key');
      }
    } catch (error) {
      alert('Failed to download key');
    }
  };
  const maskKey = (keyCode) => {
    if (!keyCode) return '';
    const parts = keyCode.split('-');
    if (parts.length >= 4) {
      return `${parts[0]}-${parts[1]}-****-****`;
    }
    return keyCode.substring(0, 8) + '****';
  };
  const getTimeRemaining = (expiresAt) => {
    const now = new Date();
    const expires = new Date(expiresAt);
    const diff = expires - now;
    if (diff <= 0) return 'Expired';
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}d ${hours % 24}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };
  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-success bg-success/10';
      case 'expired': return 'text-warning bg-warning/10';
      case 'revoked': return 'text-destructive bg-destructive/10';
      default: return 'text-muted-foreground bg-muted';
    }
  };
  const activeKeys = keys.filter(key => key.status === 'active');
  const expiredKeys = keys.filter(key => key.status === 'expired');
  if (loading) {
    return (
      <div className="mb-8">
        <Card className="p-6">
          <div className="flex items-center justify-center">
            <FiRefreshCw className="animate-spin mr-2" />
            <span>Loading your keys...</span>
          </div>
        </Card>
      </div>
    );
  }
  if (error) {
    return (
      <div className="mb-8">
        <Card className="p-6 border-red-200 bg-red-50">
          <div className="flex items-center text-red-600">
            <FiAlertTriangle className="mr-2" />
            <span>{error}</span>
            <Button 
              onClick={loadUserKeys} 
              variant="outline" 
              size="sm" 
              className="ml-4"
            >
              Retry
            </Button>
          </div>
        </Card>
      </div>
    );
  }
  return (
    <div className="mb-8 space-y-6">
      {/* Statistics Summary */}
      <Card className="p-6">
        <h2 className="text-xl font-bold mb-4 flex items-center">
          <FiKey className="mr-2" />
          Your Keys
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{stats.totalKeys || 0}</div>
            <div className="text-sm text-blue-600">Total Generated</div>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{stats.activeKeys || 0}</div>
            <div className="text-sm text-green-600">Active Keys</div>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">{stats.totalDownloads || 0}</div>
            <div className="text-sm text-purple-600">Total Downloads</div>
          </div>
        </div>
        {/* Expired Keys Notification */}
        {expiredKeys.length > 0 && (
          <div className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center text-orange-700">
                <FiAlertTriangle className="mr-2" />
                <span className="font-medium">
                  You have {expiredKeys.length} expired key{expiredKeys.length > 1 ? 's' : ''}
                </span>
              </div>
              <Button
                onClick={() => window.location.href = '/generate-key'}
                size="sm"
                className="bg-orange-600 hover:bg-orange-700"
              >
                Generate New Key
              </Button>
            </div>
          </div>
        )}
      </Card>
      {/* Active Keys List */}
      {activeKeys.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Active Keys</h3>
          <div className="space-y-3">
            {activeKeys.map((key) => (
              <div 
                key={key.id} 
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <div className="font-mono text-sm font-medium">
                      {maskKey(key.key_id)}
                    </div>
                    <span className={cn(
                      'px-2 py-1 rounded-full text-xs font-medium',
                      getStatusColor(key.status)
                    )}>
                      {key.status.charAt(0).toUpperCase() + key.status.slice(1)}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    {key.game_name} - {key.script_name}
                  </div>
                  <div className="flex items-center text-xs text-gray-500 mt-1">
                    <FiClock className="mr-1" />
                    Expires in {getTimeRemaining(key.expires_at)}
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button
                    onClick={() => handleViewKey(key.id)}
                    size="sm"
                    variant="outline"
                    className="flex items-center"
                  >
                    <FiEye className="mr-1" />
                    View
                  </Button>
                  <Button
                    onClick={() => handleDownloadKey(key.id)}
                    size="sm"
                    className="flex items-center"
                  >
                    <FiDownload className="mr-1" />
                    Download
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
      {/* No Keys Message */}
      {keys.length === 0 && (
        <Card className="p-6 text-center">
          <FiKey className="mx-auto text-4xl text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-600 mb-2">No Keys Yet</h3>
          <p className="text-gray-500 mb-4">
            Generate your first key to get started with Project Madara scripts
          </p>
          <Button onClick={() => window.location.href = '/generate-key'}>
            Generate Your First Key
          </Button>
        </Card>
      )}
    </div>
  );
};
export default UserKeyManagement;
