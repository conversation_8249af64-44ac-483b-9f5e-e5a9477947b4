import React, { Suspense } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON>Loader } from 'react-icons/fi';
import { CardSkeleton, FormSkeleton } from '../components/ui/LoadingSkeleton';
// Lazy load key system components
const AdminKeyManagement = React.lazy(() => import('./AdminKeyManagement'));
const KeyGenerator = React.lazy(() => import('./KeyGenerator'));
// Loading component for key system
const KeySystemLoading = ({ type = 'general' }) => (
  <div className="min-h-screen pt-16 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20 relative overflow-hidden">
    {/* Background effects */}
    <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
    <motion.div
      className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-emerald-400/20 to-blue-400/20 rounded-full blur-xl"
      animate={{
        y: [0, -20, 0],
        x: [0, 10, 0],
      }}
      transition={{
        duration: 6,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
    <div className="container mx-auto px-4 py-8 relative z-10">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="mr-3"
            >
              <FiKey className="h-8 w-8 text-emerald-500" />
            </motion.div>
            <h1 className="text-4xl font-bold text-foreground">
              Loading {type === 'admin' ? 'Key Management' : type === 'generator' ? 'Key Generator' : 'Key System'}
            </h1>
          </div>
          <p className="text-foreground/80">Please wait while we load the key system...</p>
        </div>
        {/* Loading skeleton based on type */}
        {type === 'admin' && (
          <div className="space-y-6">
            {/* Stats cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {Array.from({ length: 4 }).map((_, index) => (
                <CardSkeleton 
                  key={index}
                  showImage={false}
                  showBadge={true}
                  className="h-24"
                />
              ))}
            </div>
            {/* Key management table */}
            <CardSkeleton className="h-96" showImage={false} showBadge={false} />
          </div>
        )}
        {type === 'generator' && (
          <div className="max-w-2xl mx-auto">
            <FormSkeleton className="h-64" />
          </div>
        )}
        {type === 'general' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 3 }).map((_, index) => (
              <CardSkeleton 
                key={index}
                showImage={true}
                showBadge={true}
                className="h-48"
              />
            ))}
          </div>
        )}
      </motion.div>
    </div>
  </div>
);
// Error boundary for key system
class KeySystemErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }
  componentDidCatch(error, errorInfo) {
    // Log error for debugging
    console.error('KeySystem Error:', error);
    console.error('Error Info:', errorInfo);
    console.error('Component Stack:', errorInfo.componentStack);
  }
  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20">
          <motion.div
            className="p-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl text-center max-w-md"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="text-red-500 mb-4">
              <FiKey className="h-12 w-12 mx-auto" />
            </div>
            <h2 className="text-xl font-bold text-foreground mb-4">
              Failed to Load Key System
            </h2>
            <p className="text-foreground/80 mb-6">
              There was an error loading the key system. Please try refreshing the page.
            </p>
            {this.props.debug && this.state.error && (
              <div className="bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-800 dark:text-red-300 p-3 rounded-lg text-sm text-left mb-4 overflow-x-auto">
                <p className="font-semibold mb-2">Error Details:</p>
                <code className="block whitespace-pre-wrap break-words">{this.state.error.message}</code>
                {this.state.error.stack && (
                  <details className="mt-2 text-xs cursor-pointer">
                    <summary>Stack Trace</summary>
                    <code className="block whitespace-pre-wrap break-words">{this.state.error.stack}</code>
                  </details>
                )}
              </div>
            )}
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-emerald-500 hover:bg-emerald-600 text-white rounded-lg transition-colors"
            >
              Refresh Page
            </button>
          </motion.div>
        </div>
      );
    }
    return this.props.children;
  }
}
// Lazy admin key management component
export const LazyAdminKeyManagement = (props) => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  return (
    <KeySystemErrorBoundary debug={isDevelopment}>
      <Suspense fallback={<KeySystemLoading type="admin" />}>
        <AdminKeyManagement {...props} />
      </Suspense>
    </KeySystemErrorBoundary>
  );
};
// Lazy key generator component
export const LazyKeyGenerator = (props) => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  return (
    <KeySystemErrorBoundary debug={isDevelopment}>
      <Suspense fallback={<KeySystemLoading type="generator" />}>
        <KeyGenerator {...props} />
      </Suspense>
    </KeySystemErrorBoundary>
  );
};
export default {
  LazyAdminKeyManagement,
  LazyKeyGenerator
};
