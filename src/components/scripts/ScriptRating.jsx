import { useState, useEffect } from 'react';
import { FiStar } from 'react-icons/fi';
import { Button } from '../ui/Button';
import { scriptsAPI } from '../../lib/api';
import { cn } from '../../lib/utils';
const ScriptRating = ({ scriptId, scriptName, currentRating = 0, ratingCount = 0, onRatingChange }) => {
  const [userRating, setUserRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [hasRated, setHasRated] = useState(false);
  // Generate a consistent user identifier (in a real app, this would come from authentication)
  // Store in localStorage to persist across page reloads
  const getUserName = () => {
    let userName = localStorage.getItem('script_rating_user');
    if (!userName) {
      userName = `user_${Math.random().toString(36).substr(2, 9)}`;
      localStorage.setItem('script_rating_user', userName);
    }
    return userName;
  };
  const userName = getUserName();
  useEffect(() => {
    const fetchUserRating = async () => {
      try {
        const ratingData = await scriptsAPI.getRating(scriptId, userName);
        if (ratingData.rating) {
          setUserRating(ratingData.rating);
          setHasRated(true);
        }
      } catch (err) {
      }
    };
    fetchUserRating();
  }, [scriptId, userName]);
  const handleRatingClick = async (rating) => {
    // Prevent rating if user has already rated
    if (hasRated) {
      return;
    }
    setLoading(true);
    setError(null);
    try {
      await scriptsAPI.rate(scriptId, userName, rating);
      setUserRating(rating);
      setHasRated(true);
      // Notify parent component about rating change
      if (onRatingChange) {
        onRatingChange(rating);
      }
    } catch (err) {
      setError('Failed to submit rating');
    } finally {
      setLoading(false);
    }
  };
  const renderStars = () => {
    const stars = [];
    const maxStars = 5;
    const displayRating = hoverRating || userRating;
    for (let i = 1; i <= maxStars; i++) {
      const isUserRated = i <= userRating;
      const isHovered = i <= hoverRating;
      const isCurrentRating = i <= currentRating;
      stars.push(
        <button
          key={i}
          type="button"
          disabled={loading || hasRated}
          onClick={() => handleRatingClick(i)}
          onMouseEnter={() => !hasRated && setHoverRating(i)}
          onMouseLeave={() => setHoverRating(0)}
          className={cn(
            "transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded",
            // User's rating - bright yellow and glowing
            isUserRated && "text-yellow-400 fill-yellow-400 drop-shadow-[0_0_8px_rgba(251,191,36,0.6)]",
            // Hover effect - only if not rated yet
            !hasRated && isHovered && !isUserRated && "text-yellow-300 fill-yellow-300",
            // Current average rating - dimmer yellow
            !isUserRated && !isHovered && isCurrentRating && "text-yellow-400/60 fill-yellow-400/60",
            // Unrated stars
            !isUserRated && !isHovered && !isCurrentRating && "text-gray-500 hover:text-yellow-300",
            // Disabled state when already rated
            hasRated && !isUserRated && "cursor-not-allowed opacity-50"
          )}
          aria-label={hasRated ? `You rated ${scriptName} ${userRating} stars` : `Rate ${scriptName} ${i} star${i !== 1 ? 's' : ''}`}
        >
          <FiStar className="h-4 w-4" />
        </button>
      );
    }
    return stars;
  };
  return (
    <div className="flex flex-col items-start space-y-1">
      <div className="flex items-center space-x-2">
        {renderStars()}
        <span className="text-yellow-400 text-xs">{(currentRating || 0).toFixed(1)}</span>
        <span className="text-gray-500 text-xs">/ 5</span>
        <span className="text-gray-500 text-xs">({ratingCount || 0})</span>
      </div>
      {error && (
        <p className="text-xs text-red-500">{error}</p>
      )}
      {hasRated && (
        <p className="text-xs text-yellow-400 font-medium">
          You rated: {userRating} star{userRating !== 1 ? 's' : ''} ⭐
        </p>
      )}
    </div>
  );
};
export default ScriptRating; 