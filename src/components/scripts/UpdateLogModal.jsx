import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiX, FiClock, FiLoader } from 'react-icons/fi';
import { Button } from '../ui/Button';
import { cn } from '../../lib/utils';
import { scriptsAPI } from '../../lib/api';
const UpdateLogModal = ({ script, onClose }) => {
  const [updateLogs, setUpdateLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  useEffect(() => {
    const fetchUpdateLogs = async () => {
      if (!script?.id) return;
      try {
        setLoading(true);
        setError(null);
        const logs = await scriptsAPI.getUpdateLogs(script.id);
        setUpdateLogs(logs);
      } catch (err) {
        setError('Failed to load update logs');
      } finally {
        setLoading(false);
      }
    };
    fetchUpdateLogs();
  }, [script?.id]);
  const handleCloseModal = () => {
    onClose();
  };
  if (!script) return null;
  return (
    <AnimatePresence>
      <div className={cn("fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm")}>
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          className={cn(
            "relative w-full max-w-3xl max-h-[90vh] overflow-y-auto",
            "bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg shadow-2xl border border-gray-700",
          )}
        >
          <div className={cn("sticky top-0 z-10 flex items-center justify-between px-8 py-6 border-b border-gray-700 bg-gray-900/80 backdrop-blur-sm")}>
            <h2 className={cn("text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-500")}>
              Update Log - {script.name}
            </h2>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={handleCloseModal}
              className={cn("text-gray-400 hover:text-white")}
              ariaLabel="Close update log modal"
            >
              <FiX className={cn("h-6 w-6")} aria-hidden="true" />
            </Button>
          </div>
          <div className={cn("p-8 space-y-8")}>
            {loading && (
              <div className={cn("flex items-center justify-center py-12")}>
                <FiLoader className={cn("animate-spin h-8 w-8 text-blue-500")} />
                <span className={cn("ml-3 text-gray-400")}>Loading update logs...</span>
              </div>
            )}
            {error && (
              <div className={cn("text-center py-12")}>
                <p className={cn("text-red-400")}>{error}</p>
              </div>
            )}
            {!loading && !error && updateLogs.length === 0 && (
              <div className={cn("text-center py-12")}>
                <p className={cn("text-gray-400")}>No update logs available for this script.</p>
              </div>
            )}
            {!loading && !error && updateLogs.length > 0 && (
              <div className={cn("space-y-8 relative")}>
                <div className={cn("absolute left-3.5 top-0 bottom-0 w-1 bg-gray-700 rounded-full")} />
                {updateLogs.map((log, index) => (
                  <div key={log.id} className={cn("relative pl-10")}>
                    <div className={cn("absolute w-4 h-4 rounded-full bg-blue-500 -left-1.5 top-1.5 flex items-center justify-center")} aria-hidden="true">
                      <FiClock className={cn("h-2.5 w-2.5 text-white")} />
                    </div>
                    <div className={cn("flex flex-col")}>
                      <h4 className={cn("text-lg font-semibold text-white")}>v{log.version}</h4>
                      <span className={cn("text-sm text-gray-400")}>
                        <FiClock className={cn("inline-block h-3 w-3 mr-1")} aria-hidden="true" />
                        {new Date(log.updated_at).toLocaleDateString()}
                      </span>
                      {log.updated_by && (
                        <span className={cn("text-xs text-gray-500")}>Updated by: {log.updated_by}</span>
                      )}
                    </div>
                    <div className={cn("mt-3 text-gray-300")}>
                      <p className={cn("whitespace-pre-wrap")}>{log.changes}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className={cn("sticky bottom-0 flex justify-end px-8 py-6 bg-gray-900/80 backdrop-blur-sm border-t border-gray-700")}>
            <Button onClick={handleCloseModal} ariaLabel="Close update log modal" variant="secondary" className={cn("px-6 py-3 text-lg")}>Close</Button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};
export default UpdateLogModal;
