import { useState, useEffect } from 'react';
import { FiPlus, FiX, FiSave, FiTrash2, FiEdit2, FiLoader } from 'react-icons/fi';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Textarea } from '../ui/Textarea';
import { toast } from 'react-hot-toast';
import { cn } from '../../lib/utils';
const UpdateLogManager = ({ scriptId, updateLogs: initialLogs = [], onSave }) => {
  const [updateLogs, setUpdateLogs] = useState(initialLogs);
  const [editingLog, setEditingLog] = useState(null);
  const [newLog, setNewLog] = useState({ version: '', changes: [''] });
  const [isSubmitting, setIsSubmitting] = useState(false);
  // Initialize with current date
  useEffect(() => {
    if (!editingLog && !newLog.date) {
      setNewLog(prev => ({
        ...prev,
        date: new Date().toISOString().split('T')[0]
      }));
    }
  }, [editingLog]);
  const handleAddNewChangeField = () => {
    setNewLog(prev => ({
      ...prev,
      changes: [...prev.changes, '']
    }));
  };
  const handleChangeLogEntry = (index, value) => {
    setNewLog(prev => ({
      ...prev,
      changes: prev.changes.map((change, i) => (i === index ? value : change))
    }));
  };
  const handleRemoveChangeField = (index) => {
    setNewLog(prev => ({
      ...prev,
      changes: prev.changes.filter((_, i) => i !== index)
    }));
  };
  const handleLogSubmit = async (e) => {
    e.preventDefault();
    if (!newLog.version || !newLog.changes.some(change => change.trim())) {
      toast.error('Version and at least one change are required');
      return;
    }
    setIsSubmitting(true);
    try {
      const logToSave = {
        ...newLog,
        date: newLog.date || new Date().toISOString().split('T')[0],
        changes: newLog.changes.filter(change => change.trim())
      };
      const updatedLogs = editingLog
        ? updateLogs.map(log => (log.id === editingLog.id ? logToSave : log))
        : [...updateLogs, { ...logToSave, id: Date.now().toString() }];
      // Call the onSave prop with the updated logs
      await onSave(scriptId, updatedLogs);
      setUpdateLogs(updatedLogs);
      setNewLog({ version: '', changes: [''], date: '' });
      setEditingLog(null);
      toast.success(`Update log ${editingLog ? 'updated' : 'added'} successfully`);
    } catch (error) {
      toast.error(`Failed to ${editingLog ? 'update' : 'add'} update log`);
    } finally {
      setIsSubmitting(false);
    }
  };
  const handleEditLogEntry = (log) => {
    setEditingLog(log);
    setNewLog({
      version: log.version,
      changes: [...log.changes, ''], // Add empty field for new change
      date: log.date
    });
  };
  const handleDeleteLogEntry = async (logId) => {
    if (!window.confirm('Are you sure you want to delete this update log?')) {
      return;
    }
    try {
      const updatedLogs = updateLogs.filter(log => log.id !== logId);
      await onSave(scriptId, updatedLogs);
      setUpdateLogs(updatedLogs);
      toast.success('Update log deleted successfully');
    } catch (error) {
      toast.error('Failed to delete update log');
    }
  };
  return (
    <div className={cn("space-y-6")}>
      <div className={cn("border-b pb-4")}>
        <h3 className={cn("text-lg font-medium")}>Update Logs</h3>
        <p className={cn("text-sm text-muted-foreground")}>
          Track updates and changes to this script
        </p>
      </div>
      <form onSubmit={handleLogSubmit} className={cn("space-y-4")}>
        <div className={cn("grid grid-cols-1 md:grid-cols-3 gap-4")}>
          <div className={cn("")}>
            <label htmlFor="version" className={cn("block text-sm font-medium mb-1")}>
              Version
            </label>
            <Input
              id="version"
              value={newLog.version}
              onChange={(e) => setNewLog(prev => ({ ...prev, version: e.target.value }))}
              placeholder="e.g., 1.0.0"
              required
              aria-label="Script Version"
            />
          </div>
          <div className={cn("")}>
            <label htmlFor="date" className={cn("block text-sm font-medium mb-1")}>
              Date
            </label>
            <Input
              id="date"
              type="date"
              value={newLog.date || ''}
              onChange={(e) => setNewLog(prev => ({ ...prev, date: e.target.value }))}
              required
              aria-label="Update Date"
            />
          </div>
        </div>
        <div className={cn("")}>
          <div className={cn("flex items-center justify-between mb-2")}>
            <label className={cn("block text-sm font-medium")}>Changes</label>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleAddNewChangeField}
              className={cn("text-sm")}
              ariaLabel="Add new change field"
            >
              <FiPlus className={cn("mr-1 h-4 w-4")} aria-hidden="true" /> Add Change
            </Button>
          </div>
          <div className={cn("space-y-2")}>
            {newLog.changes.map((change, index) => (
              <div key={index} className={cn("flex items-start gap-2")}>
                <div className={cn("flex-1")}>
                  <Input
                    value={change}
                    onChange={(e) => handleChangeLogEntry(index, e.target.value)}
                    placeholder={`Change ${index + 1}`}
                    aria-label={`Change entry ${index + 1}`}
                  />
                </div>
                {newLog.changes.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => handleRemoveChangeField(index)}
                    className={cn("text-destructive hover:bg-destructive/10")}
                    ariaLabel={`Remove change entry ${index + 1}`}
                  >
                    <FiX className={cn("h-4 w-4")} aria-hidden="true" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>
        <div className={cn("flex justify-end space-x-2 pt-2")}>
          {editingLog && (
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setEditingLog(null);
                setNewLog({ version: '', changes: [''], date: '' });
              }}
              ariaLabel="Cancel editing update log"
            >
              Cancel
            </Button>
          )}
          <Button 
            type="submit" 
            disabled={isSubmitting}
            ariaLabel={isSubmitting ? (editingLog ? "Updating log..." : "Adding log...") : (editingLog ? "Update Log" : "Add Log")}
          >
            {isSubmitting ? (
              <>
                <FiLoader className={cn("animate-spin mr-2 h-4 w-4")} aria-hidden="true" />
                {editingLog ? 'Updating...' : 'Adding...'}
              </>
            ) : (
              <>
                <FiSave className={cn("mr-2 h-4 w-4")} aria-hidden="true" />
                {editingLog ? 'Update Log' : 'Add Log'}
              </>
            )}
          </Button>
        </div>
      </form>
      <div className={cn("mt-8 space-y-6")}>
        <h3 className={cn("text-lg font-medium")}>Existing Update Logs</h3>
        {updateLogs.length === 0 ? (
          <p className={cn("text-muted-foreground text-sm")}>No update logs available yet.</p>
        ) : (
          <ul className={cn("space-y-4")}>
            {updateLogs.map((log) => (
              <li key={log.id} className={cn("p-4 border rounded-lg bg-card shadow-sm flex flex-col sm:flex-row justify-between sm:items-center gap-3")}>
                <div className={cn("flex-grow")}>
                  <h4 className={cn("font-semibold text-lg")}>Version {log.version} <span className={cn("text-muted-foreground text-sm font-normal")}>({log.date})</span></h4>
                  <ul className={cn("list-disc list-inside text-sm text-foreground/80 mt-2 space-y-1")}>
                    {log.changes.map((change, index) => (
                      <li key={index} className={cn("")}>{change}</li>
                    ))}
                  </ul>
                </div>
                <div className={cn("flex space-x-2 flex-shrink-0")}>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => handleEditLogEntry(log)}
                    ariaLabel={`Edit log for version ${log.version}`}
                  >
                    <FiEdit2 className={cn("h-4 w-4")} aria-hidden="true" />
                  </Button>
                  <Button
                    type="button"
                    variant="destructive"
                    size="icon"
                    onClick={() => handleDeleteLogEntry(log.id)}
                    ariaLabel={`Delete log for version ${log.version}`}
                  >
                    <FiTrash2 className={cn("h-4 w-4")} aria-hidden="true" />
                  </Button>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};
export default UpdateLogManager;
