import React from 'react';
import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  FiKey, FiUsers, FiBarChart2, FiFileText, FiSettings, FiLogOut, FiActivity,
  FiPieChart, FiAlertTriangle, FiCheckSquare, FiClock, FiShield, FiTrendingUp,
  FiRefreshCw, FiEye, FiZap, FiServer, FiGlobe, FiLock
} from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';
import { Card } from '../ui/Card';
import ThemeToggle from './ThemeToggle';
import ScriptRequestManagement from './ScriptRequestManagement';
import ScriptManagement from './ScriptManagement';
import { LazyAdminKeyManagement } from '../../KeySystem/LazyKeySystem';
import SecurityManagement from './SecurityManagement';
import MLSecurityDashboard from './MLSecurityDashboard';
import SecurityDashboard from './SecurityDashboard';
import SecurityCenter from './SecurityCenter';
import ObfuscatorPanel from './ObfuscatorPanel';

import AdminUserManagement from './AdminUserManagement';
import Crown from '../ui/Crown';
import LazyImage from '../ui/LazyImage';
const NAV_ITEMS = [
  {
    key: 'dashboard',
    label: 'Dashboard',
    icon: <FiBarChart2 className="h-5 w-5" />,
    roles: ['owner', 'admin'],
    gradient: 'bg-primary/10'
  },
  {
    key: 'key-management',
    label: 'Key Management',
    icon: <FiKey className="h-5 w-5" />,
    roles: ['owner', 'admin'],
    gradient: 'bg-success/10'
  },
  {
    key: 'script-request',
    label: 'Script Request',
    icon: <FiUsers className="h-5 w-5" />,
    roles: ['owner', 'admin'],
    gradient: 'bg-accent/10'
  },
  {
    key: 'admin-users',
    label: 'Admin Users',
    icon: <FiLock className="h-5 w-5" />,
    roles: ['owner'],
    gradient: 'bg-destructive/10'
  },
  {
    key: 'script-manager',
    label: 'Script Manager',
    icon: <FiFileText className="h-5 w-5" />,
    roles: ['owner'],
    gradient: 'from-orange-500 to-orange-600'
  },
  {
    key: 'security-center',
    label: 'Security Center',
    icon: <FiShield className="h-5 w-5" />,
    roles: ['owner'],
    gradient: 'from-red-500 to-red-600'
  },
  {
    key: 'ml-security',
    label: 'ML Security',
    icon: <FiZap className="h-5 w-5" />,
    roles: ['owner', 'ml_security'],
    gradient: 'from-cyan-500 to-cyan-600'
  },

  {
    key: 'settings',
    label: 'Settings',
    icon: <FiSettings className="h-5 w-5" />,
    roles: ['owner', 'admin'],
    gradient: 'from-gray-500 to-gray-600'
  },
];
const QUICK_ACTIONS = [
  {
    icon: <FiKey className="h-6 w-6" />,
    title: 'Generate Keys',
    subtitle: 'Create new license keys',
    color: 'primary',
    bgGradient: 'bg-primary/5',
    iconBg: 'bg-primary',
    action: 'generate-keys'
  },
  {
    icon: <FiShield className="h-6 w-6" />,
    title: 'Security Center',
    subtitle: 'Monitor threats & security',
    color: 'destructive',
    bgGradient: 'bg-destructive/5',
    iconBg: 'bg-destructive',
    action: 'security-center'
  },
  {
    icon: <FiZap className="h-6 w-6" />,
    title: 'ML Security',
    subtitle: 'AI-powered threat detection',
    color: 'info',
    bgGradient: 'bg-info/5',
    iconBg: 'bg-info',
    action: 'ml-security'
  },
  {
    icon: <FiLock className="h-6 w-6" />,
    title: 'Admin Users',
    subtitle: 'Manage admin accounts',
    color: 'purple',
    bgGradient: 'from-purple-50 to-purple-100',
    iconBg: 'bg-purple-500',
    action: 'admin-users',
    ownerOnly: true
  },
];
const AVATAR_PLACEHOLDER = 'https://api.dicebear.com/7.x/identicon/svg?seed=admin';
// Helper to get admin session headers for API calls
const getAdminHeaders = () => {
  const sessionToken = sessionStorage.getItem('adminSessionToken');
  return {
    'x-session-token': sessionToken || '',
    'Content-Type': 'application/json',
  };
};
const AdminDashboard = () => {
  const location = useLocation();
  const { admin, logout, hasPermission, isMLSecurity } = useAuth();
  const [activeNav, setActiveNav] = useState(isMLSecurity ? 'ml-security' : 'dashboard');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const avatarUrl = AVATAR_PLACEHOLDER;
  // Stats state
  const [stats, setStats] = useState({
    totalKeys: 0,
    activeKeys: 0,
    expiredKeys: 0,
    revokedKeys: 0,
    todaysKeys: 0,
    totalBannedIps: 0
  });
  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState(null);
  // Recent activity state
  const [activity, setActivity] = useState([]);
  const [activityLoading, setActivityLoading] = useState(true);
  const [activityError, setActivityError] = useState(null);
  // System health state
  const [health, setHealth] = useState({
    uptime: '-',
    totalAdmins: 0,
    expiredKeys: 0,
    todaysKeys: 0,
    settings: {}
  });
  const [healthLoading, setHealthLoading] = useState(true);
  const [healthError, setHealthError] = useState(null);
  // Remove 'session' param from URL if present (prevents user key generator logic from interfering)
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    if (params.has('session')) {
      params.delete('session');
      window.history.replaceState({}, '', location.pathname + (params.toString() ? `?${params.toString()}` : ''));
    }
  }, [location.pathname, location.search]);
  useEffect(() => {
    if (activeNav === 'dashboard' && admin) {
      fetchStats();
      fetchActivity();
      fetchHealth();
    }
    // eslint-disable-next-line
  }, [activeNav, admin]);
  const fetchStats = async () => {
    setStatsLoading(true);
    setStatsError(null);
    try {
      const response = await fetch('/.netlify/functions/security?action=ip-bans', {
        headers: getAdminHeaders()
      });
      if (response.status === 401) {
        // Use the logout function from AuthContext instead of manual cleanup
        await logout();
        return;
      }
      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Failed to fetch stats');
      setStats({ ...stats, totalBannedIps: data.length });
    } catch (err) {
      setStatsError(err.message);
    } finally {
      setStatsLoading(false);
    }
  };
  const fetchActivity = async () => {
    setActivityLoading(true);
    setActivityError(null);
    try {
      const response = await fetch('/.netlify/functions/security?action=admin-actions', {
        headers: getAdminHeaders()
      });
      if (response.status === 401) {
        // Use the logout function from AuthContext instead of manual cleanup
        await logout();
        return;
      }
      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Failed to fetch activity');
      setActivity(data || []);
    } catch (err) {
      setActivityError(err.message);
    } finally {
      setActivityLoading(false);
    }
  };
  const fetchHealth = async () => {
    // Disabled to prevent 502 errors - health will show default values
    setHealthLoading(false);
    setHealthError(null);
    setHealth({ ...health, settings: { status: 'healthy' } });
  };
  const handleLogout = async () => {
    await logout();
    // The AuthContext logout function will handle the redirect
  };
  return (
    <div className="flex h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 overflow-hidden">
      {/* Modern Sidebar */}
      <motion.aside
        initial={{ x: -300, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
        className="hidden lg:flex w-72 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border-r border-gray-200/50 dark:border-gray-700/50 flex-col shadow-2xl"
      >
        {/* Admin Profile - Moved to Top */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="flex flex-col items-center py-8 border-b border-gray-200/50 dark:border-gray-700/50"
        >
          <motion.div
            whileHover={{ scale: 1.1 }}
            className="relative mb-4"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full blur-md opacity-40"></div>
            <LazyImage
              src={avatarUrl}
              alt="Admin avatar"
              className="relative w-16 h-16 rounded-full border-3 border-white dark:border-gray-700 shadow-xl"
              placeholder={
                <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 flex items-center justify-center">
                  <div className="text-lg font-bold text-white/60">
                    {admin?.username?.charAt(0) || 'A'}
                  </div>
                </div>
              }
            />
            <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-success border-2 border-background rounded-full"></div>
          </motion.div>
          <h3 className="font-semibold text-gray-900 dark:text-white text-xl mb-2">{admin?.username || 'Admin'}</h3>
          <span className={`text-xs px-3 py-1 rounded-full font-medium mb-4 ${
            admin?.role === 'owner'
              ? 'bg-warning/10 text-warning'
              : admin?.role === 'ml_security'
              ? 'bg-info/10 text-info'
              : 'bg-primary/10 text-primary'
          }`}>
            {admin?.role === 'owner' ? (
              <span className="flex items-center gap-1.5">
                <Crown size="w-4 h-4" />
                <span>Owner</span>
              </span>
            ) : admin?.role === 'ml_security' ? '🤖 ML Security' : '⚡ Admin'}
          </span>
          {/* Logout Button in Profile Section */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleLogout}
            className="flex items-center gap-2 px-4 py-2 rounded-xl text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 transition-all duration-300 border border-red-200 dark:border-red-800 hover:border-red-300 dark:hover:border-red-700"
          >
            <FiLogOut className="h-4 w-4" />
            <span className="text-sm font-semibold">Logout</span>
          </motion.button>
        </motion.div>
        {/* Navigation */}
        <nav className="flex-1 flex flex-col gap-2 mt-4 px-4">
          {NAV_ITEMS.filter(item => !item.roles || item.roles.includes(admin?.role)).map((item, index) => (
            <motion.button
              key={item.key}
              initial={{ x: -50, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.02, x: 4 }}
              whileTap={{ scale: 0.98 }}
              className={`group flex items-center gap-4 px-4 py-3 rounded-xl font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/30 ${
                activeNav === item.key
                  ? `bg-gradient-to-r ${item.gradient} text-white shadow-lg shadow-blue-500/25`
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100/80 dark:hover:bg-gray-800/50 hover:text-gray-900 dark:hover:text-white'
              }`}
              onClick={() => setActiveNav(item.key)}
            >
              <div className={`p-2 rounded-lg transition-all duration-300 ${
                activeNav === item.key
                  ? 'bg-white/20 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 group-hover:bg-gray-200 dark:group-hover:bg-gray-600'
              }`}>
                {React.cloneElement(item.icon, { className: "h-5 w-5" })}
              </div>
              <span className="font-semibold">{item.label}</span>
              {activeNav === item.key && (
                <motion.div
                  layoutId="activeIndicator"
                  className="ml-auto w-2 h-2 bg-white rounded-full"
                />
              )}
            </motion.button>
          ))}
        </nav>
      </motion.aside>
      {/* Mobile Header - Only visible on mobile */}
      <div className="lg:hidden sticky top-0 z-30 bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-700/50 shadow-lg">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center gap-3">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            >
              <FiBarChart2 className="h-5 w-5 text-gray-600 dark:text-gray-300" />
            </button>
            <div>
              <h1 className="text-lg font-bold text-gray-900 dark:text-white">
                {activeNav === 'dashboard' && 'Dashboard'}
                {activeNav === 'script-request' && 'Script Requests'}
                {activeNav === 'script-manager' && 'Script Manager'}
                {activeNav === 'key-management' && 'Key Management'}
                {activeNav === 'security' && 'Security Center'}
                {activeNav === 'security-dashboard' && 'Security Monitor'}
                {activeNav === 'ml-security' && 'ML Security'}

                {activeNav === 'settings' && 'Settings'}
              </h1>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <ThemeToggle />
            <LazyImage
              src={avatarUrl}
              alt="Admin avatar"
              className="w-8 h-8 rounded-full border-2 border-blue-500"
              placeholder={
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 flex items-center justify-center">
                  <div className="text-xs font-bold text-white/60">
                    {admin?.username?.charAt(0) || 'A'}
                  </div>
                </div>
              }
            />
          </div>
        </div>
      </div>
      {/* Mobile Navigation Overlay */}
      {isMobileMenuOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="lg:hidden fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
          onClick={() => setIsMobileMenuOpen(false)}
        >
          <motion.div
            initial={{ x: -300 }}
            animate={{ x: 0 }}
            exit={{ x: -300 }}
            className="w-72 h-full bg-white dark:bg-gray-900 shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Mobile Sidebar Content - Same as desktop sidebar */}
            <div className="flex flex-col h-full">
              {/* Admin Profile - Moved to Top */}
              <div className="flex flex-col items-center py-8 border-b border-gray-200/50 dark:border-gray-700/50">
                <div className="relative mb-4">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full blur-md opacity-40"></div>
                  <LazyImage
                    src={avatarUrl}
                    alt="Admin avatar"
                    className="relative w-16 h-16 rounded-full border-3 border-white dark:border-gray-700 shadow-xl"
                    placeholder={
                      <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 flex items-center justify-center">
                        <div className="text-lg font-bold text-white/60">
                          {admin?.username?.charAt(0) || 'A'}
                        </div>
                      </div>
                    }
                  />
                  <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-success border-2 border-background rounded-full"></div>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white text-xl mb-2">{admin?.username || 'Admin'}</h3>
                <span className={`text-xs px-3 py-1 rounded-full font-medium mb-4 ${
                  admin?.role === 'owner'
                    ? 'bg-warning/10 text-warning'
                    : admin?.role === 'ml_security'
                    ? 'bg-info/10 text-info'
                    : 'bg-primary/10 text-primary'
                }`}>
                  {admin?.role === 'owner' ? (
                    <span className="flex items-center gap-1.5">
                      <Crown size="w-4 h-4" />
                      <span>Owner</span>
                    </span>
                  ) : admin?.role === 'ml_security' ? '🤖 ML Security' : '⚡ Admin'}
                </span>
                {/* Mobile Logout Button in Profile Section */}
                <button
                  onClick={handleLogout}
                  className="flex items-center gap-2 px-4 py-2 rounded-xl text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 transition-all duration-300 border border-red-200 dark:border-red-800 hover:border-red-300 dark:hover:border-red-700"
                >
                  <FiLogOut className="h-4 w-4" />
                  <span className="text-sm font-semibold">Logout</span>
                </button>
              </div>
              {/* Mobile Navigation */}
              <nav className="flex-1 flex flex-col gap-2 mt-4 px-4">
                {NAV_ITEMS.filter(item => !item.roles || item.roles.includes(admin?.role)).map((item) => (
                  <button
                    key={item.key}
                    className={`group flex items-center gap-4 px-4 py-3 rounded-xl font-medium transition-all duration-300 ${
                      activeNav === item.key
                        ? `bg-gradient-to-r ${item.gradient} text-white shadow-lg`
                        : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                    }`}
                    onClick={() => {
                      setActiveNav(item.key);
                      setIsMobileMenuOpen(false);
                    }}
                  >
                    <div className={`p-2 rounded-lg transition-all duration-300 ${
                      activeNav === item.key
                        ? 'bg-white/20 text-white'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
                    }`}>
                      {React.cloneElement(item.icon, { className: "h-5 w-5" })}
                    </div>
                    <span className="font-semibold">{item.label}</span>
                  </button>
                ))}
              </nav>
            </div>
          </motion.div>
        </motion.div>
      )}
      {/* Main Content */}
      <main className="flex-1 overflow-auto bg-transparent">
        {/* Desktop Header - Hidden on mobile */}
        <motion.header
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="hidden lg:block sticky top-0 z-20 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-700/50 shadow-lg"
        >
          <div className="flex flex-col md:flex-row md:items-center md:justify-between px-8 py-6">
            <div className="flex items-center gap-4">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="p-3 rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg"
              >
                {NAV_ITEMS.find(item => item.key === activeNav)?.icon || <FiBarChart2 className="h-6 w-6" />}
              </motion.div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                  {activeNav === 'dashboard' && 'Dashboard'}
                  {activeNav === 'script-request' && 'Script Requests'}
                  {activeNav === 'script-manager' && 'Script Manager'}
                  {activeNav === 'key-management' && 'Key Management'}
                  {activeNav === 'security' && 'Security Center'}
                  {activeNav === 'security-dashboard' && 'Security Monitor'}
                  {activeNav === 'ml-security' && 'ML Security'}

                  {activeNav === 'settings' && 'Settings'}
                </h1>
                <p className="text-gray-600 dark:text-gray-400 font-medium">
                  {activeNav === 'dashboard' && 'System overview and quick actions'}
                  {activeNav === 'script-request' && 'Manage user script requests'}
                  {activeNav === 'script-manager' && 'Add, edit, and manage scripts'}
                  {activeNav === 'key-management' && 'License key management'}
                  {activeNav === 'security' && 'Security monitoring and controls'}
                  {activeNav === 'security-dashboard' && 'Real-time security monitoring and event tracking'}
                  {activeNav === 'ml-security' && 'Machine learning security analysis'}

                  {activeNav === 'settings' && 'System configuration'}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <motion.div whileHover={{ scale: 1.05 }}>
                <ThemeToggle />
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="flex items-center gap-3 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm px-4 py-2 rounded-xl border border-gray-200/50 dark:border-gray-700/50 shadow-lg"
              >
                <LazyImage
                  src={avatarUrl}
                  alt="Admin avatar"
                  className="w-10 h-10 rounded-full border-2 border-blue-500 shadow-md"
                  placeholder={
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 flex items-center justify-center">
                      <div className="text-sm font-bold text-white/60">
                        {admin?.username?.charAt(0) || 'A'}
                      </div>
                    </div>
                  }
                />
                <div className="hidden md:block">
                  <p className="text-sm font-semibold text-gray-900 dark:text-white">{admin?.username || 'Admin'}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Online</p>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.header>
        {/* Main Content Switcher */}
        {activeNav === 'dashboard' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="p-8 space-y-8"
          >
            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {QUICK_ACTIONS
                .filter(action => !action.ownerOnly || admin?.role === 'owner')
                .map((action, index) => (
                <motion.div
                  key={action.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.02, y: -2 }}
                  onClick={() => setActiveNav(action.action)}
                  className={`group relative overflow-hidden rounded-2xl bg-gradient-to-br ${action.bgGradient} dark:from-gray-800 dark:to-gray-700 p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer border border-gray-200/50 dark:border-gray-600/50`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 dark:text-white text-lg mb-1">{action.title}</h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm">{action.subtitle}</p>
                    </div>
                    <motion.div
                      whileHover={{ rotate: 10, scale: 1.1 }}
                      className={`${action.iconBg} p-3 rounded-xl text-white shadow-lg`}
                    >
                      {React.cloneElement(action.icon, { className: "h-6 w-6" })}
                    </motion.div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                </motion.div>
              ))}
            </div>
            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.4 }}
                className="relative overflow-hidden"
              >
                <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-gray-200/50 dark:border-gray-700/50 shadow-xl">
                  <Card.Header className="pb-3">
                    <div className="flex items-center justify-between">
                      <Card.Title className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Keys</Card.Title>
                      <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                        <FiKey className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      </div>
                    </div>
                  </Card.Header>
                  <Card.Content>
                    <div className="space-y-2">
                      <div className="text-3xl font-bold text-gray-900 dark:text-white">{stats.totalKeys}</div>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1 text-green-600 dark:text-green-400">
                          <FiTrendingUp className="h-4 w-4" />
                          <span className="text-sm font-medium">+12%</span>
                        </div>
                        <span className="text-sm text-gray-500 dark:text-gray-400">vs last month</span>
                      </div>
                    </div>
                  </Card.Content>
                </Card>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.5 }}
              >
                <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-gray-200/50 dark:border-gray-700/50 shadow-xl">
                  <Card.Header className="pb-3">
                    <div className="flex items-center justify-between">
                      <Card.Title className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Keys</Card.Title>
                      <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                        <FiCheckSquare className="h-4 w-4 text-green-600 dark:text-green-400" />
                      </div>
                    </div>
                  </Card.Header>
                  <Card.Content>
                    <div className="space-y-2">
                      <div className="text-3xl font-bold text-gray-900 dark:text-white">{stats.activeKeys}</div>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1 text-green-600 dark:text-green-400">
                          <FiTrendingUp className="h-4 w-4" />
                          <span className="text-sm font-medium">+8%</span>
                        </div>
                        <span className="text-sm text-gray-500 dark:text-gray-400">vs last week</span>
                      </div>
                    </div>
                  </Card.Content>
                </Card>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.6 }}
              >
                <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-gray-200/50 dark:border-gray-700/50 shadow-xl">
                  <Card.Header className="pb-3">
                    <div className="flex items-center justify-between">
                      <Card.Title className="text-sm font-medium text-gray-600 dark:text-gray-400">Today's Keys</Card.Title>
                      <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                        <FiClock className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                      </div>
                    </div>
                  </Card.Header>
                  <Card.Content>
                    <div className="space-y-2">
                      <div className="text-3xl font-bold text-gray-900 dark:text-white">{stats.todaysKeys}</div>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1 text-purple-600 dark:text-purple-400">
                          <FiActivity className="h-4 w-4" />
                          <span className="text-sm font-medium">Live</span>
                        </div>
                        <span className="text-sm text-gray-500 dark:text-gray-400">real-time</span>
                      </div>
                    </div>
                  </Card.Content>
                </Card>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.7 }}
              >
                <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-gray-200/50 dark:border-gray-700/50 shadow-xl">
                  <Card.Header className="pb-3">
                    <div className="flex items-center justify-between">
                      <Card.Title className="text-sm font-medium text-gray-600 dark:text-gray-400">Banned IPs</Card.Title>
                      <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-lg">
                        <FiShield className="h-4 w-4 text-red-600 dark:text-red-400" />
                      </div>
                    </div>
                  </Card.Header>
                  <Card.Content>
                    <div className="space-y-2">
                      <div className="text-3xl font-bold text-gray-900 dark:text-white">{stats.totalBannedIps}</div>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1 text-red-600 dark:text-red-400">
                          <FiLock className="h-4 w-4" />
                          <span className="text-sm font-medium">Protected</span>
                        </div>
                        <span className="text-sm text-gray-500 dark:text-gray-400">security active</span>
                      </div>
                    </div>
                  </Card.Content>
                </Card>
              </motion.div>
            </div>
            {/* Recent Activity */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="grid grid-cols-1 lg:grid-cols-2 gap-6"
            >
              <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-gray-200/50 dark:border-gray-700/50 shadow-xl">
                <Card.Header>
                  <Card.Title className="flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-white">
                    <FiActivity className="h-5 w-5 text-blue-500" />
                    Recent Activity
                  </Card.Title>
                </Card.Header>
                <Card.Content>
                  <div className="space-y-4">
                    {activityLoading ? (
                      <div className="flex items-center justify-center py-8 text-gray-500">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                      </div>
                    ) : activity.length > 0 ? (
                      activity.slice(0, 5).map((item, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="flex items-center gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        >
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900 dark:text-white">{item.action}</p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">{item.timestamp}</p>
                          </div>
                        </motion.div>
                      ))
                    ) : (
                      <p className="text-center text-gray-500 dark:text-gray-400 py-8">No recent activity</p>
                    )}
                  </div>
                </Card.Content>
              </Card>
              <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-gray-200/50 dark:border-gray-700/50 shadow-xl">
                <Card.Header>
                  <Card.Title className="flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-white">
                    <FiServer className="h-5 w-5 text-green-500" />
                    System Health
                  </Card.Title>
                </Card.Header>
                <Card.Content>
                  <div className="space-y-4">
                    {healthLoading ? (
                      <div className="flex items-center justify-center py-8 text-gray-500">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
                      </div>
                    ) : (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-3 rounded-lg bg-green-50 dark:bg-green-900/20">
                          <div className="text-2xl font-bold text-green-600 dark:text-green-400">99.9%</div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">Uptime</div>
                        </div>
                        <div className="text-center p-3 rounded-lg bg-blue-50 dark:bg-blue-900/20">
                          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{health.totalAdmins}</div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">Admins</div>
                        </div>
                        <div className="text-center p-3 rounded-lg bg-purple-50 dark:bg-purple-900/20">
                          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{health.todaysKeys}</div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">Today</div>
                        </div>
                        <div className="text-center p-3 rounded-lg bg-orange-50 dark:bg-orange-900/20">
                          <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">{health.expiredKeys}</div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">Expired</div>
                        </div>
                      </div>
                    )}
                  </div>
                </Card.Content>
              </Card>
            </motion.div>
          </motion.div>
        )}
        {activeNav === 'script-request' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="p-8"
          >
            <ScriptRequestManagement />
          </motion.div>
        )}
        {activeNav === 'script-manager' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="p-8"
          >
            <ScriptManagement />
          </motion.div>
        )}
        {activeNav === 'key-management' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="p-8"
          >
            <LazyAdminKeyManagement />
          </motion.div>
        )}

        {activeNav === 'settings' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="p-8"
          >
            <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-gray-200/50 dark:border-gray-700/50 shadow-xl max-w-2xl mx-auto">
              <Card.Header>
                <Card.Title className="flex items-center gap-2 text-xl font-semibold text-gray-900 dark:text-white">
                  <FiSettings className="h-6 w-6 text-blue-500" />
                  Settings
                </Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <FiSettings className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Settings Panel</h3>
                  <p className="text-gray-600 dark:text-gray-400">Advanced configuration options coming soon...</p>
                </div>
              </Card.Content>
            </Card>
          </motion.div>
        )}
        {activeNav === 'security-center' && (admin?.role === 'owner' || admin?.role === 'admin') && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <SecurityCenter />
          </motion.div>
        )}
        {activeNav === 'ml-security' && (admin?.role === 'owner' || admin?.role === 'ml_security') && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="p-8"
          >
            <MLSecurityDashboard />
          </motion.div>
        )}
        {activeNav === 'admin-users' && admin?.role === 'owner' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <AdminUserManagement />
          </motion.div>
        )}
      </main>
    </div>
  );
};
export default AdminDashboard; 