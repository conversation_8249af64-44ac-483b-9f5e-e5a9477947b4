import { useState, useEffect } from 'react';
import { <PERSON>Calendar, FiClock, FiUsers } from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';
// Helper to get admin session headers for API calls
const getAdminHeaders = () => {
  const sessionToken = sessionStorage.getItem('adminSessionToken');
  return {
    'x-session-token': sessionToken || '',
    'Content-Type': 'application/json',
  };
};
const UserActivityHeatmap = () => {
  const { admin } = useAuth();
  const [activityData, setActivityData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState('week');
  useEffect(() => {
    if (admin) {
      fetchActivityData();
    }
  }, [selectedTimeframe, admin]);
  const fetchActivityData = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/.netlify/functions/analytics?action=user-activity-heatmap&timeframe=${selectedTimeframe}`, {
        headers: getAdminHeaders()
      });
      if (response.ok) {
        const data = await response.json();
        setActivityData(data);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch activity data');
      }
    } catch (err) {
      setError(err.message);
      setActivityData([]);
    } finally {
      setLoading(false);
    }
  };

  const getActivityColor = (activity) => {
    if (activity >= 80) return 'bg-destructive';
    if (activity >= 60) return 'bg-warning';
    if (activity >= 40) return 'bg-primary';
    if (activity >= 20) return 'bg-info';
    return 'bg-muted';
  };
  const getActivityIntensity = (activity) => {
    if (activity >= 80) return 'Very High';
    if (activity >= 60) return 'High';
    if (activity >= 40) return 'Medium';
    if (activity >= 20) return 'Low';
    return 'Very Low';
  };
  const formatHour = (hour) => {
    return `${hour.toString().padStart(2, '0')}:00`;
  };
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-400">Loading activity heatmap...</div>
      </div>
    );
  }
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-red-400">Error: {error}</div>
      </div>
    );
  }
  const days = selectedTimeframe === 'week' 
    ? ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    : Array.from({ length: 30 }, (_, i) => (i + 1).toString());
  const hours = Array.from({ length: 24 }, (_, i) => i);
  return (
    <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-white flex items-center gap-2">
            <FiUsers className="h-5 w-5" />
            User Activity Heatmap
          </h3>
          <p className="text-sm text-gray-400">Activity patterns throughout the {selectedTimeframe}</p>
        </div>
        <div className="flex items-center gap-3">
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="bg-gray-700 border border-gray-600 text-white rounded-lg px-3 py-2 text-sm"
          >
            <option value="week">This Week</option>
            <option value="month">This Month</option>
          </select>
        </div>
      </div>
      {/* Legend */}
      <div className="flex items-center gap-4 mb-4 text-sm">
        <span className="text-gray-300">Activity Level:</span>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-gray-600 rounded"></div>
          <span className="text-gray-400">Very Low</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-blue-500 rounded"></div>
          <span className="text-gray-400">Low</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-yellow-500 rounded"></div>
          <span className="text-gray-400">Medium</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-orange-500 rounded"></div>
          <span className="text-gray-400">High</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-red-600 rounded"></div>
          <span className="text-gray-400">Very High</span>
        </div>
      </div>
      {/* Heatmap Grid */}
      <div className="overflow-x-auto">
        <div className="min-w-max">
          {/* Header row with hours */}
          <div className="flex mb-2">
            <div className="w-16 h-8 flex items-center justify-center text-xs text-gray-400 font-medium">
              Day/Hour
            </div>
            {hours.map(hour => (
              <div
                key={hour}
                className="w-8 h-8 flex items-center justify-center text-xs text-gray-400 font-medium"
              >
                {formatHour(hour)}
              </div>
            ))}
          </div>
          {/* Heatmap rows */}
          {days.map(day => (
            <div key={day} className="flex mb-1">
              <div className="w-16 h-8 flex items-center justify-center text-xs text-gray-300 font-medium border-r border-gray-700">
                {day}
              </div>
              {hours.map(hour => {
                const cellData = activityData.find(d => d.day === day && d.hour === hour);
                const activity = cellData?.activity || 0;
                const users = cellData?.users || 0;
                return (
                  <div
                    key={`${day}-${hour}`}
                    className={`w-8 h-8 ${getActivityColor(activity)} rounded-sm cursor-pointer transition-all duration-200 hover:scale-110 relative group`}
                    title={`${day} ${formatHour(hour)}: ${getActivityIntensity(activity)} activity (${users} users)`}
                  >
                    {/* Tooltip */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                      <div className="font-medium">{day} {formatHour(hour)}</div>
                      <div>Activity: {activity}</div>
                      <div>Users: {users}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6 pt-6 border-t border-gray-700">
        <div className="text-center">
          <div className="text-2xl font-bold text-white">
            {Math.max(...activityData.map(d => d.activity))}
          </div>
          <div className="text-sm text-gray-400">Peak Activity</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-white">
            {Math.round(activityData.reduce((sum, d) => sum + d.activity, 0) / activityData.length)}
          </div>
          <div className="text-sm text-gray-400">Average Activity</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-white">
            {Math.max(...activityData.map(d => d.users))}
          </div>
          <div className="text-sm text-gray-400">Peak Users</div>
        </div>
      </div>
      {/* Peak Hours Analysis */}
      <div className="mt-6 pt-6 border-t border-gray-700">
        <h4 className="text-md font-semibold text-white mb-3">Peak Activity Hours</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {hours
            .map(hour => {
              const hourData = activityData.filter(d => d.hour === hour);
              const avgActivity = hourData.length > 0 
                ? Math.round(hourData.reduce((sum, d) => sum + d.activity, 0) / hourData.length)
                : 0;
              return { hour, avgActivity };
            })
            .sort((a, b) => b.avgActivity - a.avgActivity)
            .slice(0, 4)
            .map(({ hour, avgActivity }, index) => (
              <div key={hour} className="bg-gray-700 rounded-lg p-3 text-center">
                <div className="text-lg font-bold text-white">{formatHour(hour)}</div>
                <div className="text-sm text-gray-400">#{index + 1} Peak</div>
                <div className="text-xs text-gray-500">{avgActivity} activity</div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};
export default UserActivityHeatmap; 