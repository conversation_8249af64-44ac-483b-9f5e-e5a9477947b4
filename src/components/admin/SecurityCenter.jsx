import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FiShield, 
  FiEye, 
  FiActivity, 
  FiUsers, 
  FiSettings,
  FiAlertTriangle,
  FiLock,
  FiMonitor,
  FiZap,
  FiBarChart2
} from 'react-icons/fi';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { useAuth } from '../../context/AuthContext';
import SecurityManagement from './SecurityManagement';
import SecurityDashboard from './SecurityDashboard';

const SecurityCenter = () => {
  const { admin } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [securityStats, setSecurityStats] = useState({
    totalEvents: 0,
    activeThreats: 0,
    blockedIPs: 0,
    mlDetections: 0
  });

  const securityTabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: <FiBarChart2 className="h-4 w-4" />,
      roles: ['owner', 'admin']
    },
    {
      id: 'monitoring',
      label: 'Real-time Monitor',
      icon: <FiEye className="h-4 w-4" />,
      roles: ['owner', 'admin']
    },
    {
      id: 'management',
      label: 'Security Management',
      icon: <FiSettings className="h-4 w-4" />,
      roles: ['owner']
    }
  ];

  const getAdminHeaders = () => ({
    'x-session-token': sessionStorage.getItem('adminSessionToken') || '',
    'Content-Type': 'application/json',
  });

  const fetchSecurityStats = async () => {
    try {
      const headers = getAdminHeaders();

      // Check if we have a session token
      if (!headers['x-session-token']) {
        console.warn('No session token available for security stats');
        return;
      }

      // Fetch security statistics from various endpoints
      const [eventsResponse, bansResponse] = await Promise.all([
        fetch('/.netlify/functions/security?action=events', {
          headers,
        }),
        fetch('/.netlify/functions/security?action=ip-bans', {
          headers,
        })
      ]);

      if (eventsResponse.ok) {
        const eventsData = await eventsResponse.json();
        const events = Array.isArray(eventsData) ? eventsData : [];

        setSecurityStats(prev => ({
          ...prev,
          totalEvents: events.length,
          activeThreats: events.filter(e =>
            e.event_type?.includes('threat') ||
            e.event_type?.includes('attack')
          ).length,
          mlDetections: events.filter(e =>
            e.event_type?.includes('ml_') ||
            e.event_type?.includes('bot_')
          ).length
        }));
      } else {
        console.error('Events response failed:', eventsResponse.status, await eventsResponse.text());
      }

      if (bansResponse.ok) {
        const bansData = await bansResponse.json();
        const bans = Array.isArray(bansData.data) ? bansData.data : [];

        setSecurityStats(prev => ({
          ...prev,
          blockedIPs: bans.filter(ban => ban.is_active).length
        }));
      } else {
        console.error('Bans response failed:', bansResponse.status, await bansResponse.text());
      }
    } catch (error) {
      console.error('Failed to fetch security stats:', error);
    }
  };

  useEffect(() => {
    fetchSecurityStats();
    const interval = setInterval(fetchSecurityStats, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const StatCard = ({ title, value, icon, color, description }) => (
    <Card className={`p-6 bg-gradient-to-br ${color} border-opacity-20`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-white/70 text-sm font-medium">{title}</p>
          <p className="text-3xl font-bold text-white mt-1">{value}</p>
          <p className="text-white/60 text-xs mt-1">{description}</p>
        </div>
        <div className="text-white/80">
          {icon}
        </div>
      </div>
    </Card>
  );

  const QuickAction = ({ title, description, icon, onClick, color }) => (
    <Card className="p-4 hover:shadow-lg transition-shadow cursor-pointer" onClick={onClick}>
      <div className="flex items-center space-x-3">
        <div className={`p-3 rounded-lg ${color}`}>
          {icon}
        </div>
        <div>
          <h4 className="font-semibold text-gray-900 dark:text-white">{title}</h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">{description}</p>
        </div>
      </div>
    </Card>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            {/* Security Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <StatCard
                title="Security Events"
                value={securityStats.totalEvents}
                icon={<FiActivity className="h-8 w-8" />}
                color="bg-primary/10 border-primary/20"
                description="Last 24 hours"
              />
              <StatCard
                title="Active Threats"
                value={securityStats.activeThreats}
                icon={<FiAlertTriangle className="h-8 w-8" />}
                color="bg-destructive/10 border-destructive/20"
                description="Requires attention"
              />
              <StatCard
                title="Blocked IPs"
                value={securityStats.blockedIPs}
                icon={<FiShield className="h-8 w-8" />}
                color="bg-success/10 border-success/20"
                description="Currently active"
              />
              <StatCard
                title="ML Detections"
                value={securityStats.mlDetections}
                icon={<FiZap className="h-8 w-8" />}
                color="bg-accent/10 border-accent/20"
                description="AI-powered blocks"
              />
            </div>

            {/* Quick Actions */}
            <Card>
              <Card.Header>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Quick Security Actions
                </h3>
              </Card.Header>
              <Card.Content>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <QuickAction
                    title="View Live Monitor"
                    description="Real-time security monitoring"
                    icon={<FiMonitor className="h-5 w-5 text-white" />}
                    color="bg-blue-500"
                    onClick={() => setActiveTab('monitoring')}
                  />
                  {admin?.role === 'owner' && (
                    <QuickAction
                      title="Security Settings"
                      description="Configure security policies"
                      icon={<FiSettings className="h-5 w-5 text-white" />}
                      color="bg-green-500"
                      onClick={() => setActiveTab('management')}
                    />
                  )}
                  <QuickAction
                    title="ML Security Dashboard"
                    description="AI-powered threat detection"
                    icon={<FiZap className="h-5 w-5 text-white" />}
                    color="bg-purple-500"
                    onClick={() => window.location.hash = '#ml-security'}
                  />
                </div>
              </Card.Content>
            </Card>

            {/* Security Health Status */}
            <Card>
              <Card.Header>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Security Health Status
                </h3>
              </Card.Header>
              <Card.Content>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="font-medium text-gray-900 dark:text-white">Authentication System</span>
                    </div>
                    <span className="text-green-600 dark:text-green-400 text-sm font-medium">Secure</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="font-medium text-gray-900 dark:text-white">Password Security</span>
                    </div>
                    <span className="text-green-600 dark:text-green-400 text-sm font-medium">Encrypted</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="font-medium text-gray-900 dark:text-white">Session Management</span>
                    </div>
                    <span className="text-green-600 dark:text-green-400 text-sm font-medium">Active</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="font-medium text-gray-900 dark:text-white">Role-Based Access Control</span>
                    </div>
                    <span className="text-green-600 dark:text-green-400 text-sm font-medium">Enforced</span>
                  </div>
                </div>
              </Card.Content>
            </Card>
          </div>
        );

      case 'monitoring':
        return <SecurityDashboard />;

      case 'management':
        return admin?.role === 'owner' ? <SecurityManagement /> : (
          <Card className="p-6 text-center">
            <FiLock className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Access Denied</h3>
            <p className="text-gray-600 dark:text-gray-400">
              Only owners can access security management.
            </p>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Security Center
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Comprehensive security monitoring and management dashboard
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="mb-8">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {securityTabs
              .filter(tab => tab.roles.includes(admin?.role))
              .map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  {tab.icon}
                  <span>{tab.label}</span>
                </button>
              ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {renderTabContent()}
      </motion.div>
    </div>
  );
};

export default SecurityCenter;
