import { useState, useEffect } from 'react';
import { FiShield, FiEye, FiSettings, FiAlertTriangle, FiCheckCircle, FiXCircle, FiClock, FiUser, FiX } from 'react-icons/fi';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '../ui/Table';
import { Label } from '../ui/Label';
import { Textarea } from '../ui/Textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '../ui/Dialog';
import { Badge } from '../ui/Badge';
import { Switch } from '../ui/Switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/Tabs';
import { useAuth } from '../../context/AuthContext';
const SecurityManagement = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  // IP Bans state
  const [ipBans, setIpBans] = useState([]);
  const [isBanModalOpen, setIsBanModalOpen] = useState(false);
  const [banFormData, setBanFormData] = useState({
    ip_address: '',
    reason: '',
    duration_hours: '24',
    notes: ''
  });
  // Security Events state
  const [securityEvents, setSecurityEvents] = useState([]);
  const [isEventModalOpen, setIsEventModalOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);
  // Security Settings state
  const [securitySettings, setSecuritySettings] = useState({});
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [selectedSetting, setSelectedSetting] = useState(null);
  // Admin Actions state
  const [adminActions, setAdminActions] = useState([]);
  const { token, isAuthenticated } = useAuth();
  const getAdminHeaders = () => ({
    'x-session-token': sessionStorage.getItem('adminSessionToken') || '',
    'Content-Type': 'application/json',
  });
  const fetchSecurityData = async () => {
    setLoading(true);
    setError(null);
    if (!isAuthenticated) {
      setError('User not authenticated.');
      setLoading(false);
      return;
    }
    try {
      const [bansResponse, eventsResponse, settingsResponse, actionsResponse] = await Promise.all([
        fetch('/.netlify/functions/security?action=ip-bans', {
          headers: getAdminHeaders(),
        }),
        fetch('/.netlify/functions/security?action=events', {
          headers: getAdminHeaders(),
        }),
        fetch('/.netlify/functions/security?action=settings', {
          headers: getAdminHeaders(),
        }),
        fetch('/.netlify/functions/security?action=admin-actions', {
          headers: getAdminHeaders(),
        })
      ]);
      if (bansResponse.ok) {
        const bansData = await bansResponse.json();
        setIpBans(Array.isArray(bansData.data) ? bansData.data : []);
      }
      if (eventsResponse.ok) {
        const eventsData = await eventsResponse.json();
        setSecurityEvents(eventsData);
      }
      if (settingsResponse.ok) {
        const settingsData = await settingsResponse.json();
        const settingsMap = {};
        settingsData.forEach(setting => {
          settingsMap[setting.setting_key] = setting;
        });
        setSecuritySettings(settingsMap);
      }
      if (actionsResponse.ok) {
        const actionsData = await actionsResponse.json();
        setAdminActions(actionsData);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (isAuthenticated) {
      fetchSecurityData();
    }
  }, [isAuthenticated, token]);
  const handleBanIP = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/.netlify/functions/security?action=ban-ip', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAdminHeaders(),
        },
        body: JSON.stringify(banFormData),
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || 'Failed to ban IP');
      }
      setIsBanModalOpen(false);
      setBanFormData({
        ip_address: '',
        reason: '',
        duration_hours: '24',
        notes: ''
      });
      fetchSecurityData();
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  const handleUnbanIP = async (ipAddress) => {
    if (!window.confirm(`Are you sure you want to unban ${ipAddress}?`)) {
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/.netlify/functions/security?action=unban-ip', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAdminHeaders(),
        },
        body: JSON.stringify({ ip_address: ipAddress }),
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || 'Failed to unban IP');
      }
      fetchSecurityData();
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  const handleUpdateSecuritySetting = async (settingKey, newValue) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/.netlify/functions/security?action=update-setting', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAdminHeaders(),
        },
        body: JSON.stringify({
          setting_key: settingKey,
          setting_value: newValue
        }),
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || 'Failed to update setting');
      }
      fetchSecurityData();
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical':
        return 'bg-destructive/10 text-destructive';
      case 'high':
        return 'bg-destructive/10 text-destructive';
      case 'medium':
        return 'bg-warning/10 text-warning';
      case 'low':
        return 'bg-info/10 text-info';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };
  const getStatusColor = (status) => {
    switch (status) {
      case 'resolved':
        return 'bg-success/10 text-success';
      case 'investigating':
        return 'bg-info/10 text-info';
      case 'false_positive':
        return 'bg-muted text-muted-foreground';
      case 'pending':
        return 'bg-warning/10 text-warning';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };
  const formatDuration = (hours) => {
    if (hours === 0) return 'Permanent';
    if (hours < 24) return `${hours}h`;
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    return remainingHours > 0 ? `${days}d ${remainingHours}h` : `${days}d`;
  };
  const getSecurityOverview = () => {
    const activeBans = ipBans.filter(ban => ban.is_active);
    const pendingEvents = securityEvents.filter(event => event.status === 'pending');
    const criticalEvents = securityEvents.filter(event => event.severity === 'critical');
    return {
      activeBans: activeBans.length,
      pendingEvents: pendingEvents.length,
      criticalEvents: criticalEvents.length,
      totalEvents: securityEvents.length
    };
  };
  const overview = getSecurityOverview();
  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-foreground">Security Management</h2>
        <div className="flex gap-2">
          <Button
            onClick={() => setIsBanModalOpen(true)}
            className="gap-2"
            tabIndex={0}
            aria-label="Ban IP address"
          >
            <FiX className="h-5 w-5" />
            Ban IP
          </Button>
          <Button
            onClick={() => setIsSettingsModalOpen(true)}
            variant="outline"
            className="gap-2"
            tabIndex={0}
            aria-label="Security settings"
          >
            <FiSettings className="h-5 w-5" />
            Settings
          </Button>
        </div>
      </div>
      {loading && <p className="text-center text-muted-foreground">Loading security data...</p>}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-800 font-medium">Error:</p>
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="ip-bans">IP Bans</TabsTrigger>
          <TabsTrigger value="events">Security Events</TabsTrigger>
          <TabsTrigger value="actions">Admin Actions</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2">
                <FiX className="h-6 w-6 text-red-600" />
                <div>
                  <p className="text-sm text-red-600 font-medium">Active Bans</p>
                  <p className="text-2xl font-bold text-red-800">{overview.activeBans}</p>
                </div>
              </div>
            </div>
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center gap-2">
                <FiAlertTriangle className="h-6 w-6 text-yellow-600" />
                <div>
                  <p className="text-sm text-yellow-600 font-medium">Pending Events</p>
                  <p className="text-2xl font-bold text-yellow-800">{overview.pendingEvents}</p>
                </div>
              </div>
            </div>
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2">
                <FiAlertTriangle className="h-6 w-6 text-red-600" />
                <div>
                  <p className="text-sm text-red-600 font-medium">Critical Events</p>
                  <p className="text-2xl font-bold text-red-800">{overview.criticalEvents}</p>
                </div>
              </div>
            </div>
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2">
                <FiEye className="h-6 w-6 text-blue-600" />
                <div>
                  <p className="text-sm text-blue-600 font-medium">Total Events</p>
                  <p className="text-2xl font-bold text-blue-800">{overview.totalEvents}</p>
                </div>
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Recent Security Events</h3>
              <div className="space-y-2">
                {securityEvents.slice(0, 5).map((event) => (
                  <div key={event.id} className="p-3 bg-gray-50 rounded-md">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-sm">{event.event_type}</p>
                        <p className="text-xs text-gray-500">{event.ip_address}</p>
                      </div>
                      <Badge className={getSeverityColor(event.severity)}>
                        {event.severity}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Recent Admin Actions</h3>
              <div className="space-y-2">
                {adminActions.slice(0, 5).map((action) => (
                  <div key={action.id} className="p-3 bg-gray-50 rounded-md">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-sm">{action.action_type}</p>
                        <p className="text-xs text-gray-500">{action.admin_username}</p>
                      </div>
                      <p className="text-xs text-gray-500">
                        {new Date(action.performed_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </TabsContent>
        <TabsContent value="ip-bans" className="space-y-4">
          <div className="overflow-x-auto rounded-md border border-border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>IP Address</TableHead>
                  <TableHead>Reason</TableHead>
                  <TableHead>Banned By</TableHead>
                  <TableHead>Duration</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Banned At</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {ipBans.map((ban) => (
                  <TableRow key={ban.id}>
                    <TableCell className="font-mono">{ban.ip_address}</TableCell>
                    <TableCell>{ban.reason}</TableCell>
                    <TableCell>{ban.banned_by}</TableCell>
                    <TableCell>{formatDuration(ban.duration_hours)}</TableCell>
                    <TableCell>
                      <Badge variant={ban.is_active ? 'destructive' : 'secondary'}>
                        {ban.is_active ? 'Active' : 'Expired'}
                      </Badge>
                    </TableCell>
                    <TableCell>{new Date(ban.banned_at).toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">
                      {ban.is_active && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleUnbanIP(ban.ip_address)}
                          tabIndex={0}
                          aria-label={`Unban ${ban.ip_address}`}
                        >
                          Unban
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        <TabsContent value="events" className="space-y-4">
          <div className="overflow-x-auto rounded-md border border-border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Event Type</TableHead>
                  <TableHead>Severity</TableHead>
                  <TableHead>IP Address</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created At</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {securityEvents.map((event) => (
                  <TableRow key={event.id}>
                    <TableCell className="font-medium">{event.event_type}</TableCell>
                    <TableCell>
                      <Badge className={getSeverityColor(event.severity)}>
                        {event.severity}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-mono">{event.ip_address}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(event.status)}>
                        {event.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{new Date(event.created_at).toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedEvent(event);
                          setIsEventModalOpen(true);
                        }}
                        tabIndex={0}
                        aria-label={`View details for ${event.event_type}`}
                      >
                        <FiEye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        <TabsContent value="actions" className="space-y-4">
          <div className="overflow-x-auto rounded-md border border-border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Admin</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Target</TableHead>
                  <TableHead>Performed At</TableHead>
                  <TableHead>IP Address</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {adminActions.map((action) => (
                  <TableRow key={action.id}>
                    <TableCell className="font-medium">{action.admin_username}</TableCell>
                    <TableCell>{action.action_type}</TableCell>
                    <TableCell className="font-mono">{action.target_identifier}</TableCell>
                    <TableCell>{new Date(action.performed_at).toLocaleDateString()}</TableCell>
                    <TableCell className="font-mono">{action.ip_address}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
      </Tabs>
      {/* Ban IP Modal */}
      <Dialog open={isBanModalOpen} onOpenChange={setIsBanModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Ban IP Address</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleBanIP} className="space-y-4">
            <div>
              <Label htmlFor="ip_address">IP Address</Label>
              <Input
                id="ip_address"
                value={banFormData.ip_address}
                onChange={(e) => setBanFormData(prev => ({ ...prev, ip_address: e.target.value }))}
                required
                placeholder="***********"
              />
            </div>
            <div>
              <Label htmlFor="reason">Reason</Label>
              <Input
                id="reason"
                value={banFormData.reason}
                onChange={(e) => setBanFormData(prev => ({ ...prev, reason: e.target.value }))}
                required
                placeholder="Suspicious activity detected"
              />
            </div>
            <div>
              <Label htmlFor="duration_hours">Duration (hours, 0 for permanent)</Label>
              <Input
                id="duration_hours"
                type="number"
                value={banFormData.duration_hours}
                onChange={(e) => setBanFormData(prev => ({ ...prev, duration_hours: e.target.value }))}
                min="0"
                placeholder="24"
              />
            </div>
            <div>
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Textarea
                id="notes"
                value={banFormData.notes}
                onChange={(e) => setBanFormData(prev => ({ ...prev, notes: e.target.value }))}
                rows={3}
                placeholder="Additional details..."
              />
            </div>
            {error && <p className="text-sm text-red-500">Error: {error}</p>}
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsBanModalOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Banning...' : 'Ban IP'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      {/* Security Event Details Modal */}
      <Dialog open={isEventModalOpen} onOpenChange={setIsEventModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Security Event Details</DialogTitle>
          </DialogHeader>
          {selectedEvent && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Event Type</Label>
                  <p className="font-medium">{selectedEvent.event_type}</p>
                </div>
                <div>
                  <Label>Severity</Label>
                  <Badge className={getSeverityColor(selectedEvent.severity)}>
                    {selectedEvent.severity}
                  </Badge>
                </div>
                <div>
                  <Label>IP Address</Label>
                  <p className="font-mono">{selectedEvent.ip_address}</p>
                </div>
                <div>
                  <Label>Status</Label>
                  <Badge className={getStatusColor(selectedEvent.status)}>
                    {selectedEvent.status}
                  </Badge>
                </div>
              </div>
              <div>
                <Label>User Agent</Label>
                <p className="text-sm text-gray-600 break-all">{selectedEvent.user_agent}</p>
              </div>
              <div>
                <Label>Details</Label>
                <pre className="text-sm bg-gray-50 p-2 rounded overflow-auto">
                  {JSON.stringify(selectedEvent.details, null, 2)}
                </pre>
              </div>
              <div>
                <Label>Created At</Label>
                <p>{new Date(selectedEvent.created_at).toLocaleString()}</p>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button onClick={() => setIsEventModalOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {/* Security Settings Modal */}
      <Dialog open={isSettingsModalOpen} onOpenChange={setIsSettingsModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Security Settings</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {Object.entries(securitySettings).map(([key, setting]) => (
              <div key={key} className="flex items-center justify-between p-3 border rounded">
                <div>
                  <p className="font-medium">{setting.setting_key}</p>
                  <p className="text-sm text-gray-600">{setting.description}</p>
                </div>
                <div className="flex items-center gap-2">
                  {typeof setting.setting_value === 'boolean' || setting.setting_value === 'true' || setting.setting_value === 'false' ? (
                    <Switch
                      checked={setting.setting_value === 'true'}
                      onCheckedChange={(checked) => handleUpdateSecuritySetting(key, checked.toString())}
                    />
                  ) : (
                    <Input
                      value={setting.setting_value}
                      onChange={(e) => handleUpdateSecuritySetting(key, e.target.value)}
                      className="w-32"
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
          <DialogFooter>
            <Button onClick={() => setIsSettingsModalOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
export default SecurityManagement; 