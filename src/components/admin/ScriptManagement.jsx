import { useState, useEffect } from 'react';
import { FiPlus, FiEdit, FiTrash, FiEye, FiStar, FiTag, FiMonitor, FiCode } from 'react-icons/fi';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '../ui/Table';
import { Label } from '../ui/Label';
import { Textarea } from '../ui/Textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '../ui/Dialog';
import { Badge } from '../ui/Badge';
import { Switch } from '../ui/Switch';
import { useAuth } from '../../context/AuthContext';
const ScriptManagement = () => {
  const [scripts, setScripts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isUpdateLogModalOpen, setIsUpdateLogModalOpen] = useState(false);
  const [currentScript, setCurrentScript] = useState(null);
  const [scriptFormData, setScriptFormData] = useState({
    name: '',
    description: '',
    content: '',
    file_url: '',
    category: 'other',
    tags: [],
    executor: '',
    version: '1.0.0',
    is_active: true,
  });
  const [updateLogData, setUpdateLogData] = useState({
    version: '',
    changes: '',
  });
  const [tagInput, setTagInput] = useState('');
  const { token, isAuthenticated } = useAuth();
  const getAdminHeaders = () => ({
    'x-session-token': sessionStorage.getItem('adminSessionToken') || '',
    'Content-Type': 'application/json',
  });
  const fetchScripts = async () => {
    setLoading(true);
    setError(null);
    if (!isAuthenticated) {
      setError('User not authenticated.');
      setLoading(false);
      return;
    }
    try {
      const response = await fetch('/.netlify/functions/security?action=scripts', {
        headers: {
          ...getAdminHeaders(),
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `Failed with status ${response.status}`;
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch (parseError) {
          errorMessage = `API Error: ${response.status} - ${errorText || 'Unknown error'}`;
        }
        throw new Error(errorMessage);
      }
      const data = await response.json();
      setScripts(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (isAuthenticated) {
      fetchScripts();
    }
  }, [isAuthenticated, token]);
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setScriptFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };
  const handleTagInputKeyDown = (e) => {
    if (e.key === 'Enter' && tagInput.trim()) {
      e.preventDefault();
      if (!scriptFormData.tags.includes(tagInput.trim())) {
        setScriptFormData((prev) => ({
          ...prev,
          tags: [...prev.tags, tagInput.trim()],
        }));
      }
      setTagInput('');
    }
  };
  const handleRemoveTag = (tagToRemove) => {
    setScriptFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((tag) => tag !== tagToRemove),
    }));
  };
  const handleOpenModal = (script = null) => {
    setCurrentScript(script);
    setScriptFormData({
      name: script ? script.name : '',
      description: script ? script.description : '',
      content: script ? script.content : '',
      file_url: script ? script.file_url : '',
      category: script ? script.category : 'other',
      tags: script ? (Array.isArray(script.tags) ? script.tags : []) : [],
      executor: script ? script.executor : '',
      version: script ? script.version : '1.0.0',
      is_active: script ? script.is_active : true,
    });
    setIsModalOpen(true);
  };
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setCurrentScript(null);
    setScriptFormData({
      name: '',
      description: '',
      content: '',
      file_url: '',
      category: 'other',
      tags: [],
      executor: '',
      version: '1.0.0',
      is_active: true,
    });
    setError(null);
  };
  const handleSaveScript = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    if (!isAuthenticated) {
      setError('User not authenticated.');
      setLoading(false);
      return;
    }
    try {
      let response;
      if (currentScript) {
        // Update existing script
        response = await fetch('/.netlify/functions/security?action=update-script', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            ...getAdminHeaders(),
          },
          body: JSON.stringify({ id: currentScript.id, ...scriptFormData }),
        });
      } else {
        // Add new script
        response = await fetch('/.netlify/functions/security?action=create-script', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...getAdminHeaders(),
          },
          body: JSON.stringify(scriptFormData),
        });
      }
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || 'Failed to save script');
      }
      handleCloseModal();
      fetchScripts();
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  const handleDeleteScript = async (scriptId) => {
    if (!window.confirm('Are you sure you want to delete this script?')) {
      return;
    }
    setLoading(true);
    setError(null);
    if (!isAuthenticated) {
      setError('User not authenticated.');
      setLoading(false);
      return;
    }
    try {
      const response = await fetch(`/.netlify/functions/security?action=delete-script&id=${scriptId}`, {
        method: 'DELETE',
        headers: {
          ...getAdminHeaders(),
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to delete script');
      }
      fetchScripts();
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  const handleOpenUpdateLogModal = (script) => {
    setCurrentScript(script);
    setUpdateLogData({
      version: script.version || '1.0.0',
      changes: '',
    });
    setIsUpdateLogModalOpen(true);
  };
  const handleCloseUpdateLogModal = () => {
    setIsUpdateLogModalOpen(false);
    setCurrentScript(null);
    setUpdateLogData({
      version: '',
      changes: '',
    });
  };
  const handleSaveUpdateLog = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    if (!isAuthenticated) {
      setError('User not authenticated.');
      setLoading(false);
      return;
    }
    try {
      const response = await fetch('/.netlify/functions/security?action=update-log', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...getAdminHeaders(),
        },
        body: JSON.stringify({
          script_id: currentScript.id,
          version: updateLogData.version,
          changes: updateLogData.changes,
          updated_by: 'admin' // Will be determined by session token on backend
        }),
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || 'Failed to save update log');
      }
      handleCloseUpdateLogModal();
      fetchScripts();
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  const getCategoryColor = (category) => {
    switch (category) {
      case 'game':
        return 'bg-info/10 text-info';
      case 'utility':
        return 'bg-success/10 text-success';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };
  const formatViews = (views) => {
    if (views >= 1000000) {
      return `${(views / 1000000).toFixed(1)}M`;
    } else if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K`;
    }
    return views.toString();
  };
  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-foreground">Script Management</h2>
        <Button
          onClick={() => handleOpenModal()}
          className="gap-2"
          tabIndex={0}
          aria-label="Add new script"
        >
          <FiPlus className="h-5 w-5" />
          Add Script
        </Button>
      </div>
      {loading && <p className="text-center text-muted-foreground">Loading scripts...</p>}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-800 font-medium">Error:</p>
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}
      {!loading && !error && scripts.length === 0 && (
        <p className="text-center text-muted-foreground">No scripts found.</p>
      )}
      {!loading && !error && scripts.length > 0 && (
        <div className="overflow-x-auto rounded-md border border-border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Views</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Version</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Uploaded By</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {scripts.map((script) => (
                <TableRow key={script.id}>
                  <TableCell className="font-medium">{script.name}</TableCell>
                  <TableCell>
                    <Badge className={getCategoryColor(script.category)}>
                      {script.category}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatViews(script.views || 0)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <FiStar className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                      <span>{(script.rating || 0).toFixed(1)}</span>
                      <span className="text-gray-500">({script.rating_count || 0})</span>
                    </div>
                  </TableCell>
                  <TableCell>{script.version || '1.0.0'}</TableCell>
                  <TableCell>
                    <Badge variant={script.is_active ? 'success' : 'destructive'}>
                      {script.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell>{script.uploaded_by}</TableCell>
                  <TableCell>{new Date(script.created_at).toLocaleDateString()}</TableCell>
                  <TableCell className="text-right flex space-x-2 justify-end">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleOpenModal(script)}
                      tabIndex={0}
                      aria-label={`Edit ${script.name}`}
                    >
                      <FiEdit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleOpenUpdateLogModal(script)}
                      tabIndex={0}
                      aria-label={`Add update log for ${script.name}`}
                    >
                      <FiCode className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDeleteScript(script.id)}
                      tabIndex={0}
                      aria-label={`Delete ${script.name}`}
                    >
                      <FiTrash className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
      {/* Script Form Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{currentScript ? 'Edit Script' : 'Add New Script'}</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSaveScript} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Script Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={scriptFormData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div>
                <Label htmlFor="category">Category</Label>
                <select
                  id="category"
                  name="category"
                  value={scriptFormData.category}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="game">Game</option>
                  <option value="utility">Utility</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={scriptFormData.description}
                onChange={handleInputChange}
                required
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="content">Script Content</Label>
              <Textarea
                id="content"
                name="content"
                value={scriptFormData.content}
                onChange={handleInputChange}
                required
                rows={8}
                placeholder="-- Paste your script content here"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="file_url">File URL (Optional)</Label>
                <Input
                  id="file_url"
                  name="file_url"
                  value={scriptFormData.file_url}
                  onChange={handleInputChange}
                  placeholder="https://example.com/script.lua"
                />
              </div>
              <div>
                <Label htmlFor="version">Version</Label>
                <Input
                  id="version"
                  name="version"
                  value={scriptFormData.version}
                  onChange={handleInputChange}
                  placeholder="1.0.0"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="executor">Supported Executors</Label>
              <Input
                id="executor"
                name="executor"
                value={scriptFormData.executor}
                onChange={handleInputChange}
                placeholder="Synapse X, ScriptWare, KRNL"
              />
            </div>
            <div>
              <Label>Tags</Label>
              <div className="flex flex-wrap gap-2 mb-2">
                {scriptFormData.tags.map((tag, index) => (
                  <Badge key={index} className="bg-blue-100 text-blue-800">
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </Badge>
                ))}
              </div>
              <Input
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyDown={handleTagInputKeyDown}
                placeholder="Type a tag and press Enter"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="is_active"
                name="is_active"
                checked={scriptFormData.is_active}
                onCheckedChange={(checked) =>
                  setScriptFormData((prev) => ({ ...prev, is_active: checked }))
                }
              />
              <Label htmlFor="is_active">Active (Published)</Label>
            </div>
            {error && <p className="text-sm text-red-500">Error: {error}</p>}
            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleCloseModal}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Saving...' : 'Save Script'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      {/* Update Log Modal */}
      <Dialog open={isUpdateLogModalOpen} onOpenChange={setIsUpdateLogModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Update Log for {currentScript?.name}</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSaveUpdateLog} className="space-y-4">
            <div>
              <Label htmlFor="update-version">Version</Label>
              <Input
                id="update-version"
                value={updateLogData.version}
                onChange={(e) => setUpdateLogData((prev) => ({ ...prev, version: e.target.value }))}
                required
                placeholder="1.1.0"
              />
            </div>
            <div>
              <Label htmlFor="update-changes">Changes</Label>
              <Textarea
                id="update-changes"
                value={updateLogData.changes}
                onChange={(e) => setUpdateLogData((prev) => ({ ...prev, changes: e.target.value }))}
                required
                rows={4}
                placeholder="Describe the changes in this update..."
              />
            </div>
            {error && <p className="text-sm text-red-500">Error: {error}</p>}
            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleCloseUpdateLogModal}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Saving...' : 'Add Update Log'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};
export default ScriptManagement; 