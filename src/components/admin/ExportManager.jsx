import { useState } from 'react';
import { FiDownload, FiCalendar, FiFileText, FiBarChart2, FiUsers, FiKey, FiDollarSign, FiSettings } from 'react-icons/fi';
// Helper to get admin session headers for API calls
const getAdminHeaders = () => {
  const sessionToken = sessionStorage.getItem('adminSessionToken');
  return {
    'x-session-token': sessionToken || '',
    'Content-Type': 'application/json',
  };
};
const ExportManager = ({ onClose }) => {
  const [exportType, setExportType] = useState('summary');
  const [dateRange, setDateRange] = useState('7d');
  const [customStartDate, setCustomStartDate] = useState('');
  const [customEndDate, setCustomEndDate] = useState('');
  const [format, setFormat] = useState('csv');
  const [includeCharts, setIncludeCharts] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const exportTypes = [
    { value: 'summary', label: 'Summary Report', icon: <FiBarChart2 className="h-4 w-4" />, description: 'Key metrics and overview' },
    { value: 'key-trends', label: 'Key Generation Trends', icon: <FiKey className="h-4 w-4" />, description: 'Daily key generation data' },
    { value: 'user-activity', label: 'User Activity', icon: <FiUsers className="h-4 w-4" />, description: 'User engagement patterns' },
    { value: 'revenue', label: 'Revenue Analysis', icon: <FiDollarSign className="h-4 w-4" />, description: 'Financial performance data' },
    { value: 'comprehensive', label: 'Comprehensive Report', icon: <FiFileText className="h-4 w-4" />, description: 'All data combined' }
  ];
  const dateRanges = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: '1y', label: 'Last year' },
    { value: 'custom', label: 'Custom range' }
  ];
  const formats = [
    { value: 'csv', label: 'CSV', description: 'Spreadsheet format' },
    { value: 'pdf', label: 'PDF', description: 'Document format' },
    { value: 'json', label: 'JSON', description: 'Raw data format' }
  ];
  const handleExport = async () => {
    setLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams({
        action: 'export',
        type: exportType,
        format,
        range: dateRange,
        includeCharts: includeCharts.toString()
      });
      if (dateRange === 'custom') {
        if (!customStartDate || !customEndDate) {
          throw new Error('Please select both start and end dates');
        }
        params.append('startDate', customStartDate);
        params.append('endDate', customEndDate);
      }
      const response = await fetch(`/.netlify/functions/analytics?${params}`, {
        headers: getAdminHeaders()
      });
      if (!response.ok) {
        throw new Error('Export failed');
      }
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      const timestamp = new Date().toISOString().split('T')[0];
      a.download = `analytics-${exportType}-${dateRange}-${timestamp}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      // Close modal after successful export
      onClose();
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  const getFileSizeEstimate = () => {
    const baseSizes = {
      'summary': 50,
      'key-trends': 200,
      'user-activity': 300,
      'revenue': 150,
      'comprehensive': 800
    };
    const rangeMultipliers = {
      '7d': 1,
      '30d': 4,
      '90d': 12,
      '1y': 52,
      'custom': 8
    };
    const formatMultipliers = {
      'csv': 1,
      'pdf': 2,
      'json': 1.5
    };
    const baseSize = baseSizes[exportType] || 100;
    const rangeMultiplier = rangeMultipliers[dateRange] || 1;
    const formatMultiplier = formatMultipliers[format] || 1;
    return Math.round(baseSize * rangeMultiplier * formatMultiplier);
  };
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-white flex items-center gap-2">
            <FiDownload className="h-5 w-5" />
            Export Analytics Data
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
            aria-label="Close"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        {/* Export Type Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-300 mb-3">Export Type</label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {exportTypes.map((type) => (
              <button
                key={type.value}
                onClick={() => setExportType(type.value)}
                className={`p-4 rounded-lg border transition-colors text-left ${
                  exportType === type.value
                    ? 'border-blue-500 bg-blue-500 bg-opacity-10'
                    : 'border-gray-600 bg-gray-700 hover:border-gray-500'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded ${
                    exportType === type.value ? 'bg-blue-500 text-white' : 'bg-gray-600 text-gray-300'
                  }`}>
                    {type.icon}
                  </div>
                  <div>
                    <div className="font-medium text-white">{type.label}</div>
                    <div className="text-sm text-gray-400">{type.description}</div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
        {/* Date Range Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-300 mb-3">Date Range</label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {dateRanges.map((range) => (
              <button
                key={range.value}
                onClick={() => setDateRange(range.value)}
                className={`p-3 rounded-lg border transition-colors ${
                  dateRange === range.value
                    ? 'border-blue-500 bg-blue-500 bg-opacity-10 text-blue-400'
                    : 'border-gray-600 bg-gray-700 hover:border-gray-500 text-gray-300'
                }`}
              >
                {range.label}
              </button>
            ))}
          </div>
          {/* Custom Date Range */}
          {dateRange === 'custom' && (
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Start Date</label>
                <input
                  type="date"
                  value={customStartDate}
                  onChange={(e) => setCustomStartDate(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-3 py-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">End Date</label>
                <input
                  type="date"
                  value={customEndDate}
                  onChange={(e) => setCustomEndDate(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-3 py-2"
                />
              </div>
            </div>
          )}
        </div>
        {/* Format Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-300 mb-3">Export Format</label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {formats.map((fmt) => (
              <button
                key={fmt.value}
                onClick={() => setFormat(fmt.value)}
                className={`p-3 rounded-lg border transition-colors ${
                  format === fmt.value
                    ? 'border-blue-500 bg-blue-500 bg-opacity-10 text-blue-400'
                    : 'border-gray-600 bg-gray-700 hover:border-gray-500 text-gray-300'
                }`}
              >
                <div className="font-medium">{fmt.label}</div>
                <div className="text-xs text-gray-400">{fmt.description}</div>
              </button>
            ))}
          </div>
        </div>
        {/* Options */}
        <div className="mb-6">
          <label className="flex items-center gap-3 cursor-pointer">
            <input
              type="checkbox"
              checked={includeCharts}
              onChange={(e) => setIncludeCharts(e.target.checked)}
              className="rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-300">Include charts and visualizations (PDF only)</span>
          </label>
        </div>
        {/* File Size Estimate */}
        <div className="mb-6 p-4 bg-gray-700 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-300">Estimated file size:</span>
            <span className="text-sm font-medium text-white">~{getFileSizeEstimate()} KB</span>
          </div>
        </div>
        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-900 border border-red-700 rounded-lg">
            <div className="text-red-300 text-sm">{error}</div>
          </div>
        )}
        {/* Action Buttons */}
        <div className="flex items-center justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-300 hover:text-white transition-colors"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            onClick={handleExport}
            disabled={loading}
            className="flex items-center gap-2 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Exporting...
              </>
            ) : (
              <>
                <FiDownload className="h-4 w-4" />
                Export Data
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};
export default ExportManager; 