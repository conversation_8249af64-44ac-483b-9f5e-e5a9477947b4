import React, { useState, useRef } from 'react';
import { Copy, Download, Upload, Shield, Zap, AlertTriangle, CheckCircle, FileText, Settings } from 'lucide-react';

const ObfuscatorPanel = () => {
    const [luaCode, setLuaCode] = useState('');
    const [obfuscatedCode, setObfuscatedCode] = useState('');
    const [hwid, setHwid] = useState('UNIVERSAL');
    const [isObfuscating, setIsObfuscating] = useState(false);
    const [statistics, setStatistics] = useState(null);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const fileInputRef = useRef(null);

    // Get admin headers for authentication
    const getAdminHeaders = () => {
        const sessionToken = sessionStorage.getItem('adminSessionToken');
        const headers = {
            'Content-Type': 'application/json',
        };

        if (sessionToken) {
            headers['x-session-token'] = sessionToken;
        }

        return headers;
    };

    // Handle file upload
    const handleFileUpload = (event) => {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                setLuaCode(e.target.result);
                setError('');
                setSuccess(`File "${file.name}" loaded successfully!`);
            };
            reader.readAsText(file);
        }
    };

    // Trigger file upload
    const triggerFileUpload = () => {
        fileInputRef.current?.click();
    };

    // Obfuscate code
    const handleObfuscate = async () => {
        if (!luaCode.trim()) {
            setError('Please enter Lua code to obfuscate');
            return;
        }

        setIsObfuscating(true);
        setError('');
        setSuccess('');
        setObfuscatedCode('');
        setStatistics(null);

        try {
            // Add timeout and better error handling
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

            const response = await fetch('/.netlify/functions/obfuscate', {
                method: 'POST',
                headers: getAdminHeaders(),
                body: JSON.stringify({
                    luaCode: luaCode,
                    hwid: hwid,
                    options: {
                        enableAntiClipboard: true,
                        enableHWIDBinding: hwid !== 'UNIVERSAL',
                        enableStringObfuscation: true,
                        enableVariableMangling: true,
                        enableControlFlowObfuscation: true
                    }
                }),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            // Handle different response types
            if (response.status === 504) {
                throw new Error('Obfuscation timeout - your code is very complex. Try breaking it into smaller parts.');
            }

            if (response.status === 413) {
                throw new Error('Code too large - please reduce the size of your script.');
            }

            if (!response.ok) {
                const errorText = await response.text();
                let errorMessage = 'Obfuscation failed';

                try {
                    const errorData = JSON.parse(errorText);
                    errorMessage = errorData.error || errorMessage;
                } catch {
                    errorMessage = errorText || errorMessage;
                }

                throw new Error(errorMessage);
            }

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'Obfuscation failed');
            }

            setObfuscatedCode(data.obfuscatedCode);
            setStatistics(data.statistics);
            setSuccess('🎉 Nuclear-level obfuscation completed successfully! Your code is now undecodable.');

        } catch (err) {
            if (err.name === 'AbortError') {
                setError('Obfuscation timeout - your code is very complex. Try breaking it into smaller parts or simplifying it.');
            } else if (err.message.includes('Failed to fetch')) {
                setError('Network error - please check your connection and try again.');
            } else {
                setError(err.message);
            }
        } finally {
            setIsObfuscating(false);
        }
    };

    // Copy to clipboard
    const copyToClipboard = async (text, type) => {
        try {
            await navigator.clipboard.writeText(text);
            setSuccess(`${type} copied to clipboard!`);
        } catch (err) {
            setError('Failed to copy to clipboard');
        }
    };

    // Download as file
    const downloadAsFile = (content, filename) => {
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        setSuccess(`File "${filename}" downloaded!`);
    };

    // Clear all
    const clearAll = () => {
        setLuaCode('');
        setObfuscatedCode('');
        setHwid('UNIVERSAL');
        setStatistics(null);
        setError('');
        setSuccess('');
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6 rounded-lg">
                <div className="flex items-center space-x-3">
                    <Shield className="w-8 h-8" />
                    <div>
                        <h1 className="text-2xl font-bold">Madara Obfuscator</h1>
                    </div>
                </div>
            </div>

            {/* Status Messages */}
            {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-center space-x-2">
                    <AlertTriangle className="w-5 h-5" />
                    <span>{error}</span>
                </div>
            )}

            {success && (
                <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-center space-x-2">
                    <CheckCircle className="w-5 h-5" />
                    <span>{success}</span>
                </div>
            )}

            {/* Configuration */}
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                <div className="flex items-center space-x-2 mb-4">
                    <Settings className="w-5 h-5 text-gray-600" />
                    <h2 className="text-lg font-semibold">Configuration</h2>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Hardware ID (HWID)
                        </label>
                        <input
                            type="text"
                            value={hwid}
                            onChange={(e) => setHwid(e.target.value)}
                            placeholder="Enter HWID or leave as UNIVERSAL"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                            Use "UNIVERSAL" for scripts that work on any device, or enter specific HWID for user-locked scripts
                        </p>
                    </div>
                    <div className="flex items-end">
                        <button
                            onClick={clearAll}
                            className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
                        >
                            Clear All
                        </button>
                    </div>
                </div>
            </div>

            {/* Input Section */}
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                        <FileText className="w-5 h-5 text-gray-600" />
                        <h2 className="text-lg font-semibold">Input Lua Code</h2>
                    </div>
                    <div className="flex space-x-2">
                        <button
                            onClick={triggerFileUpload}
                            className="flex items-center space-x-2 px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                        >
                            <Upload className="w-4 h-4" />
                            <span>Upload File</span>
                        </button>
                        <input
                            ref={fileInputRef}
                            type="file"
                            accept=".lua,.txt"
                            onChange={handleFileUpload}
                            className="hidden"
                        />
                    </div>
                </div>
                
                <textarea
                    value={luaCode}
                    onChange={(e) => setLuaCode(e.target.value)}
                    placeholder="Paste your Lua code here or upload a file...

-- Example:
local function myScript()
    print('Hello, World!')
    game.Players.LocalPlayer.Character.Humanoid.WalkSpeed = 50
end

myScript()"
                    className="w-full h-64 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 font-mono text-sm"
                />
                
                <div className="flex items-center justify-between mt-4">
                    <span className="text-sm text-gray-500">
                        {luaCode.length.toLocaleString()} characters, {luaCode.split('\n').length} lines
                        {luaCode.length > 3000 && <span className="text-green-600 font-medium"> (Large script - nuclear protection will take ~5-10 seconds)</span>}
                        {luaCode.length > 10000 && <span className="text-orange-600 font-medium"> (Very large script - may take up to 30 seconds)</span>}
                    </span>
                    <button
                        onClick={handleObfuscate}
                        disabled={isObfuscating || !luaCode.trim()}
                        className="flex items-center space-x-2 px-6 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-md hover:from-purple-700 hover:to-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-all"
                    >
                        {isObfuscating ? (
                            <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                <span>Applying Nuclear Protection...</span>
                            </>
                        ) : (
                            <>
                                <Zap className="w-4 h-4" />
                                <span>🚀 Obfuscate Code</span>
                            </>
                        )}
                    </button>
                </div>
            </div>

            {/* Statistics */}
            {statistics && (
                <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                    <h2 className="text-lg font-semibold mb-4">Obfuscation Statistics</h2>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center p-4 bg-blue-50 rounded-lg">
                            <div className="text-2xl font-bold text-blue-600">{statistics.originalSize.toLocaleString()}</div>
                            <div className="text-sm text-gray-500">Original Size (bytes)</div>
                        </div>
                        <div className="text-center p-4 bg-green-50 rounded-lg">
                            <div className="text-2xl font-bold text-green-600">{statistics.obfuscatedSize.toLocaleString()}</div>
                            <div className="text-sm text-gray-500">Obfuscated Size (bytes)</div>
                        </div>
                        <div className="text-center p-4 bg-purple-50 rounded-lg">
                            <div className="text-2xl font-bold text-purple-600">
                                {statistics.compressionRatio ? `${statistics.compressionRatio}x` : `${Math.round(statistics.obfuscatedSize / statistics.originalSize)}x`}
                            </div>
                            <div className="text-sm text-gray-500">Size Ratio</div>
                        </div>
                        <div className="text-center p-4 bg-orange-50 rounded-lg">
                            <div className="text-2xl font-bold text-orange-600">
                                {statistics.processingTime ? `${statistics.processingTime}ms` : 'N/A'}
                            </div>
                            <div className="text-sm text-gray-500">Processing Time</div>
                        </div>
                    </div>
                    <div className="mt-4">
                        <div className="text-sm font-medium text-gray-700 mb-2">Protection Features Applied:</div>
                        <div className="flex flex-wrap gap-2">
                            {Array.isArray(statistics.featuresApplied) ? (
                                statistics.featuresApplied.map((feature, index) => (
                                    <span
                                        key={index}
                                        className="px-3 py-1 bg-purple-100 text-purple-800 text-xs rounded-full font-medium"
                                    >
                                        {feature.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                    </span>
                                ))
                            ) : (
                                <span className="px-3 py-1 bg-purple-100 text-purple-800 text-xs rounded-full font-medium">
                                    {statistics.featuresApplied || 0} Nuclear Protection Features Applied
                                </span>
                            )}
                            {statistics.protectionLayers && (
                                <span className="px-3 py-1 bg-red-100 text-red-800 text-xs rounded-full font-medium">
                                    {statistics.protectionLayers} Protection Layers
                                </span>
                            )}
                        </div>
                    </div>
                </div>
            )}

            {/* Output Section */}
            {obfuscatedCode && (
                <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                    <div className="flex items-center justify-between mb-4">
                        <h2 className="text-lg font-semibold text-green-700">Protected Code (Ready to Use)</h2>
                        <div className="flex space-x-2">
                            <button
                                onClick={() => copyToClipboard(obfuscatedCode, 'Protected code')}
                                className="flex items-center space-x-2 px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                            >
                                <Copy className="w-4 h-4" />
                                <span>Copy</span>
                            </button>
                            <button
                                onClick={() => downloadAsFile(obfuscatedCode, 'madara_protected_script.lua')}
                                className="flex items-center space-x-2 px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
                            >
                                <Download className="w-4 h-4" />
                                <span>Download</span>
                            </button>
                        </div>
                    </div>
                    
                    <textarea
                        value={obfuscatedCode}
                        readOnly
                        className="w-full h-64 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 font-mono text-sm"
                    />
                    
                    <div className="mt-4 flex items-center justify-between">
                        <span className="text-sm text-gray-500">
                            {obfuscatedCode.length.toLocaleString()} characters, {obfuscatedCode.split('\n').length} lines
                        </span>
                        <div className="flex items-center space-x-2 text-sm text-green-600">
                            <Shield className="w-4 h-4" />
                            <span className="font-medium">Maximum Protection Applied</span>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ObfuscatorPanel;
