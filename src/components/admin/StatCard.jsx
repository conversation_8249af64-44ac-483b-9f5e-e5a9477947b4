import React from 'react';
import { cn } from '../../lib/utils';
const StatCard = ({ title, value, icon, accent = 'bg-primary' }) => (
  <div className={cn(
    'relative flex items-center gap-4 p-6 rounded-2xl shadow-xl bg-card border border-border',
    'transition-all duration-200 group hover:scale-[1.03] hover:shadow-2xl'
  )}>
    <div className={cn(
      'flex items-center justify-center w-12 h-12 rounded-xl text-white',
      accent
    )}>
      {icon}
    </div>
    <div>
      <div className="text-sm text-muted-foreground font-medium mb-1">{title}</div>
      <div className="text-2xl font-extrabold text-foreground">{value}</div>
    </div>
  </div>
);
export default StatCard; 