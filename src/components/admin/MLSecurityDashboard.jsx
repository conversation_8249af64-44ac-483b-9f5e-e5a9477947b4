import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { Card } from '../ui/Card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/Tabs';
import { Badge } from '../ui/Badge';
import { Button } from '../ui/Button';
import { AlertTriangle, Brain, Activity, Shield, TrendingUp, Users, Eye } from 'lucide-react';
const MLSecurityDashboard = () => {
  const { admin, hasPermission } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [mlAnalytics, setMLAnalytics] = useState({
    totalAnalyses: 0,
    botDetections: 0,
    anomalies: 0,
    accuracy: 0
  });
  const [behaviorProfiles, setBehaviorProfiles] = useState([]);
  const [securityEvents, setSecurityEvents] = useState([]);
  const [realTimeData, setRealTimeData] = useState({
    activeUsers: 0,
    suspiciousActivity: 0,
    mlConfidence: 0
  });
  const getAdminHeaders = () => ({
    'x-session-token': sessionStorage.getItem('adminSessionToken') || '',
    'Content-Type': 'application/json',
  });
  const fetchMLSecurityData = async () => {
    if (!hasPermission('ml_analysis')) {
      setError('Insufficient permissions for ML Security data');
      setLoading(false);
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const analyticsResponse = await fetch('/.netlify/functions/admin-keys?action=security-events', {
        headers: getAdminHeaders(),
      });
      if (analyticsResponse.ok) {
        const data = await analyticsResponse.json();
        const mlAnalytics = {
          totalAnalyses: data.behaviorProfiles?.length || 0,
          botDetections: data.securityEvents?.filter(e =>
            e.event_type?.includes('ml_bot_detected') ||
            e.event_type?.includes('bot_behavior')
          ).length || 0,
          anomalies: data.behaviorProfiles?.filter(b =>
            (b.anomaly_score || 0) > 0.7
          ).length || 0,
          accuracy: 95 // Default accuracy
        };
        setMLAnalytics(mlAnalytics);
      }
      const profilesResponse = await fetch('/.netlify/functions/admin-keys?action=behavior-profiles', {
        headers: getAdminHeaders(),
      });
      if (profilesResponse.ok) {
        const profilesData = await profilesResponse.json();
        setBehaviorProfiles(Array.isArray(profilesData) ? profilesData : []);
      }
      const eventsResponse = await fetch('/.netlify/functions/security?action=events', {
        headers: getAdminHeaders(),
      });
      if (eventsResponse.ok) {
        const eventsData = await eventsResponse.json();
        setSecurityEvents(Array.isArray(eventsData) ? eventsData : []);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (admin && hasPermission('ml_analysis')) {
      fetchMLSecurityData();
      const interval = setInterval(fetchMLSecurityData, 30000);
      return () => clearInterval(interval);
    }
  }, [admin]);
  if (!hasPermission('ml_analysis')) {
    return (
      <div className="p-6 text-center">
        <AlertTriangle className="mx-auto h-12 w-12 text-yellow-500 mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
        <p className="text-gray-600">You don't have permission to access ML Security features.</p>
      </div>
    );
  }
  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };
  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return 'bg-destructive/10 text-destructive';
      case 'high': return 'bg-destructive/10 text-destructive';
      case 'medium': return 'bg-warning/10 text-warning';
      case 'low': return 'bg-info/10 text-info';
      default: return 'bg-muted text-muted-foreground';
    }
  };
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">ML Security Dashboard</h1>
          <p className="text-gray-600">Advanced machine learning security monitoring and analysis</p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            <Brain className="w-3 h-3 mr-1" />
            ML Security
          </Badge>
          <Button onClick={fetchMLSecurityData} disabled={loading} size="sm">
            <Activity className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>
      {loading && <p className="text-center text-gray-500">Loading ML security data...</p>}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-800 font-medium">Error:</p>
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="behavior">Behavior Analysis</TabsTrigger>
          <TabsTrigger value="events">Security Events</TabsTrigger>
          <TabsTrigger value="monitoring">Real-time Monitoring</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <Card.Header className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Card.Title className="text-sm font-medium">Total ML Analyses</Card.Title>
                <Brain className="h-4 w-4 text-blue-600" />
              </Card.Header>
              <Card.Content>
                <div className="text-2xl font-bold">{mlAnalytics.totalAnalyses}</div>
                <p className="text-xs text-gray-600">Behavioral patterns analyzed</p>
              </Card.Content>
            </Card>
            <Card>
              <Card.Header className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Card.Title className="text-sm font-medium">Bot Detections</Card.Title>
                <Shield className="h-4 w-4 text-red-600" />
              </Card.Header>
              <Card.Content>
                <div className="text-2xl font-bold">{mlAnalytics.botDetections}</div>
                <p className="text-xs text-gray-600">Automated behavior detected</p>
              </Card.Content>
            </Card>
            <Card>
              <Card.Header className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Card.Title className="text-sm font-medium">Anomalies</Card.Title>
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
              </Card.Header>
              <Card.Content>
                <div className="text-2xl font-bold">{mlAnalytics.anomalies}</div>
                <p className="text-xs text-gray-600">Suspicious patterns found</p>
              </Card.Content>
            </Card>
            <Card>
              <Card.Header className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Card.Title className="text-sm font-medium">ML Accuracy</Card.Title>
                <TrendingUp className="h-4 w-4 text-green-600" />
              </Card.Header>
              <Card.Content>
                <div className="text-2xl font-bold">{mlAnalytics.accuracy}%</div>
                <p className="text-xs text-gray-600">Detection accuracy rate</p>
              </Card.Content>
            </Card>
          </div>
          <Card>
            <Card.Header>
              <Card.Title>Recent ML Security Activity</Card.Title>
              <Card.Description>Latest machine learning security events and detections</Card.Description>
            </Card.Header>
            <Card.Content>
              <div className="space-y-3">
                {securityEvents.slice(0, 5).map((event, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-2 h-2 rounded-full ${
                        event.event_type?.includes('ml') ? 'bg-blue-500' : 'bg-gray-400'
                      }`} />
                      <div>
                        <p className="font-medium text-sm">{event.event_type || 'Unknown Event'}</p>
                        <p className="text-xs text-gray-600">{formatTimestamp(event.created_at)}</p>
                      </div>
                    </div>
                    <Badge className={getSeverityColor(event.severity)}>
                      {event.severity || 'unknown'}
                    </Badge>
                  </div>
                ))}
                {securityEvents.length === 0 && (
                  <p className="text-center text-gray-500 py-4">No recent ML security events</p>
                )}
              </div>
            </Card.Content>
          </Card>
        </TabsContent>
        <TabsContent value="behavior" className="space-y-6">
          <Card>
            <Card.Header>
              <Card.Title>User Behavior Profiles</Card.Title>
              <Card.Description>Detailed analysis of user behavioral patterns</Card.Description>
            </Card.Header>
            <Card.Content>
              <div className="space-y-4">
                {behaviorProfiles.map((profile, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-blue-600" />
                        <span className="font-medium">Profile #{profile.id?.slice(-8) || index + 1}</span>
                      </div>
                      <Badge className={profile.anomaly_score > 0.7 ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}>
                        Anomaly: {((profile.anomaly_score || 0) * 100).toFixed(1)}%
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Session Duration:</span>
                        <p className="font-medium">{Math.round((profile.session_duration || 0) / 1000)}s</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Mouse Movements:</span>
                        <p className="font-medium">{profile.mouse_movement_count || 0}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Clicks:</span>
                        <p className="font-medium">{profile.click_count || 0}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Typing Speed:</span>
                        <p className="font-medium">{profile.typing_speed_wpm || 0} WPM</p>
                      </div>
                    </div>
                  </div>
                ))}
                {behaviorProfiles.length === 0 && (
                  <p className="text-center text-gray-500 py-8">No behavior profiles available</p>
                )}
              </div>
            </Card.Content>
          </Card>
        </TabsContent>
        <TabsContent value="events" className="space-y-6">
          <Card>
            <Card.Header>
              <Card.Title>ML Security Events</Card.Title>
              <Card.Description>Comprehensive log of machine learning security events</Card.Description>
            </Card.Header>
            <Card.Content>
              <div className="space-y-3">
                {securityEvents.map((event, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <Eye className="h-4 w-4 text-gray-600" />
                        <span className="font-medium">{event.event_type || 'Unknown Event'}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={getSeverityColor(event.severity)}>
                          {event.severity || 'unknown'}
                        </Badge>
                        <span className="text-xs text-gray-500">{formatTimestamp(event.created_at)}</span>
                      </div>
                    </div>
                    {event.details && (
                      <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                        <pre className="whitespace-pre-wrap">{JSON.stringify(event.details, null, 2)}</pre>
                      </div>
                    )}
                  </div>
                ))}
                {securityEvents.length === 0 && (
                  <p className="text-center text-gray-500 py-8">No security events found</p>
                )}
              </div>
            </Card.Content>
          </Card>
        </TabsContent>
        <TabsContent value="monitoring" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <Card.Header>
                <Card.Title className="text-lg">Active Users</Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="text-3xl font-bold text-blue-600">{realTimeData.activeUsers}</div>
                <p className="text-sm text-gray-600">Currently being monitored</p>
              </Card.Content>
            </Card>
            <Card>
              <Card.Header>
                <Card.Title className="text-lg">Suspicious Activity</Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="text-3xl font-bold text-red-600">{realTimeData.suspiciousActivity}</div>
                <p className="text-sm text-gray-600">Flagged in last hour</p>
              </Card.Content>
            </Card>
            <Card>
              <Card.Header>
                <Card.Title className="text-lg">ML Confidence</Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="text-3xl font-bold text-green-600">{realTimeData.mlConfidence}%</div>
                <p className="text-sm text-gray-600">Average detection confidence</p>
              </Card.Content>
            </Card>
          </div>
          <Card>
            <Card.Header>
              <Card.Title>Real-time ML Monitoring</Card.Title>
              <Card.Description>Live feed of machine learning security analysis</Card.Description>
            </Card.Header>
            <Card.Content>
              <div className="text-center py-8 text-gray-500">
                <Activity className="mx-auto h-12 w-12 mb-4" />
                <p>Real-time monitoring dashboard</p>
                <p className="text-sm">Live ML analysis data will appear here</p>
              </div>
            </Card.Content>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
export default MLSecurityDashboard;
