import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FiShield,
  FiAlertTriangle,
  FiActivity,
  FiUsers,
  FiEye,
  FiSettings,
  FiRefreshCw,
  FiDownload,
  FiTrash2
} from 'react-icons/fi';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { formatHashForDisplay, generateShortId } from '../../utils/ipAnonymization';
const SecurityDashboard = () => {
  const [securityLogs, setSecurityLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    totalEvents: 0,
    botDetections: 0,
    adminBypasses: 0,
    securityViolations: 0
  });

  const getAdminHeaders = () => ({
    'x-session-token': sessionStorage.getItem('adminSessionToken') || '',
    'Content-Type': 'application/json',
  });

  useEffect(() => {
    loadSecurityData();
    const interval = setInterval(loadSecurityData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadSecurityData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Fetch real security events from the database
      const response = await fetch('/.netlify/functions/security?action=events', {
        headers: getAdminHeaders(),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch security events');
      }

      const events = await response.json();
      const eventsArray = Array.isArray(events) ? events : [];

      setSecurityLogs(eventsArray.slice(-50)); // Show last 50 events

      // Calculate real statistics from the events
      const stats = {
        totalEvents: eventsArray.length,
        botDetections: eventsArray.filter(event =>
          event.event_type?.includes('bot_detected') ||
          event.event_type?.includes('ml_bot') ||
          event.event_type === 'BOT_DETECTION_BLOCK'
        ).length,
        adminBypasses: eventsArray.filter(event =>
          event.event_type?.includes('bypass') ||
          event.event_type === 'BOT_DETECTION_BYPASSED'
        ).length,
        securityViolations: eventsArray.filter(event =>
          event.event_type?.includes('violation') ||
          event.event_type?.includes('threat') ||
          event.event_type?.includes('VIOLATION')
        ).length
      };

      setStats(stats);
    } catch (err) {
      setError(err.message);
      console.error('Failed to load security data:', err);
    } finally {
      setLoading(false);
    }
  };
  const clearLogs = async () => {
    try {
      const response = await fetch('/.netlify/functions/security?action=clear-events', {
        method: 'POST',
        headers: getAdminHeaders(),
      });

      if (!response.ok) {
        throw new Error('Failed to clear security events');
      }

      // Refresh the data after clearing
      await loadSecurityData();
    } catch (err) {
      setError(err.message);
      console.error('Failed to clear security events:', err);
    }
  };
  const exportLogs = () => {
    const dataStr = JSON.stringify(securityLogs, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `security-logs-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };
  const getEventIcon = (eventType) => {
    if (eventType?.includes('bot') || eventType?.includes('BOT_DETECTION')) return '🤖';
    if (eventType?.includes('admin') || eventType?.includes('bypass') || eventType?.includes('BYPASS')) return '👑';
    if (eventType?.includes('violation') || eventType?.includes('threat') || eventType?.includes('VIOLATION')) return '🚨';
    if (eventType?.includes('login') || eventType?.includes('LOGIN')) return '🔐';
    if (eventType?.includes('key') || eventType?.includes('KEY')) return '🔑';
    if (eventType?.includes('ip_ban')) return '🚫';
    if (eventType?.includes('ml_')) return '🧠';
    if (eventType?.includes('SYSTEM')) return '⚙️';
    return '📊';
  };
  const getEventColor = (eventType) => {
    if (eventType?.includes('bot_detected') || eventType?.includes('BOT_DETECTION_BLOCK') || eventType?.includes('VIOLATION')) return 'text-destructive';
    if (eventType?.includes('bypass') || eventType?.includes('BYPASS') || eventType?.includes('admin') || eventType?.includes('ADMIN')) return 'text-primary';
    if (eventType?.includes('login') || eventType?.includes('LOGIN')) return 'text-success';
    if (eventType?.includes('key') || eventType?.includes('KEY')) return 'text-info';
    if (eventType?.includes('ip_ban')) return 'text-warning';
    if (eventType?.includes('ml_')) return 'text-accent-foreground';
    if (eventType?.includes('SYSTEM')) return 'text-success';
    return 'text-warning';
  };
  if (loading && securityLogs.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FiRefreshCw className="w-8 h-8 animate-spin text-blue-500 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Loading security data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Card className="p-4 bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800">
          <div className="flex items-center">
            <FiAlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2" />
            <p className="text-red-800 dark:text-red-200">{error}</p>
          </div>
        </Card>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Security Dashboard</h2>
          <p className="text-gray-600 dark:text-gray-400">Real-time security monitoring from database</p>
        </div>
        <div className="flex gap-3">
          <Button
            onClick={loadSecurityData}
            variant="outline"
            size="sm"
            disabled={loading}
            className="flex items-center gap-2"
          >
            <FiRefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            onClick={exportLogs}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <FiDownload className="h-4 w-4" />
            Export
          </Button>
          <Button
            onClick={clearLogs}
            variant="outline"
            size="sm"
            className="flex items-center gap-2 text-red-400 border-red-400/30 hover:bg-red-400/10"
          >
            <FiTrash2 className="h-4 w-4" />
            Clear
          </Button>
        </div>
      </div>
      {}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6 bg-gradient-to-br from-blue-500/10 to-purple-500/10 border-blue-500/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Total Events</p>
              <p className="text-2xl font-bold text-white">{stats.totalEvents}</p>
            </div>
            <FiActivity className="h-8 w-8 text-blue-400" />
          </div>
        </Card>
        <Card className="p-6 bg-gradient-to-br from-red-500/10 to-orange-500/10 border-red-500/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Bot Detections</p>
              <p className="text-2xl font-bold text-white">{stats.botDetections}</p>
            </div>
            <FiAlertTriangle className="h-8 w-8 text-red-400" />
          </div>
        </Card>
        <Card className="p-6 bg-gradient-to-br from-green-500/10 to-emerald-500/10 border-green-500/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Admin Bypasses</p>
              <p className="text-2xl font-bold text-white">{stats.adminBypasses}</p>
            </div>
            <FiUsers className="h-8 w-8 text-green-400" />
          </div>
        </Card>
        <Card className="p-6 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 border-yellow-500/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/70 text-sm">Violations</p>
              <p className="text-2xl font-bold text-white">{stats.securityViolations}</p>
            </div>
            <FiShield className="h-8 w-8 text-yellow-400" />
          </div>
        </Card>
      </div>

      {}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <FiEye className="h-5 w-5 text-green-400" />
          <h3 className="text-xl font-semibold text-white">Recent Security Events</h3>
          <span className="text-white/50 text-sm">({securityLogs.length} events)</span>
        </div>
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {securityLogs.length === 0 ? (
            <p className="text-white/50 text-center py-8">No security events recorded</p>
          ) : (
            securityLogs.slice().reverse().map((event, index) => (
              <motion.div
                key={event.id || index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-start gap-3 p-3 bg-white/5 rounded-lg border border-white/10"
              >
                <span className="text-lg">{getEventIcon(event.event_type)}</span>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className={`font-medium ${getEventColor(event.event_type)}`}>
                      {(event.event_type || 'Unknown Event').replace(/_/g, ' ').toUpperCase()}
                    </span>
                    <span className="text-white/50 text-xs">
                      {new Date(event.created_at || event.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  <p className="text-white/70 text-sm">
                    {(event.ip_hash || event.ip_address) && `IP: ${
                      event.ip_hash
                        ? `${generateShortId(event.ip_hash)} (${formatHashForDisplay(event.ip_hash)})`
                        : event.ip_address
                    } | `}
                    {event.user_agent && `Agent: ${event.user_agent.substring(0, 50)}... | `}
                    {event.details && `Details: ${JSON.stringify(event.details).substring(0, 100)}...`}
                  </p>
                  {event.description && (
                    <p className="text-white/60 text-xs mt-1">
                      {event.description}
                    </p>
                  )}
                </div>
              </motion.div>
            ))
          )}
        </div>
      </Card>
    </div>
  );
};
export default SecurityDashboard;
