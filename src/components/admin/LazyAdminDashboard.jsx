import React, { Suspense } from 'react';
import { motion } from 'framer-motion';
import { FiLoader } from 'react-icons/fi';
import { CardSkeleton } from '../ui/LoadingSkeleton';
// Lazy load the AdminDashboard component
const AdminDashboard = React.lazy(() => import('./AdminDashboard'));
// Loading component for the admin dashboard
const AdminDashboardLoading = () => (
  <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20 relative overflow-hidden">
    {/* Background effects */}
    <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
    <motion.div
      className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl"
      animate={{
        y: [0, -20, 0],
        x: [0, 10, 0],
      }}
      transition={{
        duration: 6,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
    <div className="container mx-auto px-4 py-8 relative z-10">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-4">Loading Admin Dashboard</h1>
          <p className="text-foreground/80">Please wait while we load the admin interface...</p>
        </div>
        {/* Loading skeleton for admin dashboard */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar skeleton */}
          <div className="lg:col-span-1">
            <CardSkeleton className="h-96" showImage={true} showBadge={false} />
          </div>
          {/* Main content skeleton */}
          <div className="lg:col-span-3 space-y-6">
            {/* Stats cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Array.from({ length: 3 }).map((_, index) => (
                <CardSkeleton 
                  key={index}
                  showImage={false}
                  showBadge={true}
                  className="h-32"
                />
              ))}
            </div>
            {/* Main dashboard content */}
            <CardSkeleton className="h-64" showImage={false} showBadge={false} />
            <CardSkeleton className="h-48" showImage={false} showBadge={false} />
          </div>
        </div>
      </motion.div>
    </div>
  </div>
);
// Error boundary for admin dashboard
class AdminDashboardErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }
  componentDidCatch(error, errorInfo) {
  }
  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20">
          <motion.div
            className="p-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl text-center max-w-md"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="text-red-500 mb-4">
              <FiLoader className="h-12 w-12 mx-auto" />
            </div>
            <h2 className="text-xl font-bold text-foreground mb-4">
              Failed to Load Admin Dashboard
            </h2>
            <p className="text-foreground/80 mb-6">
              There was an error loading the admin dashboard. Please try refreshing the page.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              Refresh Page
            </button>
          </motion.div>
        </div>
      );
    }
    return this.props.children;
  }
}
// Main lazy admin dashboard component
const LazyAdminDashboard = (props) => {
  return (
    <AdminDashboardErrorBoundary>
      <Suspense fallback={<AdminDashboardLoading />}>
        <AdminDashboard {...props} />
      </Suspense>
    </AdminDashboardErrorBoundary>
  );
};
export default LazyAdminDashboard;
