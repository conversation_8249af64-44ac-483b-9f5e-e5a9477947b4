import * as React from 'react';
import { cn, a11y } from '../../lib/utils';
const Input = React.forwardRef(({
  className,
  type = 'text',
  icon: Icon,
  ariaLabel,
  ariaDescribedBy,
  ariaInvalid,
  error,
  label,
  helperText,
  required = false,
  size = 'md',
  ...props
}, ref) => {
  const inputId = React.useId();
  const errorId = `${inputId}-error`;
  const helperId = `${inputId}-helper`;
  // Size variants for responsive design
  const sizeVariants = {
    sm: 'h-9 px-3 py-2 text-sm',
    md: 'h-10 px-3 py-2 text-sm sm:h-11 sm:px-4 sm:text-base',
    lg: 'h-11 px-4 py-3 text-base sm:h-12 sm:px-5 sm:text-lg'
  };
  // Icon size variants
  const iconSizeVariants = {
    sm: 'h-4 w-4',
    md: 'h-4 w-4 sm:h-5 sm:w-5',
    lg: 'h-5 w-5 sm:h-6 sm:w-6'
  };
  // Icon positioning
  const iconPadding = {
    sm: Icon ? 'pl-9' : 'pl-3',
    md: Icon ? 'pl-10 sm:pl-12' : 'pl-3 sm:pl-4',
    lg: Icon ? 'pl-11 sm:pl-13' : 'pl-4 sm:pl-5'
  };
  // Build aria-describedby
  const describedBy = [
    ariaDescribedBy,
    error ? errorId : null,
    helperText ? helperId : null
  ].filter(Boolean).join(' ') || undefined;
  return (
    <div className="w-full">
      {label && (
        <label
          htmlFor={inputId}
          className="form-label"
        >
          {label}
          {required && (
            <span className="text-destructive ml-1" aria-label="required">*</span>
          )}
        </label>
      )}
      <div className="relative">
        {Icon && (
          <div className="absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none">
            <Icon
              className={cn(
                iconSizeVariants[size],
                error ? 'text-destructive' : 'text-muted-foreground'
              )}
              aria-hidden="true"
            />
          </div>
        )}
        <input
          id={inputId}
          type={type}
          className={cn(
            // Base styles
            'flex w-full rounded-md border bg-background ring-offset-background transition-colors',
            'file:border-0 file:bg-transparent file:text-sm file:font-medium',
            'placeholder:text-muted-foreground',
            'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
            'disabled:cursor-not-allowed disabled:opacity-50',
            // Size variants
            sizeVariants[size],
            // Icon padding
            iconPadding[size],
            // Error states
            error
              ? 'border-destructive focus:ring-destructive focus:border-destructive'
              : 'border-input focus:border-ring',
            // Touch target compliance
            'min-h-[44px]',
            className
          )}
          ref={ref}
          aria-label={ariaLabel}
          aria-describedby={describedBy}
          aria-invalid={ariaInvalid || !!error}
          aria-required={required}
          {...props}
        />
      </div>
      {/* Helper text */}
      {helperText && !error && (
        <p id={helperId} className="form-help">
          {helperText}
        </p>
      )}
      {/* Error message */}
      {error && (
        <p id={errorId} className="form-error" role="alert">
          {error}
        </p>
      )}
    </div>
  );
});
Input.displayName = 'Input';
export { Input };
