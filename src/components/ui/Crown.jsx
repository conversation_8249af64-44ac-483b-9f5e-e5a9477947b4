import { motion } from 'framer-motion';
import LazyImage from './LazyImage';
const Crown = ({ className = "", size = "w-4 h-4" }) => {
  return (
    <motion.div
      className={`inline-flex items-center justify-center ${className}`}
      whileHover={{ 
        scale: 1.2,
        rotate: [0, -5, 5, 0],
        transition: { duration: 0.6, ease: "easeInOut" }
      }}
      initial={{ scale: 1 }}
      animate={{ 
        y: [0, -1, 0],
        transition: { 
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }
      }}
    >
      <div className="relative">
        {/* Glow effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full blur-sm opacity-30 animate-pulse"></div>
        {/* Crown SVG */}
        <LazyImage
          src="/crown-icon.svg"
          alt="Crown"
          className={`relative ${size} drop-shadow-lg`}
          style={{
            filter: 'drop-shadow(0 0 4px rgba(251, 191, 36, 0.5))'
          }}
          placeholder={
            <div className={`${size} bg-gradient-to-r from-yellow-400/20 to-orange-500/20 rounded-full flex items-center justify-center`}>
              <div className="text-yellow-400 text-xs">👑</div>
            </div>
          }
        />
        {/* Sparkle effects */}
        <motion.div
          className="absolute -top-1 -right-1 w-1 h-1 bg-yellow-300 rounded-full"
          animate={{
            opacity: [0, 1, 0],
            scale: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            delay: 0,
          }}
        />
        <motion.div
          className="absolute -bottom-1 -left-1 w-1 h-1 bg-yellow-300 rounded-full"
          animate={{
            opacity: [0, 1, 0],
            scale: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            delay: 0.5,
          }}
        />
        <motion.div
          className="absolute top-0 left-1/2 w-0.5 h-0.5 bg-yellow-200 rounded-full"
          animate={{
            opacity: [0, 1, 0],
            scale: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            delay: 1,
          }}
        />
      </div>
    </motion.div>
  );
};
export default Crown;
