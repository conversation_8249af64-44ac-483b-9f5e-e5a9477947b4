import React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cn, keyboard } from '../../lib/utils';
const Button = ({
  children,
  variant = 'primary',
  size = 'md',
  className = '',
  icon: Icon,
  iconPosition = 'left',
  isLoading = false,
  asChild = false,
  ariaLabel,
  ariaDescribedBy,
  ariaExpanded,
  ariaPressed,
  disabled = false,
  type = 'button',
  ...props
}) => {
  const Comp = asChild ? Slot : 'button';
  const buttonId = React.useId();
  // Base button styles with accessibility improvements
  const baseStyles = cn(
    'inline-flex items-center justify-center font-medium rounded-lg transition-all',
    'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background',
    'disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed',
    'touch-target', // Ensures minimum 44x44px touch target
    'select-none', // Prevents text selection
    'relative' // For loading state positioning
  );
  // Simplified variant styles
  const variants = {
    primary: cn(
      'bg-primary text-primary-foreground',
      'hover:bg-primary-hover',
      'focus:ring-primary'
    ),
    secondary: cn(
      'bg-secondary text-secondary-foreground',
      'hover:bg-secondary/80',
      'focus:ring-secondary'
    ),
    outline: cn(
      'border border-primary text-primary bg-transparent',
      'hover:bg-primary hover:text-primary-foreground',
      'focus:ring-primary'
    ),
    ghost: cn(
      'text-foreground bg-transparent',
      'hover:bg-accent hover:text-accent-foreground',
      'focus:ring-accent'
    ),
    link: cn(
      'text-primary bg-transparent p-0 h-auto',
      'underline-offset-4 hover:underline',
      'focus:ring-primary'
    ),
    destructive: cn(
      'bg-destructive text-destructive-foreground',
      'hover:bg-destructive/90',
      'focus:ring-destructive'
    )
  };
  // Simplified size styles
  const sizes = {
    xs: 'h-8 px-2 text-xs min-w-[32px]',
    sm: 'h-9 px-3 text-sm min-w-[36px]',
    md: 'h-10 px-4 text-base min-w-[44px]',
    lg: 'h-11 px-5 text-base min-w-[48px]',
    xl: 'h-12 px-6 text-lg min-w-[52px]',
    icon: 'h-10 w-10 p-0 min-w-[44px]',
  };
  // Simplified icon sizes
  const iconSizes = {
    xs: 'h-3 w-3',
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6',
    xl: 'h-7 w-7',
  };
  // Determine if this is an icon-only button
  const hasChildren = React.Children.count(children) > 0;
  const isIconOnly = !hasChildren && Icon;
  const sizeClass = isIconOnly ? 'icon' : size;
  // Get icon size based on button size
  const iconSize = size === 'xs' ? 'xs' : size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : size === 'xl' ? 'xl' : 'md';
  // Enhanced icon styling with responsive spacing
  const getIconClassName = (position) => cn(
    iconSizes[iconSize],
    position === 'left' && hasChildren && 'mr-1.5 sm:mr-2',
    position === 'right' && hasChildren && 'ml-1.5 sm:ml-2',
    'flex-shrink-0' // Prevent icon from shrinking
  );
  // Enhanced accessibility attributes
  const accessibilityProps = {
    'aria-label': ariaLabel || (typeof children === 'string' ? String(children) : undefined),
    'aria-describedby': ariaDescribedBy,
    'aria-expanded': ariaExpanded,
    'aria-pressed': ariaPressed,
    'aria-disabled': disabled || isLoading,
    'role': asChild ? undefined : 'button',
    'tabIndex': disabled ? -1 : 0,
    'type': asChild ? undefined : type,
    'disabled': disabled || isLoading
  };
  // Enhanced keyboard event handling
  const handleKeyDown = (event) => {
    // Only handle keyboard events if not using asChild (which should handle its own)
    if (!asChild && (event.key === keyboard.keys.ENTER || event.key === keyboard.keys.SPACE)) {
      event.preventDefault();
      if (props.onClick && !disabled && !isLoading) {
        props.onClick(event);
      }
    }
    // Call any additional onKeyDown handler
    if (props.onKeyDown) {
      props.onKeyDown(event);
    }
  };
  // Loading spinner component
  const LoadingSpinner = () => (
    <svg
      className={cn('animate-spin', iconSizes[iconSize])}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );
  // Render the button content
  const renderContent = () => {
    if (isLoading) {
      return (
        <>
          <LoadingSpinner />
          {hasChildren && (
            <span className="ml-2 opacity-70">
              {typeof children === 'string' ? 'Loading...' : children}
            </span>
          )}
        </>
      );
    }
    return (
      <>
        {Icon && iconPosition === 'left' && (
          <span className={getIconClassName('left')} aria-hidden="true">
            <Icon />
          </span>
        )}
        {children}
        {Icon && iconPosition === 'right' && (
          <span className={getIconClassName('right')} aria-hidden="true">
            <Icon />
          </span>
        )}
      </>
    );
  };
  // Render the button
  if (asChild) {
    return (
      <Comp
        className={cn(baseStyles, variants[variant], sizes[sizeClass], className)}
        onKeyDown={handleKeyDown}
        {...accessibilityProps}
        {...props}
      >
        {renderContent()}
      </Comp>
    );
  }
  return (
    <Comp
      className={cn(baseStyles, variants[variant], sizes[sizeClass], className)}
      onKeyDown={handleKeyDown}
      {...accessibilityProps}
      {...props}
    >
      {renderContent()}
    </Comp>
  );
};
export { Button };
