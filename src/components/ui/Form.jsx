import React from 'react';
import { cn, a11y } from '../../lib/utils';
// Form Root Component
const Form = ({ 
  children, 
  className, 
  onSubmit,
  noValidate = true,
  ...props 
}) => {
  const handleSubmit = (event) => {
    if (onSubmit) {
      event.preventDefault();
      onSubmit(event);
    }
  };
  return (
    <form
      className={cn('space-y-6', className)}
      onSubmit={handleSubmit}
      noValidate={noValidate}
      {...props}
    >
      {children}
    </form>
  );
};
// Form Field Component
const FormField = ({ 
  children, 
  className,
  ...props 
}) => {
  return (
    <div className={cn('space-y-2', className)} {...props}>
      {children}
    </div>
  );
};
// Form Item Component (wrapper for input + label + error)
const FormItem = ({ 
  children, 
  className,
  ...props 
}) => {
  return (
    <div className={cn('space-y-2', className)} {...props}>
      {children}
    </div>
  );
};
// Form Label Component
const FormLabel = ({ 
  children, 
  className,
  htmlFor,
  required = false,
  ...props 
}) => {
  return (
    <label
      htmlFor={htmlFor}
      className={cn(
        'form-label',
        'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
        className
      )}
      {...props}
    >
      {children}
      {required && (
        <span
          className="text-destructive ml-1"
          aria-label="required field"
          title="This field is required"
        >
          *
        </span>
      )}
    </label>
  );
};
// Form Control Component (wrapper for input elements)
const FormControl = ({ 
  children, 
  className,
  ...props 
}) => {
  return (
    <div className={cn('relative', className)} {...props}>
      {children}
    </div>
  );
};
// Form Description Component
const FormDescription = ({ 
  children, 
  className,
  id,
  ...props 
}) => {
  return (
    <p
      id={id}
      className={cn('form-help', 'text-sm text-muted-foreground', className)}
      {...props}
    >
      {children}
    </p>
  );
};
// Form Message Component (for errors and success messages)
const FormMessage = ({ 
  children, 
  className,
  id,
  type = 'error',
  ...props 
}) => {
  if (!children) return null;
  const typeStyles = {
    error: 'form-error text-destructive',
    success: 'text-primary',
    warning: 'text-muted-foreground',
    info: 'text-primary'
  };
  return (
    <p
      id={id}
      className={cn(
        'text-sm font-medium',
        typeStyles[type],
        className
      )}
      role={type === 'error' ? 'alert' : 'status'}
      aria-live={type === 'error' ? 'assertive' : 'polite'}
      {...props}
    >
      {children}
    </p>
  );
};
// Form Group Component (for grouping related fields)
const FormGroup = ({ 
  children, 
  className,
  legend,
  description,
  ...props 
}) => {
  return (
    <fieldset className={cn('space-y-4', className)} {...props}>
      {legend && (
        <legend className="text-base font-medium leading-none text-foreground">
          {legend}
        </legend>
      )}
      {description && (
        <p className="text-sm text-muted-foreground">
          {description}
        </p>
      )}
      <div className="space-y-4">
        {children}
      </div>
    </fieldset>
  );
};
// Form Actions Component (for submit/cancel buttons)
const FormActions = ({ 
  children, 
  className,
  align = 'right',
  ...props 
}) => {
  const alignmentClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
    between: 'justify-between'
  };
  return (
    <div 
      className={cn(
        'flex gap-3 pt-4',
        alignmentClasses[align],
        className
      )} 
      {...props}
    >
      {children}
    </div>
  );
};
// Enhanced Input with Form integration
const FormInput = React.forwardRef(({ 
  className,
  type = 'text',
  error,
  description,
  label,
  required = false,
  ...props 
}, ref) => {
  const inputId = React.useId();
  const errorId = error ? `${inputId}-error` : undefined;
  const descriptionId = description ? `${inputId}-description` : undefined;
  const describedBy = [
    props['aria-describedby'],
    errorId,
    descriptionId
  ].filter(Boolean).join(' ') || undefined;
  return (
    <FormItem>
      {label && (
        <FormLabel htmlFor={inputId} required={required}>
          {label}
        </FormLabel>
      )}
      <FormControl>
        <input
          id={inputId}
          type={type}
          className={cn(
            'form-input',
            error && 'border-destructive focus:ring-destructive',
            className
          )}
          ref={ref}
          aria-describedby={describedBy}
          aria-invalid={!!error}
          aria-required={required}
          {...props}
        />
      </FormControl>
      {description && (
        <FormDescription id={descriptionId}>
          {description}
        </FormDescription>
      )}
      {error && (
        <FormMessage id={errorId} type="error">
          {error}
        </FormMessage>
      )}
    </FormItem>
  );
});
FormInput.displayName = 'FormInput';
export {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
  FormGroup,
  FormActions,
  FormInput
};
