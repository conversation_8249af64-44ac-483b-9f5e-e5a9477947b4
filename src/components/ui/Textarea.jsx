import React from 'react';
import { cn, a11y } from '../../lib/utils';
const Textarea = React.forwardRef(({
  className,
  error,
  label,
  helperText,
  required = false,
  size = 'md',
  resize = 'vertical',
  maxLength,
  showCharCount = false,
  ariaLabel,
  ariaDescribedBy,
  ...props
}, ref) => {
  const textareaId = React.useId();
  const errorId = error ? `${textareaId}-error` : undefined;
  const helperId = helperText ? `${textareaId}-helper` : undefined;
  const charCountId = showCharCount ? `${textareaId}-char-count` : undefined;
  const [charCount, setCharCount] = React.useState(props.value?.length || 0);
  // Size variants
  const sizeVariants = {
    sm: 'min-h-[60px] px-3 py-2 text-sm',
    md: 'min-h-[80px] px-3 py-2 text-sm sm:px-4 sm:py-3 sm:text-base',
    lg: 'min-h-[120px] px-4 py-3 text-base sm:px-5 sm:py-4 sm:text-lg'
  };
  // Resize options
  const resizeClasses = {
    none: 'resize-none',
    vertical: 'resize-y',
    horizontal: 'resize-x',
    both: 'resize'
  };
  // Build aria-describedby
  const describedBy = [
    ariaDescribedBy,
    errorId,
    helperId,
    charCountId
  ].filter(Boolean).join(' ') || undefined;
  // Handle character count
  const handleChange = (event) => {
    setCharCount(event.target.value.length);
    if (props.onChange) {
      props.onChange(event);
    }
  };
  return (
    <div className="w-full">
      {label && (
        <label
          htmlFor={textareaId}
          className="form-label"
        >
          {label}
          {required && (
            <span className="text-red-500 ml-1" aria-label="required">*</span>
          )}
        </label>
      )}
      <div className="relative">
        <textarea
          id={textareaId}
          className={cn(
            // Base styles
            'flex w-full rounded-md border bg-background ring-offset-background transition-colors',
            'placeholder:text-muted-foreground',
            'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
            'disabled:cursor-not-allowed disabled:opacity-50',
            // Size variants
            sizeVariants[size],
            // Resize behavior
            resizeClasses[resize],
            // Error states
            error
              ? 'border-destructive focus:ring-destructive focus:border-destructive'
              : 'border-input focus:border-ring',
            className
          )}
          ref={ref}
          aria-label={ariaLabel}
          aria-describedby={describedBy}
          aria-invalid={!!error}
          aria-required={required}
          maxLength={maxLength}
          onChange={handleChange}
          {...props}
        />
        {/* Character count */}
        {showCharCount && (
          <div
            id={charCountId}
            className="absolute bottom-2 right-2 text-xs text-muted-foreground bg-background/80 px-2 py-1 rounded"
            aria-live="polite"
          >
            {charCount}{maxLength && `/${maxLength}`}
          </div>
        )}
      </div>
      {/* Helper text */}
      {helperText && !error && (
        <p id={helperId} className="form-help">
          {helperText}
        </p>
      )}
      {/* Error message */}
      {error && (
        <p id={errorId} className="form-error" role="alert">
          {error}
        </p>
      )}
    </div>
  );
});
Textarea.displayName = 'Textarea';
export { Textarea };
