import React from 'react';
import { cn } from '../../lib/utils';
const CardComponent = ({
  children,
  className = '',
  variant = 'default',
  ...props
}) => {
  const baseStyles = 'rounded-lg border border-border bg-card text-card-foreground shadow-sm transition-colors';
  const variants = {
    default: 'bg-card',
    primary: 'bg-primary/5 border-primary/20',
    secondary: 'bg-secondary/5 border-secondary/20',
  };
  return (
    <div
      className={cn(baseStyles, variants[variant], className)}
      {...props}
    >
      {children}
    </div>
  );
};
const CardHeader = ({ className = '', ...props }) => (
  <div
    className={cn('flex flex-col space-y-1.5 p-4', className)}
    {...props}
  />
);
const CardTitle = ({ className = '', ...props }) => (
  <h3
    className={cn('text-lg font-semibold leading-none tracking-tight text-card-foreground', className)}
    {...props}
  />
);
const CardDescription = ({ className = '', ...props }) => (
  <p
    className={cn('text-sm text-muted-foreground', className)}
    {...props}
  />
);
const CardContent = ({ className = '', ...props }) => (
  <div className={cn('p-4 pt-0', className)} {...props} />
);
const CardFooter = ({ className = '', ...props }) => (
  <div
    className={cn('flex items-center p-4 pt-0', className)}
    {...props}
  />
);
export const Card = Object.assign(CardComponent, {
  Header: CardHeader,
  Title: CardTitle,
  Description: CardDescription,
  Content: CardContent,
  Footer: CardFooter
});
