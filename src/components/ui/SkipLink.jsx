import React from 'react';
import { cn } from '../../lib/utils';
const SkipLink = ({ 
  href = '#main-content',
  children = 'Skip to main content',
  className,
  ...props 
}) => {
  return (
    <a
      href={href}
      className={cn(
        // Position off-screen by default
        'absolute -top-40 left-6 z-[9999]',
        // Show when focused
        'focus:top-6',
        // Styling
        'bg-primary text-primary-foreground px-4 py-2 rounded-md',
        'font-medium text-sm',
        'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
        'transition-all duration-200',
        // Ensure it's above everything
        'shadow-lg',
        className
      )}
      {...props}
    >
      {children}
    </a>
  );
};
// Skip Links Container for multiple skip links
const SkipLinks = ({ children, className }) => {
  return (
    <div className={cn('sr-only focus-within:not-sr-only', className)}>
      {children}
    </div>
  );
};
export { SkipLink, SkipLinks };
