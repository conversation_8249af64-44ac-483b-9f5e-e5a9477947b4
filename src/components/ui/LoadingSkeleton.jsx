import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';
// Base skeleton component
const Skeleton = ({ className = '', ...props }) => (
  <motion.div
    className={cn(
      "relative overflow-hidden rounded-lg",
      "bg-gradient-to-r from-slate-800/50 via-slate-700/50 to-slate-800/50",
      "backdrop-blur-sm border border-white/10",
      className
    )}
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ duration: 0.3 }}
    {...props}
  >
    <motion.div
      className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"
      animate={{ x: ['-100%', '100%'] }}
      transition={{
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
  </motion.div>
);
// Card skeleton for script cards, dashboard sections, etc.
const CardSkeleton = ({ className = '', showImage = true, showBadge = true }) => (
  <motion.div
    className={cn(
      "p-6 rounded-2xl backdrop-blur-xl border border-white/10",
      "bg-gradient-to-br from-white/5 to-white/2",
      className
    )}
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
  >
    {/* Header with image/icon */}
    {showImage && (
      <div className="flex items-center space-x-4 mb-4">
        <Skeleton className="w-12 h-12 rounded-xl" />
        <div className="flex-1 space-y-2">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-3 w-1/2" />
        </div>
        {showBadge && <Skeleton className="w-16 h-6 rounded-full" />}
      </div>
    )}
    {/* Content lines */}
    <div className="space-y-3">
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-5/6" />
      <Skeleton className="h-4 w-4/6" />
    </div>
    {/* Footer actions */}
    <div className="flex items-center justify-between mt-6">
      <Skeleton className="h-8 w-20 rounded-lg" />
      <Skeleton className="h-8 w-24 rounded-lg" />
    </div>
  </motion.div>
);
// List item skeleton
const ListItemSkeleton = ({ className = '' }) => (
  <motion.div
    className={cn(
      "flex items-center space-x-4 p-4 rounded-xl",
      "bg-gradient-to-r from-white/5 to-white/2 backdrop-blur-sm border border-white/10",
      className
    )}
    initial={{ opacity: 0, x: -20 }}
    animate={{ opacity: 1, x: 0 }}
    transition={{ duration: 0.4 }}
  >
    <Skeleton className="w-10 h-10 rounded-lg" />
    <div className="flex-1 space-y-2">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-3 w-1/2" />
    </div>
    <Skeleton className="w-16 h-6 rounded-full" />
  </motion.div>
);
// Form skeleton
const FormSkeleton = ({ className = '', fields = 3 }) => (
  <motion.div
    className={cn(
      "space-y-6 p-6 rounded-2xl backdrop-blur-xl border border-white/10",
      "bg-gradient-to-br from-white/5 to-white/2",
      className
    )}
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
  >
    {/* Form title */}
    <div className="space-y-2">
      <Skeleton className="h-6 w-1/3" />
      <Skeleton className="h-4 w-2/3" />
    </div>
    {/* Form fields */}
    {Array.from({ length: fields }).map((_, index) => (
      <div key={index} className="space-y-2">
        <Skeleton className="h-4 w-1/4" />
        <Skeleton className="h-12 w-full rounded-lg" />
      </div>
    ))}
    {/* Submit button */}
    <Skeleton className="h-12 w-32 rounded-lg" />
  </motion.div>
);
// Dashboard stats skeleton
const StatsSkeleton = ({ className = '', count = 4 }) => (
  <motion.div
    className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6", className)}
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ duration: 0.5 }}
  >
    {Array.from({ length: count }).map((_, index) => (
      <motion.div
        key={index}
        className="p-6 rounded-2xl backdrop-blur-xl border border-white/10 bg-gradient-to-br from-white/5 to-white/2"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: index * 0.1 }}
      >
        <div className="flex items-center justify-between mb-4">
          <Skeleton className="w-8 h-8 rounded-lg" />
          <Skeleton className="w-12 h-6 rounded-full" />
        </div>
        <Skeleton className="h-8 w-20 mb-2" />
        <Skeleton className="h-4 w-16" />
      </motion.div>
    ))}
  </motion.div>
);
// Table skeleton
const TableSkeleton = ({ className = '', rows = 5, columns = 4 }) => (
  <motion.div
    className={cn(
      "rounded-2xl backdrop-blur-xl border border-white/10 overflow-hidden",
      "bg-gradient-to-br from-white/5 to-white/2",
      className
    )}
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
  >
    {/* Table header */}
    <div className="grid gap-4 p-4 border-b border-white/10" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
      {Array.from({ length: columns }).map((_, index) => (
        <Skeleton key={index} className="h-4 w-full" />
      ))}
    </div>
    {/* Table rows */}
    <div className="divide-y divide-white/10">
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <motion.div
          key={rowIndex}
          className="grid gap-4 p-4"
          style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.4, delay: rowIndex * 0.1 }}
        >
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton key={colIndex} className="h-4 w-full" />
          ))}
        </motion.div>
      ))}
    </div>
  </motion.div>
);
// Export all skeleton components
export {
  Skeleton,
  CardSkeleton,
  ListItemSkeleton,
  FormSkeleton,
  StatsSkeleton,
  TableSkeleton
};
export default Skeleton;
