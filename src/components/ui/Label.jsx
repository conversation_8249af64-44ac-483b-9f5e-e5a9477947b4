import * as React from 'react';
import { cn } from '../../lib/utils';
const Label = React.forwardRef(({
  className,
  children,
  required = false,
  size = 'md',
  variant = 'default',
  ...props
}, ref) => {
  // Size variants
  const sizeVariants = {
    sm: 'text-xs sm:text-sm',
    md: 'text-sm sm:text-base',
    lg: 'text-base sm:text-lg'
  };
  // Variant styles
  const variantStyles = {
    default: 'text-foreground',
    muted: 'text-muted-foreground',
    error: 'text-destructive',
    success: 'text-success'
  };
  return (
    <label
      ref={ref}
      className={cn(
        'font-medium leading-none block mb-2',
        'peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
        sizeVariants[size],
        variantStyles[variant],
        className
      )}
      {...props}
    >
      {children}
      {required && (
        <span
          className="text-destructive ml-1"
          aria-label="required field"
          title="This field is required"
        >
          *
        </span>
      )}
    </label>
  );
});
Label.displayName = 'Label';
export { Label };
