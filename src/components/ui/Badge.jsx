import React from 'react';
import { cva } from 'class-variance-authority';
const badgeVariants = cva(
  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        default:
          'border-transparent bg-primary/10 text-primary hover:bg-primary/20',
        secondary:
          'border-transparent bg-secondary/10 text-secondary-foreground hover:bg-secondary/20',
        destructive:
          'border-transparent bg-destructive/10 text-destructive hover:bg-destructive/20',
        outline: 'text-foreground',
        success:
          'border-transparent bg-success/10 text-success hover:bg-success/20',
        warning:
          'border-transparent bg-warning/10 text-warning hover:bg-warning/20',
        info: 'border-transparent bg-info/10 text-info hover:bg-info/20',
        premium:
          'bg-primary text-primary-foreground border-0 hover:bg-primary-hover',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
);
const Badge = ({
  className,
  variant,
  children,
  icon: Icon,
  iconPosition = 'left',
  ariaLabel,
  ...props
}) => {
  return (
    <div
      className={badgeVariants({ variant, className })}
      aria-label={ariaLabel}
      {...props}>
      {Icon && iconPosition === 'left' && (
        <Icon className="mr-1 h-3 w-3" />
      )}
      {children}
      {Icon && iconPosition === 'right' && (
        <Icon className="ml-1 h-3 w-3" />
      )}
    </div>
  );
};
export { Badge, badgeVariants };
