import React, { useEffect, useRef } from 'react';
import { a11y, keyboard } from '../../lib/utils';
const FocusTrap = ({ 
  children, 
  active = true,
  restoreFocus = true,
  initialFocus,
  className,
  ...props 
}) => {
  const containerRef = useRef(null);
  const previousActiveElement = useRef(null);
  useEffect(() => {
    if (!active) return;
    // Store the currently focused element
    previousActiveElement.current = document.activeElement;
    const container = containerRef.current;
    if (!container) return;
    // Set up focus trap
    const cleanup = a11y.trapFocus(container);
    // Focus initial element or first focusable element
    const focusInitial = () => {
      if (initialFocus) {
        if (typeof initialFocus === 'string') {
          const element = container.querySelector(initialFocus);
          if (element) element.focus();
        } else if (initialFocus.current) {
          initialFocus.current.focus();
        }
      } else {
        // Focus first focusable element
        const focusableElements = container.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        if (focusableElements.length > 0) {
          focusableElements[0].focus();
        }
      }
    };
    // Small delay to ensure DOM is ready
    setTimeout(focusInitial, 0);
    return () => {
      cleanup();
      // Restore focus to previous element
      if (restoreFocus && previousActiveElement.current) {
        previousActiveElement.current.focus();
      }
    };
  }, [active, initialFocus, restoreFocus]);
  return (
    <div
      ref={containerRef}
      className={className}
      {...props}
    >
      {children}
    </div>
  );
};
export { FocusTrap };
