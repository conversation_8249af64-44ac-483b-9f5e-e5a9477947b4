import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FiArrowLeft } from 'react-icons/fi';
export const PageLayout = ({ 
  title, 
  description, 
  children, 
  showBackButton = true,
  className = ''
}) => {
  return (
    <div className={`container mx-auto px-4 py-12 md:py-16 ${className}`}>
      <div className="max-w-4xl mx-auto">
        {showBackButton && (
          <Link 
            to="/" 
            className="inline-flex items-center text-primary hover:underline mb-6 transition-colors"
          >
            <FiArrowLeft className="mr-2" />
            Back to Home
          </Link>
        )}
        <motion.div 
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-4xl sm:text-5xl font-bold mb-4">
            {title}
          </h1>
          {description && (
            <p className="text-lg text-foreground/70 max-w-2xl mx-auto">
              {description}
            </p>
          )}
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="bg-card rounded-xl shadow-sm p-6 md:p-8"
        >
          {children}
        </motion.div>
      </div>
    </div>
  );
};
export const Section = ({ title, children, className = '' }) => (
  <section className={`mb-10 last:mb-0 ${className}`}>
    {title && (
      <h2 className="text-2xl font-bold mb-4 pb-2 border-b border-border">
        {title}
      </h2>
    )}
    <div className="prose dark:prose-invert max-w-none">
      {children}
    </div>
  </section>
);
