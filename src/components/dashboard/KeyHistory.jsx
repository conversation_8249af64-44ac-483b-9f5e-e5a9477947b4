import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Button, 
  Tag, 
  Input, 
  Select, 
  DatePicker, 
  Space, 
  Tooltip, 
  message,
  Modal,
  Typography,
  Row,
  Col,
  Statistic
} from 'antd';
import { 
  KeyOutlined, 
  DownloadOutlined, 
  SearchOutlined, 
  FilterOutlined,
  EyeOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Text, Title } = Typography;
const { confirm } = Modal;
const KeyHistory = () => {
  const [loading, setLoading] = useState(true);
  const [keys, setKeys] = useState([]);
  const [filteredKeys, setFilteredKeys] = useState([]);
  const [stats, setStats] = useState({});
  const [filters, setFilters] = useState({
    search: '',
    game: '',
    status: '',
    dateRange: null
  });
  useEffect(() => {
    loadKeyHistory();
  }, []);
  useEffect(() => {
    applyFilters();
  }, [keys, filters]);
  const loadKeyHistory = async () => {
    try {
      setLoading(true);
      const response = await fetch('/.netlify/functions/user-key-history', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      if (response.ok) {
        const data = await response.json();
        setKeys(data.keys || []);
        setStats(data.stats || {});
      } else {
        message.error('Failed to load key history');
      }
    } catch (error) {
      message.error('Failed to load key history');
    } finally {
      setLoading(false);
    }
  };
  const applyFilters = () => {
    let filtered = [...keys];
    // Search filter
    if (filters.search) {
      filtered = filtered.filter(key => 
        key.game_name.toLowerCase().includes(filters.search.toLowerCase()) ||
        key.script_name.toLowerCase().includes(filters.search.toLowerCase()) ||
        key.key_id.toLowerCase().includes(filters.search.toLowerCase())
      );
    }
    // Game filter
    if (filters.game) {
      filtered = filtered.filter(key => key.game_name === filters.game);
    }
    // Status filter
    if (filters.status) {
      filtered = filtered.filter(key => key.status === filters.status);
    }
    // Date range filter
    if (filters.dateRange && filters.dateRange.length === 2) {
      const [start, end] = filters.dateRange;
      filtered = filtered.filter(key => {
        const keyDate = dayjs(key.generated_at);
        return keyDate.isAfter(start.startOf('day')) && keyDate.isBefore(end.endOf('day'));
      });
    }
    setFilteredKeys(filtered);
  };
  const handleDownloadKey = async (keyId) => {
    try {
      // Direct download without ads
      const response = await fetch(`/.netlify/functions/get-key/${keyId}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      if (response.ok) {
        const keyData = await response.json();
        // Create download link
        const blob = new Blob([keyData.key_code], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `madara-key-${keyId}.txt`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        // Track download attempt
        await fetch('/.netlify/functions/track-analytics', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            event_type: 'key_download_initiated',
            event_data: { key_id: keyId }
          })
        });
      } else {
        message.error('Failed to download key');
      }
    } catch (error) {
      message.error('Failed to initiate download');
    }
  };
  const handleViewKey = async (keyId) => {
    try {
      // Direct view without ads
      window.open(`/key-viewer/${keyId}`, '_blank');
    } catch (error) {
      message.error('Failed to open key viewer');
    }
  };
  const handleRevokeKey = (keyId) => {
    confirm({
      title: 'Revoke Key',
      icon: <ExclamationCircleOutlined />,
      content: 'Are you sure you want to revoke this key? This action cannot be undone.',
      okText: 'Yes, Revoke',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          const response = await fetch('/.netlify/functions/revoke-key', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ key_id: keyId })
          });
          if (response.ok) {
            message.success('Key revoked successfully');
            loadKeyHistory(); // Reload the list
          } else {
            message.error('Failed to revoke key');
          }
        } catch (error) {
          message.error('Failed to revoke key');
        }
      }
    });
  };
  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'green';
      case 'expired': return 'orange';
      case 'revoked': return 'red';
      case 'suspended': return 'volcano';
      default: return 'default';
    }
  };
  const getStatusText = (status) => {
    switch (status) {
      case 'active': return 'Active';
      case 'expired': return 'Expired';
      case 'revoked': return 'Revoked';
      case 'suspended': return 'Suspended';
      default: return status;
    }
  };
  const columns = [
    {
      title: 'Game',
      dataIndex: 'game_name',
      key: 'game_name',
      sorter: (a, b) => a.game_name.localeCompare(b.game_name),
      render: (text) => <Text strong>{text}</Text>
    },
    {
      title: 'Script',
      dataIndex: 'script_name',
      key: 'script_name',
      sorter: (a, b) => a.script_name.localeCompare(b.script_name),
    },
    {
      title: 'Generated',
      dataIndex: 'generated_at',
      key: 'generated_at',
      sorter: (a, b) => new Date(a.generated_at) - new Date(b.generated_at),
      render: (date) => dayjs(date).format('MMM DD, YYYY HH:mm')
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      filters: [
        { text: 'Active', value: 'active' },
        { text: 'Expired', value: 'expired' },
        { text: 'Revoked', value: 'revoked' },
        { text: 'Suspended', value: 'suspended' }
      ],
      onFilter: (value, record) => record.status === value,
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'Usage',
      dataIndex: 'usage_count',
      key: 'usage_count',
      sorter: (a, b) => a.usage_count - b.usage_count,
      render: (count) => `${count || 0} times`
    },
    {
      title: 'Downloads',
      dataIndex: 'download_count',
      key: 'download_count',
      sorter: (a, b) => a.download_count - b.download_count,
      render: (count) => `${count || 0} downloads`
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="View Key">
            <Button 
              type="primary" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => handleViewKey(record.id)}
              disabled={record.status !== 'active'}
            />
          </Tooltip>
          <Tooltip title="Download Key">
            <Button 
              type="default" 
              icon={<DownloadOutlined />} 
              size="small"
              onClick={() => handleDownloadKey(record.key_id)}
              disabled={record.status !== 'active'}
            />
          </Tooltip>
          <Tooltip title="Revoke Key">
            <Button 
              type="danger" 
              icon={<DeleteOutlined />} 
              size="small"
              onClick={() => handleRevokeKey(record.key_id)}
              disabled={record.status !== 'active'}
            />
          </Tooltip>
        </Space>
      )
    }
  ];
  const uniqueGames = [...new Set(keys.map(key => key.game_name))];
  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Keys"
              value={stats.totalKeys || 0}
              prefix={<KeyOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Keys"
              value={stats.activeKeys || 0}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Downloads"
              value={stats.totalDownloads || 0}
              prefix={<DownloadOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Usage"
              value={stats.totalUsage || 0}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>
      {/* Filters */}
      <Card title="Filter Keys" size="small">
        <Row gutter={16}>
          <Col span={8}>
            <Search
              placeholder="Search by game, script, or key ID"
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="Select Game"
              value={filters.game}
              onChange={(value) => setFilters(prev => ({ ...prev, game: value }))}
              allowClear
              style={{ width: '100%' }}
            >
              {uniqueGames.map(game => (
                <Option key={game} value={game}>{game}</Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Status"
              value={filters.status}
              onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="active">Active</Option>
              <Option value="expired">Expired</Option>
              <Option value="revoked">Revoked</Option>
              <Option value="suspended">Suspended</Option>
            </Select>
          </Col>
          <Col span={8}>
            <RangePicker
              value={filters.dateRange}
              onChange={(dates) => setFilters(prev => ({ ...prev, dateRange: dates }))}
              style={{ width: '100%' }}
            />
          </Col>
        </Row>
      </Card>
      {/* Keys Table */}
      <Card title={`Key History (${filteredKeys.length} keys)`}>
        <Table
          columns={columns}
          dataSource={filteredKeys}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} of ${total} keys`
          }}
          scroll={{ x: 1000 }}
        />
      </Card>
    </div>
  );
};
export default KeyHistory;
