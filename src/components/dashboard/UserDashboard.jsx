import { useState, useEffect } from 'react';
import { FiKey, FiClock, FiCopy, FiRefreshCw, FiPlus, FiAlertTriangle, FiCheck } from 'react-icons/fi';
const UserDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [keys, setKeys] = useState([]);
  const [error, setError] = useState(null);
  const [copiedKey, setCopiedKey] = useState(null);
  useEffect(() => {
    loadUserKeys();
  }, []);
  const loadUserKeys = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch('/.netlify/functions/user-key-history', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      if (response.ok) {
        const data = await response.json();
        // Only show active keys
        const activeKeys = (data.keys || []).filter(key => key.status === 'active');
        setKeys(activeKeys);
      } else {
        setError('Failed to load your keys');
      }
    } catch (error) {
      setError('Failed to load your keys');
    } finally {
      setLoading(false);
    }
  };
  const handleCopyKey = async (keyId) => {
    try {
      // Direct key access without ads
      window.open(`/key-viewer/${keyId}`, '_blank');
    } catch (error) {
      alert('Failed to access key');
    }
  };
  const handleQuickCopy = async (maskedKey) => {
    try {
      await navigator.clipboard.writeText(maskedKey);
      setCopiedKey(maskedKey);
      setTimeout(() => setCopiedKey(null), 2000);
    } catch (error) {
      alert('Copy failed');
    }
  };
  const handleResetHWID = async (keyId) => {
    if (!confirm('Are you sure you want to reset the HWID for this key?\n\n• This will unlink the key from your current device\n• This will also reset your Roblox HWID\n• You can only reset HWID once per 24 hours\n• The key will need to be linked to a new device when used')) {
      return;
    }
    try {
      const response = await fetch('/.netlify/functions/reset-hwid', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ key_id: keyId })
      });
      if (response.ok) {
        alert('HWID and Roblox HWID reset successfully');
        loadUserKeys(); // Reload the keys to update the table
      } else {
        const data = await response.json();
        alert(data.error || 'Failed to reset HWID');
      }
    } catch (error) {
      alert('Failed to reset HWID');
    }
  };
  const getTimeRemaining = (expiresAt) => {
    const now = new Date();
    const expires = new Date(expiresAt);
    const diff = expires - now;
    if (diff <= 0) return 'Expired';
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}d ${hours % 24}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };
  const maskKey = (keyCode) => {
    if (!keyCode) return '';
    const parts = keyCode.split('-');
    if (parts.length >= 4) {
      return `${parts[0]}-${parts[1]}-****-****`;
    }
    return keyCode.substring(0, 8) + '****';
  };
  const getHWIDResetStatus = (key) => {
    if (!key.last_hwid_reset) {
      return { canReset: true, timeRemaining: null };
    }
    const lastReset = new Date(key.last_hwid_reset);
    const now = new Date();
    const timeDiff = now - lastReset;
    const hoursRemaining = 24 - (timeDiff / (1000 * 60 * 60));
    if (hoursRemaining <= 0) {
      return { canReset: true, timeRemaining: null };
    }
    const hours = Math.floor(hoursRemaining);
    const minutes = Math.floor((hoursRemaining % 1) * 60);
    return {
      canReset: false,
      timeRemaining: hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`
    };
  };
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <FiKey className="mx-auto text-6xl text-blue-500 mb-4 animate-pulse" />
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-2">Loading Your Keys</h2>
          <p className="text-gray-600 dark:text-gray-400">Please wait while we fetch your license keys...</p>
        </div>
      </div>
    );
  }
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <FiAlertTriangle className="mx-auto text-6xl text-red-500 mb-4" />
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-2">Error Loading Keys</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">{error}</p>
          <button
            onClick={loadUserKeys}
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }
  return (
    <div className="min-h-screen" style={{ background: 'linear-gradient(135deg, #0f0f1a 0%, #1a1a2e 50%, #16213e 100%)' }}>
      {/* Compact Header */}
      <div className="backdrop-blur-xl bg-white/5 border-b border-white/15 shadow-2xl">
        <div className="max-w-6xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 rounded-xl relative overflow-hidden"
                   style={{
                     background: 'linear-gradient(135deg, #7a5cff 0%, #9c88ff 100%)',
                     boxShadow: '0 4px 16px rgba(122, 92, 255, 0.4)'
                   }}>
                <FiKey className="text-xl text-white absolute inset-0 m-auto" />
              </div>
              <div>
                <div className="flex items-center space-x-3">
                  <h1 className="text-2xl font-bold text-white tracking-tight">
                    License Keys
                  </h1>
                  {keys.length > 0 && (
                    <span className="px-2 py-1 text-xs font-medium rounded-full"
                          style={{
                            background: 'rgba(122, 92, 255, 0.2)',
                            color: '#9c88ff',
                            border: '1px solid rgba(122, 92, 255, 0.3)'
                          }}>
                      {keys.length} Active
                    </span>
                  )}
                </div>
                <p className="text-gray-400 text-sm">
                  {keys.length === 0 ? 'No active keys' : `Managing ${keys.length} active key${keys.length > 1 ? 's' : ''}`}
                </p>
              </div>
            </div>
            {/* Generate Key Button */}
            <button
              onClick={() => window.location.href = '/generate-key'}
              className="group relative px-4 py-2 text-sm font-medium text-white rounded-lg overflow-hidden transition-all duration-200 hover:scale-105"
              style={{
                background: 'linear-gradient(135deg, #7a5cff 0%, #9c88ff 100%)',
                boxShadow: '0 4px 16px rgba(122, 92, 255, 0.3)'
              }}
            >
              <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
              <div className="relative flex items-center">
                <FiPlus className="mr-2 text-sm" />
                Generate Key
              </div>
            </button>
          </div>
        </div>
      </div>
      {/* Main Content - Compact Layout */}
      <div className="max-w-5xl mx-auto px-4 py-6">
        {/* Quick Summary for Multiple Keys */}
        {keys.length > 1 && (
          <div className="backdrop-blur-xl rounded-xl p-4 mb-4"
               style={{
                 background: 'rgba(122, 92, 255, 0.1)',
                 border: '1px solid rgba(122, 92, 255, 0.2)',
                 boxShadow: '0 4px 16px rgba(122, 92, 255, 0.2)'
               }}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <div className="text-center">
                  <div className="text-lg font-bold text-white">{keys.length}</div>
                  <div className="text-xs text-gray-400">Total Keys</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-400">
                    {keys.filter(k => {
                      const time = getTimeRemaining(k.expires_at);
                      return time !== 'Expired' && (!time.includes('h') || parseInt(time) >= 6);
                    }).length}
                  </div>
                  <div className="text-xs text-gray-400">Healthy</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-orange-400">
                    {keys.filter(k => {
                      const time = getTimeRemaining(k.expires_at);
                      return time !== 'Expired' && time.includes('h') && parseInt(time) < 6;
                    }).length}
                  </div>
                  <div className="text-xs text-gray-400">Expiring Soon</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-red-400">
                    {keys.filter(k => getTimeRemaining(k.expires_at) === 'Expired').length}
                  </div>
                  <div className="text-xs text-gray-400">Expired</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-400">
                    {keys.filter(k => k.hwid_hash).length}
                  </div>
                  <div className="text-xs text-gray-400">HWID Linked</div>
                </div>
              </div>
              <div className="text-xs text-gray-400">
                {keys.length > 3 && 'Scroll to see all keys'}
              </div>
            </div>
          </div>
        )}
        {/* Keys Display */}
        {keys.length === 0 ? (
          <div className="backdrop-blur-xl rounded-2xl p-8 text-center relative overflow-hidden"
               style={{
                 background: 'rgba(255, 255, 255, 0.08)',
                 border: '1px solid rgba(255, 255, 255, 0.15)',
                 boxShadow: '0 4px 24px rgba(0, 0, 0, 0.4)'
               }}>
            <div className="relative z-10">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-xl mb-4"
                   style={{
                     background: 'linear-gradient(135deg, #7a5cff 0%, #9c88ff 100%)',
                     boxShadow: '0 4px 16px rgba(122, 92, 255, 0.4)'
                   }}>
                <FiKey className="text-2xl text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">No Active Keys</h3>
              <p className="text-gray-400 mb-6 text-sm max-w-sm mx-auto">
                Generate your first key to get started with Project Madara
              </p>
              <button
                onClick={() => window.location.href = '/generate-key'}
                className="px-6 py-2 text-sm font-medium text-white rounded-lg transition-all duration-200 hover:scale-105"
                style={{
                  background: 'linear-gradient(135deg, #7a5cff 0%, #9c88ff 100%)',
                  boxShadow: '0 4px 16px rgba(122, 92, 255, 0.3)'
                }}
              >
                <FiPlus className="inline mr-2" />
                Generate First Key
              </button>
            </div>
          </div>
        ) : (
          <div className={`space-y-3 ${keys.length > 3 ? 'max-h-96 overflow-y-auto pr-2' : ''}`}>
            {keys.map((key, index) => {
              const resetStatus = getHWIDResetStatus(key);
              const timeRemaining = getTimeRemaining(key.expires_at);
              const isExpired = timeRemaining === 'Expired';
              const isExpiringSoon = !isExpired && timeRemaining.includes('h') && parseInt(timeRemaining) < 6;
              const maskedKey = maskKey(key.key_id);
              const isCopied = copiedKey === maskedKey;
              // Calculate time percentage for progress bar
              const totalHours = 24;
              const remainingHours = isExpired ? 0 : (timeRemaining.includes('h') ? parseInt(timeRemaining) : 0);
              const timePercentage = (remainingHours / totalHours) * 100;
              // Subtle alternating background for multiple keys
              const isEven = index % 2 === 0;
              return (
                <div key={key.id} className={`backdrop-blur-xl rounded-xl overflow-hidden relative group ${isExpired ? 'opacity-60' : ''}`}
                     style={{
                       background: isExpired
                         ? 'rgba(239, 68, 68, 0.1)'
                         : (isEven ? 'rgba(255, 255, 255, 0.08)' : 'rgba(255, 255, 255, 0.06)'),
                       border: isExpired
                         ? '1px solid rgba(239, 68, 68, 0.3)'
                         : '1px solid rgba(255, 255, 255, 0.15)',
                       boxShadow: isExpired
                         ? '0 4px 24px rgba(239, 68, 68, 0.2)'
                         : '0 4px 24px rgba(0, 0, 0, 0.4)'
                     }}>
                  <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  {/* Expired overlay */}
                  {isExpired && (
                    <div className="absolute top-2 right-2 px-2 py-1 bg-red-500/20 border border-red-500/30 rounded text-xs font-medium text-red-400">
                      EXPIRED
                    </div>
                  )}
                  {/* Compact Layout */}
                  <div className="p-4 relative z-10">
                    {/* Top Row - Key Code and Copy */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3 flex-1">
                        <div className="backdrop-blur-sm rounded-lg px-3 py-2 font-mono text-lg font-bold text-white select-all relative overflow-hidden"
                             style={{
                               background: 'rgba(122, 92, 255, 0.15)',
                               border: '1px solid rgba(122, 92, 255, 0.25)'
                             }}>
                          {maskedKey}
                        </div>
                        <button
                          onClick={() => handleQuickCopy(maskedKey)}
                          disabled={isExpired}
                          className={`w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-200 ${
                            isExpired
                              ? 'bg-gray-500/20 text-gray-500 cursor-not-allowed opacity-50'
                              : (isCopied
                                ? 'bg-green-500 text-white scale-110'
                                : 'bg-white/10 text-white/70 hover:bg-white/20 hover:scale-110')
                          }`}
                          title={isExpired ? 'Cannot copy expired key' : 'Copy masked key'}
                        >
                          {isCopied ? <FiCheck className="w-4 h-4" /> : <FiCopy className="w-4 h-4" />}
                        </button>
                      </div>
                      {/* Primary Action */}
                      <button
                        onClick={() => handleCopyKey(key.id)}
                        disabled={isExpired}
                        className={`group relative px-4 py-2 rounded-lg overflow-hidden transition-all duration-200 ml-4 ${
                          isExpired ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'
                        }`}
                        style={{
                          background: isExpired
                            ? 'rgba(107, 114, 128, 0.5)'
                            : 'linear-gradient(135deg, #7a5cff 0%, #9c88ff 100%)',
                          boxShadow: isExpired
                            ? 'none'
                            : '0 4px 16px rgba(122, 92, 255, 0.3)'
                        }}
                      >
                        <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                        <div className="relative flex items-center text-white text-sm font-medium">
                          <FiKey className="w-4 h-4 mr-2" />
                          {isExpired ? 'Expired' : 'Get Full Key'}
                        </div>
                      </button>
                    </div>
                    {/* Bottom Row - Status and Actions */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        {/* Time Status */}
                        <div className="flex items-center space-x-2">
                          <span className={`text-sm font-semibold ${
                            isExpired ? 'text-red-400' : (isExpiringSoon ? 'text-orange-400' : 'text-green-400')
                          }`}>
                            <FiClock className="inline w-4 h-4 mr-1" />
                            {timeRemaining}
                          </span>
                          <div className="w-16 h-1.5 rounded-full overflow-hidden"
                               style={{ background: 'rgba(255, 255, 255, 0.1)' }}>
                            <div
                              className="h-full rounded-full transition-all duration-1000"
                              style={{
                                width: `${timePercentage}%`,
                                background: isExpired
                                  ? 'linear-gradient(90deg, #ef4444 0%, #dc2626 100%)'
                                  : (isExpiringSoon
                                    ? 'linear-gradient(90deg, #ff9f43 0%, #ff6b35 100%)'
                                    : 'linear-gradient(90deg, #28c76f 0%, #20bf6b 100%)')
                              }}
                            ></div>
                          </div>
                        </div>
                        {/* HWID Status */}
                        <div className={`px-2 py-1 rounded text-xs font-medium ${
                          key.hwid_hash
                            ? 'bg-blue-500/20 text-blue-300'
                            : 'bg-gray-500/20 text-gray-400'
                        }`}>
                          {key.hwid_hash ? '🔗 Linked' : '🔓 Not Set'}
                        </div>
                      </div>
                      {/* Reset Action */}
                      <button
                        onClick={() => handleResetHWID(key.id)}
                        disabled={isExpired || !resetStatus.canReset}
                        className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 ${
                          isExpired
                            ? 'bg-gray-500/20 text-gray-500 cursor-not-allowed opacity-50'
                            : (resetStatus.canReset
                              ? 'bg-orange-500/20 text-orange-300 hover:bg-orange-500/30 hover:scale-105'
                              : 'bg-gray-500/20 text-gray-500 cursor-not-allowed')
                        }`}
                        title={
                          isExpired
                            ? 'Cannot reset HWID for expired key'
                            : (resetStatus.canReset ? 'Reset HWID & Roblox HWID (once per 24h)' : `Reset available in ${resetStatus.timeRemaining}`)
                        }
                      >
                        <FiRefreshCw className="inline w-3 h-3 mr-1" />
                        Reset HWID
                      </button>
                    </div>
                    {!resetStatus.canReset && (
                      <p className="text-white/40 text-xs mt-2">
                        Reset available in {resetStatus.timeRemaining}
                      </p>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
        {/* How It Works - Compact 2x2 Grid */}
        <div className="mt-8">
          <div className="text-center mb-6">
            <h3 className="text-xl font-bold text-white mb-2">How It Works</h3>
            <p className="text-gray-400 text-sm max-w-lg mx-auto">
              Quick guide to managing your license keys
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
            {[
              {
                icon: '🔑',
                title: 'Generate',
                description: 'Complete Lootlabs verification',
                colorFrom: '#7a5cff',
                colorTo: '#9c88ff'
              },
              {
                icon: '📋',
                title: 'Copy & Access',
                description: 'Quick copy or get full key',
                colorFrom: '#3b82f6',
                colorTo: '#06b6d4'
              },
              {
                icon: '🔄',
                title: 'Reset HWID',
                description: 'Resets both HWID & Roblox HWID',
                colorFrom: '#ff9f43',
                colorTo: '#ff6b35'
              },
              {
                icon: '🎮',
                title: 'Use in Executor',
                description: 'Paste in script executor',
                colorFrom: '#28c76f',
                colorTo: '#20bf6b'
              }
            ].map((item, index) => (
              <div
                key={index}
                className="group relative backdrop-blur-xl rounded-xl p-4 transition-all duration-300 hover:scale-105 cursor-pointer"
                style={{
                  background: 'rgba(255, 255, 255, 0.08)',
                  border: '1px solid rgba(255, 255, 255, 0.15)',
                  boxShadow: '0 4px 24px rgba(0, 0, 0, 0.4)'
                }}
              >
                <div className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                     style={{ background: `linear-gradient(135deg, ${item.colorFrom}20, ${item.colorTo}20)` }}></div>
                <div className="relative z-10 flex items-center space-x-3">
                  <div className="text-2xl transform group-hover:scale-110 transition-transform duration-300">
                    {item.icon}
                  </div>
                  <div>
                    <h4 className="text-sm font-bold text-white group-hover:text-white transition-colors">
                      {item.title}
                    </h4>
                    <p className="text-gray-400 text-xs leading-relaxed group-hover:text-white/80 transition-colors">
                      {item.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
export default UserDashboard;
