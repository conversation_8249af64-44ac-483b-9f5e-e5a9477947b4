import { Suspense, useEffect } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { Toaster } from 'sonner';
import Header from './Header';
import Footer from './Footer';
import Loader from '../ui/Loader';
import { cn } from '../../lib/utils';
const MainLayout = () => {
  const location = useLocation();
  // Reset scroll on route change
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);
  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground antialiased">
      {/* Global Notifications */}
      <Toaster
        position="top-center"
        toastOptions={{
          className: 'font-sans',
          style: {
            background: 'hsl(var(--background))',
            color: 'hsl(var(--foreground))',
            border: '1px solid hsl(var(--border))',
            padding: '0.75rem 1rem',
            borderRadius: '0.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          },
        }}
      />
      {/* Header */}
      <header className="sticky top-0 z-50" id="navigation" role="banner">
        <Header />
      </header>
      {/* Main Content */}
      <main
        id="main-content"
        className="flex-grow pt-16 md:pt-20"
        role="main"
        tabIndex="-1"
      >
        <Suspense
          fallback={
            <div role="status" aria-live="polite" aria-label="Loading content">
              <Loader />
            </div>
          }
        >
          <Outlet />
        </Suspense>
      </main>
      {/* Footer */}
      <footer className="mt-auto" role="contentinfo">
        <Footer />
      </footer>
    </div>
  );
};
export default MainLayout;
