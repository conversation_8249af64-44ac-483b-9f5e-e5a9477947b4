import React from 'react';
import { Row, Col, Card, Typography, Divider, Alert, Statistic, Progress, Tag } from 'antd';
import { 
  SafetyCertificateOutlined, 
  WarningOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  ClockCircleOutlined,
  ReloadOutlined,
  KeyOutlined,
  LockOutlined,
  StopOutlined
} from '@ant-design/icons';
import { useSecurity } from '../../context/SecurityContext';
const { Title, Text } = Typography;
const SecurityOverview = ({ stats }) => {
  const { securityChecks, deviceFingerprint, blockedIps, rateLimits, runSecurityChecks } = useSecurity();
  // Calculate security score
  const allChecks = Object.values(securityChecks);
  const passedChecks = allChecks.filter(check => check.status === 'passed').length;
  const totalChecks = allChecks.length || 1; // Avoid division by zero
  const securityScore = Math.round((passedChecks / totalChecks) * 100);
  const getStatusColor = () => {
    if (securityScore >= 80) return 'hsl(var(--success))';
    if (securityScore >= 50) return 'hsl(var(--warning))';
    return 'hsl(var(--destructive))';
  };
  // Render security status card
  const renderSecurityStatus = () => (
    <Card title="Security Status" style={{ height: '100%' }}>
      <div style={{ textAlign: 'center', marginBottom: 24 }}>
        <div 
          style={{
            width: 120,
            height: 120,
            borderRadius: '50%',
            background: `conic-gradient(${getStatusColor()} ${securityScore}%, #f0f0f0 ${securityScore}% 100%)`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 16px',
            border: `8px solid ${getStatusColor()}33`
          }}
        >
          <div style={{ fontSize: 24, fontWeight: 'bold', color: getStatusColor() }}>
            {securityScore}%
          </div>
        </div>
        <Title level={4} style={{ margin: '8px 0' }}>
          {securityScore >= 80 ? 'Secure' : securityScore >= 50 ? 'Warning' : 'At Risk'}
        </Title>
        <Text type="secondary">
          {passedChecks} of {totalChecks} security checks passed
        </Text>
      </div>
      <Divider orientation="left" style={{ margin: '16px 0' }}>Security Checks</Divider>
      <div style={{ maxHeight: 200, overflowY: 'auto' }}>
        {Object.entries(securityChecks).map(([name, check]) => (
          <div key={name} style={{ 
            display: 'flex', 
            alignItems: 'center',
            marginBottom: 8,
            padding: '4px 0'
          }}>
            {check.status === 'passed' ? (
              <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
            ) : check.status === 'failed' ? (
              <CloseCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
            ) : (
              <ClockCircleOutlined style={{ color: '#faad14', marginRight: 8 }} />
            )}
            <Text style={{ flex: 1 }}>
              {name.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </Text>
            <Tag color={check.status === 'passed' ? 'success' : check.status === 'failed' ? 'error' : 'warning'}>
              {check.status}
            </Tag>
          </div>
        ))}
      </div>
      <div style={{ marginTop: 16, textAlign: 'center' }}>
        <Button 
          type="primary" 
          icon={<ReloadOutlined />} 
          onClick={runSecurityChecks}
        >
          Re-run Security Checks
        </Button>
      </div>
    </Card>
  );
  // Render device info card
  const renderDeviceInfo = () => (
    <Card title="Device Information" style={{ height: '100%' }}>
      <div style={{ marginBottom: 16 }}>
        <Text strong>Device Fingerprint:</Text>
        <div style={{ 
          fontFamily: 'monospace', 
          wordBreak: 'break-all',
          backgroundColor: '#f5f5f5',
          padding: '4px 8px',
          borderRadius: 4,
          marginTop: 4,
          display: 'inline-block'
        }}>
          {deviceFingerprint || 'Not available'}
        </div>
      </div>
      <Divider orientation="left" style={{ margin: '16px 0' }}>Security Settings</Divider>
      <div style={{ marginBottom: 8 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
          <Text>Two-Factor Authentication</Text>
          <Tag color="success">Enabled</Tag>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
          <Text>Login Notifications</Text>
          <Tag color="success">Enabled</Tag>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Text>Session Timeout</Text>
          <Tag>30 minutes</Tag>
        </div>
      </div>
    </Card>
  );
  // Render rate limits card
  const renderRateLimits = () => {
    if (!rateLimits) return null;
    const { remaining, limit, reset } = rateLimits;
    const used = limit - remaining;
    const percentage = Math.round((used / limit) * 100);
    const getStatusColor = () => {
      if (percentage < 70) return 'hsl(var(--success))';
      if (percentage < 90) return 'hsl(var(--warning))';
      return 'hsl(var(--destructive))';
    };
    return (
      <Card title="API Rate Limits" style={{ height: '100%' }}>
        <div style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
            <Text>API Requests</Text>
            <Text>{used} / {limit} ({percentage}%)</Text>
          </div>
          <div style={{ 
            width: '100%', 
            height: 8, 
            backgroundColor: '#f0f0f0',
            borderRadius: 4,
            overflow: 'hidden',
            marginBottom: 8
          }}>
            <div 
              style={{
                width: `${Math.min(100, percentage)}%`,
                height: '100%',
                backgroundColor: getStatusColor(),
                transition: 'width 0.3s'
              }}
            />
          </div>
          {reset > 0 && (
            <Text type="secondary" style={{ fontSize: 12, display: 'block', marginTop: 4 }}>
              Resets in {Math.ceil(reset / 60)} minutes
            </Text>
          )}
        </div>
        <Divider orientation="left" style={{ margin: '16px 0' }}>Blocked IPs</Divider>
        <div style={{ maxHeight: 120, overflowY: 'auto', marginBottom: 16 }}>
          {blockedIps.length > 0 ? (
            <div>
              {blockedIps.map(ip => (
                <div 
                  key={ip} 
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '4px 0',
                    borderBottom: '1px solid #f0f0f0'
                  }}
                >
                  <Text style={{ fontFamily: 'monospace' }}>{ip}</Text>
                  <Button 
                    size="small" 
                    type="text" 
                    danger 
                    icon={<StopOutlined />}
                    onClick={() => handleUnblockIp(ip)}
                  >
                    Unblock
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <Text type="secondary">No IPs are currently blocked</Text>
          )}
        </div>
        <Button 
          type="primary" 
          danger 
          icon={<StopOutlined />}
          onClick={() => setIsBlockIpModalVisible(true)}
          block
        >
          Block IP Address
        </Button>
      </Card>
    );
  };
  // Render security alerts card
  const renderSecurityAlerts = () => {
    const criticalAlerts = stats?.alerts?.filter(a => a.severity === 'high') || [];
    return (
      <Card 
        title="Security Alerts" 
        extra={stats?.alerts?.length > 0 && <Tag color="red">{stats.alerts.length} Active Alerts</Tag>}
      >
        {criticalAlerts.length > 0 ? (
          <div>
            {criticalAlerts.slice(0, 3).map(alert => (
              <Alert
                key={alert.id}
                message={alert.title}
                description={alert.message}
                type="error"
                showIcon
                style={{ marginBottom: 16 }}
              />
            ))}
            {criticalAlerts.length > 3 && (
              <div style={{ textAlign: 'center', marginTop: 8 }}>
                <Text type="secondary">And {criticalAlerts.length - 3} more alerts</Text>
              </div>
            )}
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '24px 0' }}>
            <SafetyCertificateOutlined style={{ fontSize: 48, color: '#52c41a', marginBottom: 16 }} />
            <div>No active security alerts</div>
            <Text type="secondary">Your account appears to be secure</Text>
          </div>
        )}
      </Card>
    );
  };
  return (
    <>
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} md={12} lg={8}>
          {renderSecurityStatus()}
        </Col>
        <Col xs={24} md={12} lg={8}>
          {renderDeviceInfo()}
        </Col>
        <Col xs={24} md={12} lg={8}>
          {renderRateLimits()}
        </Col>
      </Row>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          {renderSecurityAlerts()}
        </Col>
      </Row>
    </>
  );
};
export default SecurityOverview;
