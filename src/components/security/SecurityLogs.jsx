import React from 'react';
import { 
  Table, 
  Tag, 
  Typo<PERSON>, 
  Button, 
  Space, 
  Tooltip, 
  Badge,
  Card,
  Tabs,
  Select,
  DatePicker,
  Empty,
  Input
} from 'antd';
import { 
  ReloadOutlined, 
  SearchOutlined, 
  FilterOutlined, 
  DownloadOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  CloseCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
const { Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const SecurityLogs = ({ logs, loading, onRefresh }) => {
  // Use logs from props (provided by parent component)
  const [filteredLogs, setFilteredLogs] = useState(logs || []);
  useEffect(() => {
    setFilteredLogs(logs || []);
  }, [logs]);
  // Handle search and filter changes
  const handleSearch = (filters = {}) => {
    if (!logs || logs.length === 0) return;
    let result = [...logs];
    // Apply type filter
    if (filters.type) {
      result = result.filter(log => log.type === filters.type);
    }
    // Apply severity filter
    if (filters.severity) {
      result = result.filter(log => log.severity === filters.severity);
    }
    // Apply date range filter
    if (filters.dateRange && filters.dateRange[0] && filters.dateRange[1]) {
      const [start, end] = filters.dateRange;
      result = result.filter(log => {
        const logDate = new Date(log.timestamp);
        return logDate >= start && logDate <= end;
      });
    }
    // Apply search term
    if (filters.searchTerm) {
      const term = filters.searchTerm.toLowerCase();
      result = result.filter(log => 
        log.details.toLowerCase().includes(term) ||
        log.ip.toLowerCase().includes(term) ||
        (log.userAgent && log.userAgent.toLowerCase().includes(term))
      );
    }
    setFilteredLogs(result);
  };
  const getStatusColor = (severity) => {
    switch (severity) {
      case 'high':
        return 'red';
      case 'medium':
        return 'orange';
      case 'low':
        return 'blue';
      case 'info':
      default:
        return 'default';
    }
  };
  const getStatusIcon = (severity) => {
    switch (severity) {
      case 'high':
        return <CloseCircleOutlined />;
      case 'medium':
        return <WarningOutlined />;
      case 'low':
        return <InfoCircleOutlined />;
      case 'info':
      default:
        return <CheckCircleOutlined />;
    }
  };
  const getTypeLabel = (type) => {
    const typeMap = {
      login: { label: 'Login', color: 'green' },
      failed_login: { label: 'Failed Login', color: 'red' },
      logout: { label: 'Logout', color: 'blue' },
      password_change: { label: 'Password Change', color: 'purple' },
      api_call: { label: 'API Call', color: 'geekblue' },
      security_event: { label: 'Security Event', color: 'volcano' },
      system: { label: 'System', color: 'default' }
    };
    const config = typeMap[type] || { label: type, color: 'default' };
    return (
      <Tag color={config.color} style={{ textTransform: 'capitalize' }}>
        {config.label}
      </Tag>
    );
  };
  // Define columns for the logs table
  const columns = [
    {
      title: 'Event',
      dataIndex: 'type',
      key: 'type',
      render: (type) => getTypeLabel(type),
      filters: [
        { text: 'Login', value: 'login' },
        { text: 'Failed Login', value: 'failed_login' },
        { text: 'Logout', value: 'logout' },
        { text: 'Password Change', value: 'password_change' },
        { text: 'API Call', value: 'api_call' },
        { text: 'Security Event', value: 'security_event' },
      ],
      onFilter: (value, record) => record.type === value,
    },
    {
      title: 'Details',
      dataIndex: 'details',
      key: 'details',
      render: (text) => <Text>{text}</Text>,
    },
    {
      title: 'IP Address',
      dataIndex: 'ip',
      key: 'ip',
      render: (ip, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{ip}</div>
          {record.location && (
            <div style={{ fontSize: 12, color: '#666' }}>{record.location}</div>
          )}
        </div>
      ),
    },
    {
      title: 'Severity',
      dataIndex: 'severity',
      key: 'severity',
      render: (severity) => (
        <Tag 
          icon={getStatusIcon(severity)}
          color={getStatusColor(severity)}
          style={{ textTransform: 'capitalize' }}
        >
          {severity}
        </Tag>
      ),
      filters: [
        { text: 'High', value: 'high' },
        { text: 'Medium', value: 'medium' },
        { text: 'Low', value: 'low' },
        { text: 'Info', value: 'info' },
      ],
      onFilter: (value, record) => record.severity === value,
    },
    {
      title: 'Timestamp',
      dataIndex: 'timestamp',
      key: 'timestamp',
      sorter: (a, b) => new Date(a.timestamp) - new Date(b.timestamp),
      defaultSortOrder: 'descend',
      render: (timestamp) => new Date(timestamp).toLocaleString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="View details">
            <Button size="small" type="link">Details</Button>
          </Tooltip>
          {record.type === 'failed_login' && (
            <Tooltip title="Block IP">
              <Button size="small" danger type="text">Block</Button>
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];
  return (
    <Card 
      title="Security Logs"
      extra={
        <Space>
          <Tooltip title="Refresh">
            <Button 
              icon={<ReloadOutlined />} 
              onClick={onRefresh}
              loading={loading}
            />
          </Tooltip>
          <Button icon={<DownloadOutlined />}>Export</Button>
        </Space>
      }
    >
      <div style={{ marginBottom: 16 }}>
        <Space wrap>
          <div style={{ width: 250 }}>
            <Input 
                placeholder="Search logs..."
                prefix={<SearchOutlined />}
              />
          </div>
          <Select
            placeholder="Filter by type"
            style={{ width: 150 }}
            allowClear
          >
            <Option value="login">Login</Option>
            <Option value="failed_login">Failed Login</Option>
            <Option value="api_call">API Call</Option>
            <Option value="security_event">Security Event</Option>
          </Select>
          <Select
            placeholder="Filter by severity"
            style={{ width: 150 }}
            allowClear
          >
            <Option value="high">High</Option>
            <Option value="medium">Medium</Option>
            <Option value="low">Low</Option>
            <Option value="info">Info</Option>
          </Select>
          <RangePicker showTime style={{ width: 350 }} />
          <Button type="primary" icon={<FilterOutlined />}>
            Apply Filters
          </Button>
        </Space>
      </div>
      <Tabs defaultActiveKey="all">
        <Tabs.TabPane
          tab={
            <span>
              All Logs
              <Badge 
                count={mockLogs.length} 
                style={{ marginLeft: 8 }} 
                size="small" 
              />
            </span>
          }
          key="all"
        >
          <Table 
            columns={columns} 
            dataSource={mockLogs}
            rowKey="id"
            loading={loading}
            scroll={{ x: true }}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} logs`,
            }}
          />
        </Tabs.TabPane>
        <Tabs.TabPane
          tab={
            <span>
              <WarningOutlined style={{ color: '#faad14' }} />
              Suspicious Activity
              <Badge 
                count={mockLogs.filter(l => l.severity === 'high' || l.severity === 'medium').length} 
                style={{ marginLeft: 8 }} 
                size="small" 
              />
            </span>
          }
          key="suspicious"
        >
          <Table 
            columns={columns} 
            dataSource={mockLogs.filter(l => l.severity === 'high' || l.severity === 'medium')}
            rowKey="id"
            loading={loading}
            scroll={{ x: true }}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
            }}
            locale={{
              emptyText: (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={
                    <span>
                      No suspicious activity found
                    </span>
                  }
                />
              )
            }}
          />
        </Tabs.TabPane>
        <Tabs.TabPane
          tab={
            <span>
              <ClockCircleOutlined />
              Recent Activity
            </span>
          }
          key="recent"
        >
          <Table 
            columns={columns.filter(col => !['actions'].includes(col.key))} 
            dataSource={mockLogs.slice(0, 5)}
            rowKey="id"
            loading={loading}
            scroll={{ x: true }}
            pagination={false}
          />
        </Tabs.TabPane>
      </Tabs>
    </Card>
  );
};
export default SecurityLogs;
