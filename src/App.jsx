import { BrowserRouter as Router, Routes, Route, useLocation, Navigate, Outlet } from 'react-router-dom';
import { Toaster } from 'sonner';
import { AnimatePresence, motion } from 'framer-motion';
import { useEffect } from 'react';
import { ThemeProvider, useTheme } from './context/ThemeContext';
import { SecurityProvider } from './context/SecurityContext';
import { AuthProvider, useAuth } from './context/AuthContext';
import MainLayout from './components/layout/MainLayout';
import Home from './pages/Home';
import { ScriptRequestProvider } from './context/ScriptRequestContext';
import AdminRoutes from './components/admin/AdminRoutes';
import { LazyKeyGenerator } from './KeySystem/LazyKeySystem';
import { getBehaviorData } from './utils/behaviorTracker';
import ScrollToTop from './components/ui/ScrollToTop';
import { RouteTransition } from './components/ui/PageTransition';
import { SkipLinks, SkipLink } from './components/ui/SkipLink';
import {
  LazyScripts,
  LazyScriptDetail,
  LazyContact,
  LazyScriptRequest,
  LazyRequestStatus,
  LazyFAQ,
  LazyTerms,
  LazyPrivacy,
  LazyCookiePolicy,
  LazyLoginPage,
  LazyNotFound
} from './components/ui/LazyPages';

import UserDashboard from './components/dashboard/UserDashboard';
import KeyViewer from './pages/KeyViewer';
// Add theme class to body for better theme support
const ThemeWrapper = ({ children }) => {
  const { resolvedTheme } = useTheme();
  useEffect(() => {
    document.body.className = resolvedTheme === 'dark' ? 'dark' : '';
  }, [resolvedTheme]);
  return children;
};
// Protected route component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();
  const location = useLocation();
  // If still loading auth state, show loading indicator
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }
  // If not authenticated, redirect to login with return URL
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location.pathname }} replace />;
  }
  // If authenticated, render the protected content
  return children;
};
// Layout for public routes
const PublicLayout = () => {
  // Removed the automatic redirect logic to prevent conflicts with LoginPage
  // The LoginPage component handles its own redirect logic when authenticated
  return (
    <MainLayout>
      <Outlet />
    </MainLayout>
  );
};
// Main app content
const AppContent = () => {
  const location = useLocation();
  useEffect(() => {
    // Initialize behavior tracking as early as possible for security/analytics
    getBehaviorData();
  }, []);
  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground">
      {/* Skip Links for Accessibility */}
      <SkipLinks>
        <SkipLink href="#main-content">Skip to main content</SkipLink>
        <SkipLink href="#navigation">Skip to navigation</SkipLink>
      </SkipLinks>
      <RouteTransition location={location}>
        <Routes location={location}>
          {/* Public routes with layout */}
          <Route element={<PublicLayout />}>
            {/* Main Navigation */}
            <Route path="/" element={<Home />} />
            <Route path="/scripts" element={<LazyScripts />} />
            <Route path="/scripts/:id" element={<LazyScriptDetail />} />
            {/* <Route path="/projects" element={<Projects />} /> */}
            <Route path="/contact" element={<LazyContact />} />
            {/* Request System */}
            <Route path="/request-script" element={<LazyScriptRequest />} />
            <Route path="/request-status" element={<LazyRequestStatus />} />
            {/* Key System */}
            <Route path="/get-key" element={<UserDashboard />} />
            <Route path="/generate-key" element={<LazyKeyGenerator />} />

            {/* Key Viewer */}
            <Route path="/key-viewer/:keyId" element={<KeyViewer />} />
            {/* Resources */}
            <Route path="/faq" element={<LazyFAQ />} />
            {/* Legal */}
            <Route path="/terms" element={<LazyTerms />} />
            <Route path="/privacy" element={<LazyPrivacy />} />
            <Route path="/cookies" element={<LazyCookiePolicy />} />
            {/* Auth */}
            <Route path="/login" element={<LazyLoginPage />} />
            {/* Catch-all for undefined public routes */}
            <Route path="*" element={<LazyNotFound />} />
          </Route>
          {/* Admin routes with protection */}
          <Route
            path="/admin/*"
            element={
              <ProtectedRoute>
                <AdminRoutes />
              </ProtectedRoute>
            }
          />
          {/*
          <Route
            path="/keysystem/*"
            element={<KeySystemRoutes />}
          />
          */}
        </Routes>
      </RouteTransition>
      {/* Global UI Components */}
      <ScrollToTop threshold={300} showProgress={true} />
      <Toaster
        position="top-right"
        toastOptions={{
          className: '!bg-background !text-foreground !border !border-border',
        }}
      />
    </div>
  );
};
// Main App component with providers
const App = () => {
  return (
    <Router>
      <ThemeProvider>
        <ThemeWrapper>
          <AuthProvider>
            <SecurityProvider>
              <ScriptRequestProvider>
                <AppContent />
              </ScriptRequestProvider>
            </SecurityProvider>
          </AuthProvider>
        </ThemeWrapper>
      </ThemeProvider>
    </Router>
  );
};
export default App;
