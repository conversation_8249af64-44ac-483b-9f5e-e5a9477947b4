@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Simplified Color System - Light Theme */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    /* Primary - Single blue tone */
    --primary: 220 90% 56%;
    --primary-foreground: 0 0% 98%;
    --primary-hover: 220 90% 48%;

    /* Secondary - Neutral grays */
    --secondary: 240 5% 96%;
    --secondary-foreground: 240 6% 10%;

    /* Muted - Subtle grays */
    --muted: 240 5% 96%;
    --muted-foreground: 240 4% 46%;

    /* Accent - Minimal accent */
    --accent: 240 5% 96%;
    --accent-foreground: 240 6% 10%;

    /* Destructive - Simple red */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    /* Success - Simple green */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;

    /* Warning - Simple yellow */
    --warning: 38 92% 50%;
    --warning-foreground: 240 10% 3.9%;

    /* Info - Consistent with primary */
    --info: 220 90% 56%;
    --info-foreground: 0 0% 98%;

    /* Borders and inputs */
    --border: 240 6% 90%;
    --input: 240 6% 90%;
    --ring: 220 90% 56%;

    /* Border radius */
    --radius: 0.5rem;
  }

  .dark {
    /* Simplified Color System - Dark Theme */
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    /* Primary - Consistent blue */
    --primary: 220 90% 56%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 220 90% 48%;

    /* Secondary - Dark grays */
    --secondary: 240 4% 16%;
    --secondary-foreground: 0 0% 98%;

    /* Muted - Dark grays */
    --muted: 240 4% 16%;
    --muted-foreground: 240 5% 65%;

    /* Accent - Consistent with secondary */
    --accent: 240 4% 16%;
    --accent-foreground: 0 0% 98%;

    /* Destructive - Darker red */
    --destructive: 0 63% 31%;
    --destructive-foreground: 0 0% 98%;

    /* Success - Darker green */
    --success: 142 70% 45%;
    --success-foreground: 0 0% 98%;

    /* Warning - Darker yellow */
    --warning: 38 92% 50%;
    --warning-foreground: 240 10% 3.9%;

    /* Info - Consistent with primary */
    --info: 220 90% 56%;
    --info-foreground: 0 0% 98%;

    /* Borders and inputs */
    --border: 240 4% 16%;
    --input: 240 4% 16%;
    --ring: 220 90% 56%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    letter-spacing: -0.025em;
    line-height: 1.2;
  }

  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.5rem;
  }

  @media (min-width: 768px) {
    h1 {
      font-size: 3rem;
    }

    h2 {
      font-size: 2.5rem;
    }

    h3 {
      font-size: 2rem;
    }
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: hsl(var(--background));
}

::-webkit-scrollbar-thumb {
  background-color: hsla(var(--muted-foreground) / 0.2);
  border-radius: 9999px;
  transition: background-color 0.2s;
}

::-webkit-scrollbar-thumb:hover {
  background-color: hsla(var(--muted-foreground) / 0.3);
}

/* Simplified animations - reduced motion */
.animate-in {
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
@layer base {
    a {
        @apply text-primary hover:text-primary-hover transition-colors;
    }

    button {
        @apply transition-colors duration-200;
    }
}

@layer components {
    .container {
        @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
    }

    .card {
        @apply bg-card rounded-lg p-4 sm:p-6 shadow-sm border border-border;
    }

    .btn {
        @apply px-4 py-2 rounded-lg font-medium min-h-[44px] min-w-[44px] inline-flex items-center justify-center transition-colors;
    }

    .btn-primary {
        @apply btn bg-primary hover:bg-primary-hover text-primary-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
    }

    .btn-outline {
        @apply btn border border-primary text-primary hover:bg-primary hover:text-primary-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
    }

    /* Accessibility utilities */
    .sr-only {
        @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
        clip: rect(0, 0, 0, 0);
    }

    .focus-visible {
        @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
    }

    .touch-target {
        @apply min-h-[44px] min-w-[44px];
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .btn-primary {
            @apply border-2 border-white;
        }

        .btn-outline {
            @apply border-2;
        }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }

    /* Simplified spacing utilities */
    .spacing-sm { @apply p-4; }
    .spacing-md { @apply p-6; }
    .spacing-lg { @apply p-8; }

    /* Form elements */
    .form-input {
        @apply w-full px-3 py-2 border border-input bg-background rounded-md text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 min-h-[44px];
    }

    .form-label {
        @apply block text-sm font-medium text-foreground mb-2;
    }

    .form-error {
        @apply text-sm text-destructive mt-1;
    }
}
