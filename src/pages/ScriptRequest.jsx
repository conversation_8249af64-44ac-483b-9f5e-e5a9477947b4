import { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { cn } from '../lib/utils';
import { Button } from '../components/ui/Button';
import { FiAlertCircle, FiCheckCircle, FiInfo, FiHome, FiSend, FiFileText, FiUser, FiMail } from 'react-icons/fi';
const ScriptRequest = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    gameName: '',
    discordUsername: '',
    robloxUsername: '',
    gameLink: '',
    description: '',
    captcha: '',
    agreeToTerms: false,
  });
  const [status, setStatus] = useState({ type: '', message: '' });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [requestId, setRequestId] = useState(null);
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    if (status.message) {
      setStatus({ type: '', message: '' });
    }
  };
  const handleValidateForm = () => {
    if (!formData.gameName.trim()) {
      setStatus({ type: 'error', message: 'Please enter the Roblox game name' });
      return false;
    }
    if (!formData.robloxUsername.trim()) {
      setStatus({ type: 'error', message: 'Please provide your Roblox username' });
      return false;
    }
    if (!formData.description.trim() || formData.description.trim().length < 30) {
      setStatus({ type: 'error', message: 'Please provide a detailed description (at least 30 characters)' });
      return false;
    }
    if (!formData.agreeToTerms) {
      setStatus({ type: 'error', message: 'You must agree to the terms and conditions' });
      return false;
    }
    return true;
  };
  const handleRequestSubmit = async (e) => {
    e.preventDefault();
    if (!handleValidateForm()) {
      return;
    }
    setStatus({ type: 'loading', message: 'Submitting your request...' });
    setIsSubmitting(true);
    try {
      const response = await fetch('/.netlify/functions/submit-request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          script_name: formData.gameName,
          user_name: formData.robloxUsername,
          description: formData.description,
        }),
      });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || 'Failed to submit request.');
      }
      setRequestId(result.request?.id);
      setStatus({ type: 'success', message: `Your request has been submitted successfully! Your tracking ID is: ${result.request?.id}` });
      // Reset form
      setFormData({
        gameName: '',
        discordUsername: '',
        robloxUsername: '',
        gameLink: '',
        description: '',
        captcha: '',
        agreeToTerms: false,
      });
    } catch (error) {
      setStatus({ type: 'error', message: error.message });
    } finally {
      setIsSubmitting(false);
    }
  };
  const handleTrackRequestClick = () => {
    if (requestId) {
      navigate(`/request-status?id=${requestId}`);
    } else {
      setStatus({ type: 'error', message: 'Please submit your request first to get a tracking ID.' });
    }
  };
  return (
    <div className="min-h-screen pt-16 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
      <motion.div
        className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl"
        animate={{
          y: [0, -20, 0],
          x: [0, 10, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-xl"
        animate={{
          y: [0, 15, 0],
          x: [0, -15, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />
      <div className="container relative mx-auto px-4 py-12">
        <div className="max-w-3xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.6 }}
            className="relative overflow-hidden bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl p-8 md:p-12"
          >
            {/* Background gradient */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5" />
            <motion.div
              className="relative z-10 mb-10 text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <motion.div
                className="inline-block px-6 py-3 mb-6 rounded-full bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm border border-blue-500/20 text-primary text-sm font-semibold shadow-lg"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <span className="flex items-center">
                  <FiFileText className="mr-2 h-4 w-4" />
                  Script Request System
                </span>
              </motion.div>
              <motion.h1
                className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 bg-clip-text text-transparent leading-tight"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                Request a Roblox Script
              </motion.h1>
              <motion.p
                className="text-lg text-foreground/80 max-w-2xl mx-auto leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                Can't find the script you need? Request a custom Roblox script and our team will consider adding it to our collection.
              </motion.p>
            </motion.div>
            {status.message && (
              <motion.div
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.4 }}
                className={cn(
                  "relative z-10 mb-8 p-6 rounded-2xl backdrop-blur-md border shadow-lg",
                  status.type === 'error' && 'bg-red-500/10 border-red-500/30 text-red-800 dark:text-red-200',
                  status.type === 'success' && 'bg-green-500/10 border-green-500/30 text-green-800 dark:text-green-200',
                  status.type === 'loading' && 'bg-blue-500/10 border-blue-500/30 text-blue-800 dark:text-blue-200'
                )}
                aria-live="assertive"
              >
                <div className="flex items-center">
                  {status.type === 'error' && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.3, delay: 0.1 }}
                    >
                      <FiAlertCircle className="h-6 w-6 mr-3" aria-hidden="true" />
                    </motion.div>
                  )}
                  {status.type === 'success' && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.3, delay: 0.1 }}
                    >
                      <FiCheckCircle className="h-6 w-6 mr-3" aria-hidden="true" />
                    </motion.div>
                  )}
                  {status.type === 'loading' && (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="mr-3"
                    >
                      <svg className="h-6 w-6 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </motion.div>
                  )}
                  <span className="font-medium">{status.message}</span>
                </div>
                {status.type === 'success' && requestId && (
                  <motion.div
                    className="mt-4 p-4 bg-white/10 rounded-xl"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.2 }}
                  >
                    <p className="text-sm mb-2">Your request ID: <span className="font-mono font-bold text-lg">{requestId}</span></p>
                    <Link
                      to={`/request-status?id=${requestId}`}
                      className="inline-flex items-center text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200"
                      aria-label="Track your request status"
                      tabIndex={0}
                    >
                      Track your request status →
                    </Link>
                  </motion.div>
                )}
              </motion.div>
            )}
            <motion.form
              onSubmit={handleRequestSubmit}
              className="relative z-10 space-y-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <div className="space-y-8">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.7 }}
                >
                  <label htmlFor="gameName" className="block text-sm font-semibold mb-3 text-foreground/90">
                    <FiFileText className="inline mr-2 h-4 w-4" />
                    Roblox Game Name *
                  </label>
                  <motion.input
                    type="text"
                    id="gameName"
                    name="gameName"
                    value={formData.gameName}
                    onChange={handleInputChange}
                    className="w-full px-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl shadow-lg focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 text-foreground placeholder-foreground/50 hover:bg-white/15"
                    placeholder="Enter the name of the Roblox game"
                    required
                    aria-label="Roblox Game Name"
                    whileFocus={{ scale: 1.02 }}
                  />
                </motion.div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.8 }}
                  >
                    <label htmlFor="discordUsername" className="block text-sm font-semibold mb-3 text-foreground/90">
                      <FiMail className="inline mr-2 h-4 w-4" />
                      Discord Username (Optional)
                    </label>
                    <motion.input
                      type="text"
                      id="discordUsername"
                      name="discordUsername"
                      value={formData.discordUsername}
                      onChange={handleInputChange}
                      className="w-full px-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl shadow-lg focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 text-foreground placeholder-foreground/50 hover:bg-white/15"
                      placeholder="YourDiscord#0000"
                      aria-label="Discord Username"
                      whileFocus={{ scale: 1.02 }}
                    />
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.9 }}
                  >
                    <label htmlFor="robloxUsername" className="block text-sm font-semibold mb-3 text-foreground/90">
                      <FiUser className="inline mr-2 h-4 w-4" />
                      Your Roblox Username *
                    </label>
                    <motion.input
                      type="text"
                      id="robloxUsername"
                      name="robloxUsername"
                      value={formData.robloxUsername}
                      onChange={handleInputChange}
                      className="w-full px-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl shadow-lg focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 text-foreground placeholder-foreground/50 hover:bg-white/15"
                      placeholder="YourRobloxUsername"
                      required
                      aria-label="Your Roblox Username"
                      whileFocus={{ scale: 1.02 }}
                    />
                  </motion.div>
                </div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 1.0 }}
                >
                  <label htmlFor="gameLink" className="block text-sm font-semibold mb-3 text-foreground/90">
                    <FiInfo className="inline mr-2 h-4 w-4" />
                    Game Link (Optional)
                  </label>
                  <motion.input
                    type="url"
                    id="gameLink"
                    name="gameLink"
                    value={formData.gameLink}
                    onChange={handleInputChange}
                    className="w-full px-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl shadow-lg focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 text-foreground placeholder-foreground/50 hover:bg-white/15"
                    placeholder="https://www.roblox.com/games/xxxx/game-name"
                    aria-label="Game Link"
                    whileFocus={{ scale: 1.02 }}
                  />
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 1.1 }}
                >
                  <label htmlFor="description" className="block text-sm font-semibold mb-3 text-foreground/90">
                    <FiFileText className="inline mr-2 h-4 w-4" />
                    Script Description *
                  </label>
                  <motion.textarea
                    id="description"
                    name="description"
                    rows="6"
                    value={formData.description}
                    onChange={handleInputChange}
                    className="w-full px-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl shadow-lg focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 text-foreground placeholder-foreground/50 hover:bg-white/15 resize-y"
                    placeholder="Provide a detailed description of the script you need. Include specific features, functionality, and any special requirements..."
                    required
                    aria-label="Script Description"
                    whileFocus={{ scale: 1.02 }}
                  ></motion.textarea>
                  <motion.p
                    className="text-sm text-foreground/60 mt-2 flex items-center"
                    animate={{
                      color: formData.description.length >= 30 ? "#10b981" : "#ef4444"
                    }}
                  >
                    <FiInfo className="mr-1 h-3 w-3" />
                    Minimum 30 characters. Current: {formData.description.length} characters.
                  </motion.p>
                </motion.div>
                <motion.div
                  className="flex items-start p-6 bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 1.2 }}
                >
                  <motion.input
                    type="checkbox"
                    id="agreeToTerms"
                    name="agreeToTerms"
                    checked={formData.agreeToTerms}
                    onChange={handleInputChange}
                    className="mt-1 mr-4 w-5 h-5 rounded-lg border-2 border-white/30 text-blue-600 shadow-sm focus:ring-2 focus:ring-blue-500/50 bg-white/10 backdrop-blur-sm"
                    required
                    aria-label="Agree to Terms and Conditions"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  />
                  <label htmlFor="agreeToTerms" className="text-sm text-foreground/80 leading-relaxed">
                    I agree to the{' '}
                    <Link
                      to="/terms"
                      className="text-blue-400 hover:text-blue-300 underline decoration-blue-400/50 hover:decoration-blue-300 transition-colors duration-200"
                      aria-label="Read our Terms and Conditions"
                    >
                      Terms and Conditions
                    </Link>
                    {' '}and{' '}
                    <Link
                      to="/privacy"
                      className="text-blue-400 hover:text-blue-300 underline decoration-blue-400/50 hover:decoration-blue-300 transition-colors duration-200"
                      aria-label="Read our Privacy Policy"
                    >
                      Privacy Policy
                    </Link>
                    .
                  </label>
                </motion.div>
                <motion.div
                  className="w-full h-32 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 backdrop-blur-sm border border-white/20 rounded-xl flex items-center justify-center shadow-lg"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 1.3 }}
                >
                  <div className="text-center">
                    <FiCheckCircle className="w-8 h-8 text-foreground/60 mx-auto mb-2" />
                    <p className="text-foreground/60 text-sm font-medium">reCAPTCHA Verification</p>
                    <p className="text-foreground/40 text-xs">Security check placeholder</p>
                  </div>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 1.4 }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    type="submit"
                    className="w-full bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white py-4 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden text-lg"
                    disabled={isSubmitting}
                    aria-label={isSubmitting ? "Submitting request" : "Submit Request"}
                  >
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0"
                      initial={{ x: "-100%" }}
                      whileHover={{ x: "100%" }}
                      transition={{ duration: 0.6 }}
                    />
                    <span className="relative z-10 flex items-center justify-center">
                      {isSubmitting ? (
                        <>
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                            className="mr-3"
                          >
                            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                          </motion.div>
                          Submitting Request...
                        </>
                      ) : (
                        <>
                          <FiSend className="mr-3 h-5 w-5" />
                          Submit Request
                          <motion.div
                            className="ml-2"
                            animate={{ x: [0, 4, 0] }}
                            transition={{ duration: 1.5, repeat: Infinity }}
                          >
                            →
                          </motion.div>
                        </>
                      )}
                    </span>
                  </Button>
                </motion.div>
              </div>
              <motion.div
                className="mt-8 text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 1.5 }}
              >
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    variant="outline"
                    className="group px-6 py-3 text-base bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl hover:bg-white/20 transition-all duration-300"
                    onClick={() => navigate('/')}
                    ariaLabel="Back to Home page"
                  >
                    <FiHome className="mr-2 h-4 w-4 transition-transform group-hover:-translate-x-1" aria-hidden="true" />
                    Back to Home
                  </Button>
                </motion.div>
              </motion.div>
            </motion.form>
          </motion.div>
        </div>
      </div>
    </div>
  );
};
export default ScriptRequest;
