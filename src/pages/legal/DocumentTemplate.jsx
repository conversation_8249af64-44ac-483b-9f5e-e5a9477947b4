import { PageLayout, Section } from '../../components/pages/PageLayout';
import { FiChevronRight } from 'react-icons/fi';
import { Link } from 'react-router-dom';
import { cn } from '../../lib/utils';
const DocumentTemplate = ({ 
  title, 
  lastUpdated, 
  children,
  tableOfContents = []
}) => {
  const handleLinkKeyDown = (event, id) => {
    if (event.key === 'Enter' || event.key === 'Space') {
      event.preventDefault();
      document.getElementById(id)?.scrollIntoView({ behavior: 'smooth' });
    }
  };
  return (
    <div className={cn("max-w-4xl mx-auto bg-background text-foreground dark:bg-background dark:text-foreground p-4 md:p-8 rounded-xl shadow-sm")}>
      <div className={cn("mb-8")}>
        <h1 className={cn("text-3xl md:text-4xl font-bold mb-2")}>{title}</h1>
        <p className={cn("text-muted-foreground")}>Last updated: {lastUpdated}</p>
      </div>
      <div className={cn("grid grid-cols-1 lg:grid-cols-4 gap-8")}>
        {/* Table of Contents */}
        {tableOfContents.length > 0 && (
          <div className={cn("lg:col-span-1")}>
            <div className={cn("sticky top-6")}>
              <div className={cn("p-4 bg-muted/30 dark:bg-muted/50 rounded-lg")}>
                <h2 className={cn("font-semibold mb-3 text-lg")}>Table of Contents</h2>
                <nav>
                  <ul className={cn("space-y-2")}>
                    {tableOfContents.map((item, index) => (
                      <li key={index} className={cn("")}>
                        <a 
                          href={`#${item.id}`}
                          className={cn("flex items-center text-sm text-muted-foreground hover:text-foreground dark:hover:text-white transition-colors")}
                          tabIndex={0}
                          onKeyDown={(e) => handleLinkKeyDown(e, item.id)}
                          aria-label={`Jump to ${item.title} section`}
                        >
                          <FiChevronRight className={cn("h-4 w-4 mr-1")} aria-hidden="true" />
                          {item.title}
                        </a>
                      </li>
                    ))}
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        )}
        {/* Main Content */}
        <div className={cn(tableOfContents.length > 0 ? 'lg:col-span-3' : 'col-span-full')}>
          <div className={cn("prose dark:prose-invert max-w-none")}>
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};
export default DocumentTemplate;
