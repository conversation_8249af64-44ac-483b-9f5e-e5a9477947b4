import { motion } from 'framer-motion';
import { FiSettings, FiShield, FiEye, FiUsers, FiMail, FiClock, FiCheckCircle, FiInfo, FiLock, FiMonitor, FiDatabase } from 'react-icons/fi';
const CookiePolicy = () => {
  const lastUpdated = 'July 4, 2025';
  const tableOfContents = [
    { id: 'introduction', title: 'Introduction', icon: FiInfo },
    { id: 'what-are-cookies', title: 'What Are Cookies', icon: FiDatabase },
    { id: 'how-we-use', title: 'How We Use Cookies', icon: FiEye },
  ];
  return (
    <div className="min-h-screen pt-16 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
      <motion.div
        className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl"
        animate={{
          y: [0, -20, 0],
          x: [0, 10, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-xl"
        animate={{
          y: [0, 15, 0],
          x: [0, -15, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />
      <div className="container relative mx-auto px-4 py-12">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div
              className="inline-block px-6 py-3 mb-6 rounded-full bg-gradient-to-r from-orange-500/10 to-red-500/10 backdrop-blur-sm border border-orange-500/20 text-primary text-sm font-semibold shadow-lg"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <span className="flex items-center">
                <FiDatabase className="mr-2 h-4 w-4" />
                Cookie Usage & Privacy
              </span>
            </motion.div>
            <motion.h1
              className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 bg-clip-text text-transparent leading-tight"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              Cookie Policy
            </motion.h1>
            <motion.p
              className="text-lg text-foreground/80 max-w-2xl mx-auto leading-relaxed mb-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              Learn how we use cookies to enhance your experience and protect your privacy on our platform.
            </motion.p>
            <motion.p
              className="text-sm text-foreground/60"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              Last updated: {lastUpdated}
            </motion.p>
          </motion.div>
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Table of Contents */}
            <motion.div
              className="lg:col-span-1"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <div className="sticky top-24">
                <div className="p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-lg">
                  <h2 className="font-bold mb-4 text-lg text-foreground flex items-center">
                    <FiEye className="mr-2 h-5 w-5 text-orange-400" />
                    Table of Contents
                  </h2>
                  <nav>
                    <ul className="space-y-3">
                      {tableOfContents.map((item, index) => (
                        <motion.li
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: 0.7 + index * 0.1 }}
                        >
                          <a
                            href={`#${item.id}`}
                            className="flex items-center text-sm text-foreground/70 hover:text-foreground transition-all duration-200 p-2 rounded-lg hover:bg-white/10 group"
                            aria-label={`Jump to ${item.title} section`}
                          >
                            <item.icon className="h-4 w-4 mr-3 text-orange-400 group-hover:text-orange-300 transition-colors" />
                            <span className="group-hover:translate-x-1 transition-transform duration-200">{item.title}</span>
                          </a>
                        </motion.li>
                      ))}
                    </ul>
                  </nav>
                </div>
              </div>
            </motion.div>
            {/* Content */}
            <motion.div
              className="lg:col-span-3"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
            >
              <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl p-8 md:p-12 space-y-12">
                {/* Introduction */}
                <motion.section
                  id="introduction"
                  className="scroll-mt-24"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <div className="flex items-center mb-6">
                    <div className="p-3 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-xl mr-4">
                      <FiInfo className="h-6 w-6 text-orange-400" />
                    </div>
                    <h2 className="text-3xl font-bold text-foreground">1. Introduction</h2>
                  </div>
                  <div className="space-y-4 text-foreground/80 leading-relaxed">
                    <p>
                      This Cookie Policy explains how <span className="font-semibold text-foreground">6FootScripts</span> uses cookies and similar tracking technologies when you visit our Roblox script hub platform. We believe in transparency about how we collect and use data related to you.
                    </p>
                    <p>
                      By continuing to use our website, you acknowledge that you have read and understood this Cookie Policy and consent to our use of cookies as described herein. You can withdraw your consent at any time by adjusting your browser settings.
                    </p>
                    <div className="p-4 bg-orange-500/10 border border-orange-500/20 rounded-xl">
                      <p className="text-sm font-medium text-orange-400 mb-2">Quick Summary</p>
                      <p className="text-sm">We use cookies to enhance your experience, analyze usage, and provide personalized content. You have full control over cookie preferences.</p>
                    </div>
                  </div>
                </motion.section>
                {/* What Are Cookies */}
                <motion.section
                  id="what-are-cookies"
                  className="scroll-mt-24"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <div className="flex items-center mb-6">
                    <div className="p-3 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-xl mr-4">
                      <FiDatabase className="h-6 w-6 text-yellow-400" />
                    </div>
                    <h2 className="text-3xl font-bold text-foreground">2. What Are Cookies</h2>
                  </div>
                  <div className="space-y-6">
                    <p className="text-foreground/80 leading-relaxed">
                      Cookies are small text files that websites place on your device to store information about your visit and preferences. Think of them as digital sticky notes that help websites remember you.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-white/5 border border-white/10 rounded-xl">
                        <h3 className="text-xl font-semibold mb-4 text-foreground flex items-center">
                          <FiClock className="mr-2 h-5 w-5 text-blue-400" />
                          Session Cookies
                        </h3>
                        <p className="text-foreground/80 text-sm">
                          Temporary cookies that are deleted when you close your browser. They help maintain your session while navigating our site.
                        </p>
                      </div>
                      <div className="p-6 bg-white/5 border border-white/10 rounded-xl">
                        <h3 className="text-xl font-semibold mb-4 text-foreground flex items-center">
                          <FiLock className="mr-2 h-5 w-5 text-green-400" />
                          Persistent Cookies
                        </h3>
                        <p className="text-foreground/80 text-sm">
                          Stored on your device until they expire or you delete them. They remember your preferences across visits.
                        </p>
                      </div>
                    </div>
                    <div className="p-6 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-xl">
                      <h3 className="text-lg font-semibold mb-3 text-foreground">How Cookies Work</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-foreground/80">
                        <div className="flex items-start">
                          <div className="w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mr-3 mt-0.5">
                            <span className="text-xs font-bold text-blue-400">1</span>
                          </div>
                          <div>
                            <p className="font-medium">Visit Website</p>
                            <p>Your browser requests our website</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <div className="w-6 h-6 bg-purple-500/20 rounded-full flex items-center justify-center mr-3 mt-0.5">
                            <span className="text-xs font-bold text-purple-400">2</span>
                          </div>
                          <div>
                            <p className="font-medium">Cookie Stored</p>
                            <p>Small data file saved to your device</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <div className="w-6 h-6 bg-pink-500/20 rounded-full flex items-center justify-center mr-3 mt-0.5">
                            <span className="text-xs font-bold text-pink-400">3</span>
                          </div>
                          <div>
                            <p className="font-medium">Data Retrieved</p>
                            <p>Information used to enhance experience</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.section>
                {/* How We Use Cookies */}
                <motion.section
                  id="how-we-use"
                  className="scroll-mt-24"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <div className="flex items-center mb-6">
                    <div className="p-3 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl mr-4">
                      <FiEye className="h-6 w-6 text-purple-400" />
                    </div>
                    <h2 className="text-3xl font-bold text-foreground">3. How We Use Cookies</h2>
                  </div>
                  <div className="space-y-6">
                    <p className="text-foreground/80 leading-relaxed">
                      We use cookies to enhance your experience on our Roblox script hub platform in several important ways:
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl">
                          <h4 className="font-semibold text-blue-400 mb-2">Essential Functions</h4>
                          <ul className="text-sm text-foreground/80 space-y-1">
                            <li>• Maintain your login session</li>
                            <li>• Remember your script preferences</li>
                            <li>• Store your key generation progress</li>
                            <li>• Enable secure authentication</li>
                          </ul>
                        </div>
                        <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-xl">
                          <h4 className="font-semibold text-green-400 mb-2">Performance & Analytics</h4>
                          <ul className="text-sm text-foreground/80 space-y-1">
                            <li>• Monitor website performance</li>
                            <li>• Analyze user behavior patterns</li>
                            <li>• Track popular scripts and features</li>
                            <li>• Optimize loading times</li>
                          </ul>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="p-4 bg-purple-500/10 border border-purple-500/20 rounded-xl">
                          <h4 className="font-semibold text-purple-400 mb-2">Personalization</h4>
                          <ul className="text-sm text-foreground/80 space-y-1">
                            <li>• Remember your theme preferences</li>
                            <li>• Customize script recommendations</li>
                            <li>• Save your favorite scripts</li>
                            <li>• Maintain dashboard settings</li>
                          </ul>
                        </div>
                        <div className="p-4 bg-orange-500/10 border border-orange-500/20 rounded-xl">
                          <h4 className="font-semibold text-orange-400 mb-2">Security & Safety</h4>
                          <ul className="text-sm text-foreground/80 space-y-1">
                            <li>• Detect suspicious activity</li>
                            <li>• Prevent unauthorized access</li>
                            <li>• Implement rate limiting</li>
                            <li>• Track security violations</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.section>
                {/* Contact Section */}
                <motion.section
                  id="contact"
                  className="scroll-mt-24"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <div className="flex items-center mb-6">
                  </div>
                  <div className="space-y-6">
                    <div className="p-6 bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-xl">
                      <h3 className="text-lg font-semibold mb-3 text-foreground">Your Rights</h3>
                      <p className="text-foreground/80 mb-4">You have the right to:</p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <ul className="space-y-2 text-sm text-foreground/80">
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Accept or decline cookies</li>
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Delete existing cookies</li>
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Opt out of tracking</li>
                        </ul>
                        <ul className="space-y-2 text-sm text-foreground/80">
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Request cookie information</li>
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Update preferences anytime</li>
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Withdraw consent</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </motion.section>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default CookiePolicy;
