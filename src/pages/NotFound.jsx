import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { <PERSON>H<PERSON>, FiSearch, FiArrowLeft } from 'react-icons/fi';
const NotFound = () => {
  return (
    <div className="min-h-screen pt-16 bg-background relative overflow-hidden flex items-center justify-center">
      {/* Background effects */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
      <motion.div
        className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl"
        animate={{
          y: [0, -20, 0],
          x: [0, 10, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-xl"
        animate={{
          y: [0, 15, 0],
          x: [0, -15, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />
      <div className="relative z-10 text-center px-4 max-w-2xl mx-auto">
        <motion.div
          className="p-8 bg-card border border-border rounded-lg"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* 404 Number */}
          <motion.div
            className="text-8xl md:text-9xl font-bold text-primary mb-6"
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            404
          </motion.div>
          {/* Title */}
          <motion.h1
            className="text-3xl md:text-4xl font-bold text-card-foreground mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            Page Not Found
          </motion.h1>
          {/* Description */}
          <motion.p
            className="text-lg text-muted-foreground mb-8 leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            Sorry, the page you are looking for doesn't exist or has been moved.
            Let's get you back on track!
          </motion.p>
          {/* Action Buttons */}
          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Link
              to="/"
              className="flex items-center justify-center px-6 py-3 bg-primary text-primary-foreground hover:bg-primary-hover font-semibold rounded-lg transition-colors group"
            >
              <FiHome className="h-5 w-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
              Back to Home
            </Link>
            <Link
              to="/scripts"
              className="flex items-center justify-center px-6 py-3 bg-secondary border border-border text-card-foreground hover:bg-secondary/80 hover:border-primary/30 font-semibold rounded-lg transition-colors group"
            >
              <FiSearch className="h-5 w-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
              Browse Scripts
            </Link>
          </motion.div>
          {/* Helpful Links */}
          <motion.div
            className="mt-8 pt-6 border-t border-white/20"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.7 }}
          >
            <p className="text-sm text-muted-foreground mb-4">Looking for something specific?</p>
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <Link to="/faq" className="text-primary hover:text-primary-hover transition-colors">
                FAQ
              </Link>
              <Link to="/contact" className="text-primary hover:text-primary-hover transition-colors">
                Contact Support
              </Link>
              <Link to="/request-script" className="text-primary hover:text-primary-hover transition-colors">
                Request Script
              </Link>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};
export default NotFound;
