import { motion } from 'framer-motion';
import { FiShield, FiLock, FiEye, FiDatabase, FiUsers, FiGlobe, FiMail, FiPhone, FiMapPin, FiClock, FiCheckCircle } from 'react-icons/fi';
const PrivacyPolicy = () => {
  const lastUpdated = 'July 4, 2025';
  const tableOfContents = [
    { id: 'introduction', title: 'Introduction', icon: FiShield },
    { id: 'data-collection', title: 'Information We Collect', icon: FiDatabase },
    { id: 'data-usage', title: 'How We Use Your Information', icon: FiEye },
  ];
  return (
    <div className="min-h-screen pt-16 bg-background relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
      <motion.div
        className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl"
        animate={{
          y: [0, -20, 0],
          x: [0, 10, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-xl"
        animate={{
          y: [0, 15, 0],
          x: [0, -15, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />
      <div className="container relative mx-auto px-4 py-12">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div
              className="inline-block px-6 py-3 mb-6 rounded-full bg-secondary border border-border text-primary text-sm font-semibold"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <span className="flex items-center">
                <FiShield className="mr-2 h-4 w-4" />
                Privacy & Data Protection
              </span>
            </motion.div>
            <motion.h1
              className="text-4xl md:text-5xl font-bold mb-6 text-foreground leading-tight"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              Privacy Policy
            </motion.h1>
            <motion.p
              className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed mb-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              Your privacy is important to us. This policy explains how we collect, use, and protect your information.
            </motion.p>
            <motion.p
              className="text-sm text-foreground/60"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              Last updated: {lastUpdated}
            </motion.p>
          </motion.div>
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Table of Contents */}
            <motion.div
              className="lg:col-span-1"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <div className="sticky top-24">
                <div className="p-6 bg-card border border-border rounded-lg">
                  <h2 className="font-bold mb-4 text-lg text-card-foreground flex items-center">
                    <FiEye className="mr-2 h-5 w-5 text-primary" />
                    Table of Contents
                  </h2>
                  <nav>
                    <ul className="space-y-3">
                      {tableOfContents.map((item, index) => (
                        <motion.li
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: 0.7 + index * 0.1 }}
                        >
                          <a
                            href={`#${item.id}`}
                            className="flex items-center text-sm text-foreground/70 hover:text-foreground transition-all duration-200 p-2 rounded-lg hover:bg-white/10 group"
                            aria-label={`Jump to ${item.title} section`}
                          >
                            <item.icon className="h-4 w-4 mr-3 text-blue-400 group-hover:text-blue-300 transition-colors" />
                            <span className="group-hover:translate-x-1 transition-transform duration-200">{item.title}</span>
                          </a>
                        </motion.li>
                      ))}
                    </ul>
                  </nav>
                </div>
              </div>
            </motion.div>
            {/* Content */}
            <motion.div
              className="lg:col-span-3"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
            >
              <div className="bg-card border border-border rounded-lg p-8 md:p-12 space-y-12">
                {/* Introduction */}
                <motion.section
                  id="introduction"
                  className="scroll-mt-24"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <div className="flex items-center mb-6">
                    <div className="p-3 bg-primary/10 rounded-lg mr-4">
                      <FiShield className="h-6 w-6 text-primary" />
                    </div>
                    <h2 className="text-3xl font-bold text-foreground">1. Introduction</h2>
                  </div>
                  <div className="space-y-4 text-foreground/80 leading-relaxed">
                    <p>
                      At <span className="font-semibold text-foreground">Project Madara</span>, we are committed to protecting your privacy and ensuring the security of your personal information. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our Roblox script hub service.
                    </p>
                    <p>
                      By accessing or using our service, you acknowledge that you have read, understood, and agree to be bound by this Privacy Policy. If you do not agree with our practices, please do not use our service.
                    </p>
                    <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl">
                      <p className="text-sm font-medium text-blue-400 mb-2">Important Notice</p>
                      <p className="text-sm">This policy is designed to comply with GDPR, CCPA, and other applicable privacy regulations.</p>
                    </div>
                  </div>
                </motion.section>
                {/* Data Collection */}
                <motion.section
                  id="data-collection"
                  className="scroll-mt-24"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <div className="flex items-center mb-6">
                    <div className="p-3 bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-xl mr-4">
                      <FiDatabase className="h-6 w-6 text-green-400" />
                    </div>
                    <h2 className="text-3xl font-bold text-foreground">2. Information We Collect</h2>
                  </div>
                  <div className="space-y-6">
                    <p className="text-foreground/80 leading-relaxed">
                      We collect information to provide and improve our services. The types of information we collect include:
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="p-6 bg-white/5 border border-white/10 rounded-xl">
                        <h3 className="text-xl font-semibold mb-4 text-foreground flex items-center">
                          <FiUsers className="mr-2 h-5 w-5 text-blue-400" />
                          Personal Information
                        </h3>
                        <ul className="space-y-2 text-foreground/80">
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Email address</li>
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Username and display name</li>
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Roblox username (for script delivery)</li>
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Profile information (optional)</li>
                        </ul>
                      </div>
                      <div className="p-6 bg-white/5 border border-white/10 rounded-xl">
                        <h3 className="text-xl font-semibold mb-4 text-foreground flex items-center">
                          <FiGlobe className="mr-2 h-5 w-5 text-purple-400" />
                          Technical Information
                        </h3>
                        <ul className="space-y-2 text-foreground/80">
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />IP address and location data</li>
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Browser type and version</li>
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Device information</li>
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Usage analytics and logs</li>
                        </ul>
                      </div>
                    </div>
                    <div className="p-6 bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-xl">
                      <h3 className="text-xl font-semibold mb-4 text-foreground flex items-center">
                        <FiLock className="mr-2 h-5 w-5 text-orange-400" />
                        Security & Authentication Data
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-foreground/80">
                        <div>
                          <p className="font-medium mb-2">Account Security:</p>
                          <ul className="space-y-1 text-sm">
                            <li>• Encrypted passwords (bcrypt hashed)</li>
                            <li>• Login timestamps and IP addresses</li>
                            <li>• Failed login attempt logs</li>
                            <li>• Session tokens and authentication data</li>
                          </ul>
                        </div>
                        <div>
                          <p className="font-medium mb-2">Anti-Fraud Measures:</p>
                          <ul className="space-y-1 text-sm">
                            <li>• Device fingerprinting</li>
                            <li>• Behavioral analysis patterns</li>
                            <li>• Rate limiting and ban data</li>
                            <li>• Security violation logs</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.section>
                {/* Data Usage */}
                <motion.section
                  id="data-usage"
                  className="scroll-mt-24"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <div className="flex items-center mb-6">
                    <div className="p-3 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl mr-4">
                      <FiEye className="h-6 w-6 text-purple-400" />
                    </div>
                    <h2 className="text-3xl font-bold text-foreground">3. How We Use Your Information</h2>
                  </div>
                  <div className="space-y-6">
                    <p className="text-foreground/80 leading-relaxed">
                      We use the collected information for various purposes to provide, maintain, and improve our services:
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl">
                          <h4 className="font-semibold text-blue-400 mb-2">Service Delivery</h4>
                          <ul className="text-sm text-foreground/80 space-y-1">
                            <li>• Provide and maintain our script hub service</li>
                            <li>• Process script requests and deliveries</li>
                            <li>• Generate and manage access keys</li>
                            <li>• Authenticate user accounts</li>
                          </ul>
                        </div>
                        <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-xl">
                          <h4 className="font-semibold text-green-400 mb-2">Communication</h4>
                          <ul className="text-sm text-foreground/80 space-y-1">
                            <li>• Send service-related notifications</li>
                            <li>• Respond to customer support inquiries</li>
                            <li>• Provide updates about new features</li>
                            <li>• Send security alerts when necessary</li>
                          </ul>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="p-4 bg-purple-500/10 border border-purple-500/20 rounded-xl">
                          <h4 className="font-semibold text-purple-400 mb-2">Security & Safety</h4>
                          <ul className="text-sm text-foreground/80 space-y-1">
                            <li>• Detect and prevent fraudulent activity</li>
                            <li>• Monitor for security threats</li>
                            <li>• Enforce our terms of service</li>
                            <li>• Implement temporary bans when needed</li>
                          </ul>
                        </div>
                        <div className="p-4 bg-orange-500/10 border border-orange-500/20 rounded-xl">
                          <h4 className="font-semibold text-orange-400 mb-2">Analytics & Improvement</h4>
                          <ul className="text-sm text-foreground/80 space-y-1">
                            <li>• Analyze usage patterns and trends</li>
                            <li>• Improve our service performance</li>
                            <li>• Develop new features and scripts</li>
                            <li>• Optimize user experience</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.section>
                {/* Contact Section */}
                <motion.section
                  id="contact"
                  className="scroll-mt-24"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <div className="flex items-center mb-6">
                    <div className="p-6 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-xl">
                      <h3 className="text-lg font-semibold mb-3 text-foreground">Your Rights</h3>
                      <p className="text-foreground/80 mb-4">Under GDPR and other privacy laws, you have the right to:</p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <ul className="space-y-2 text-sm text-foreground/80">
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Access your personal data</li>
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Correct inaccurate data</li>
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Delete your data</li>
                        </ul>
                        <ul className="space-y-2 text-sm text-foreground/80">
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Data portability</li>
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Withdraw consent</li>
                          <li className="flex items-center"><FiCheckCircle className="mr-2 h-4 w-4 text-green-400" />Object to processing</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </motion.section>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default PrivacyPolicy;
