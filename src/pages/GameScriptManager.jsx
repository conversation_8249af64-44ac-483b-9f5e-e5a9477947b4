import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FiPlus, FiEdit, FiTrash2, FiEye, FiGamepad2, FiCode, FiUsers, FiActivity } from 'react-icons/fi';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { Badge } from '../components/ui/Badge';

const GameScriptManager = () => {
  const [gameScripts, setGameScripts] = useState([]);
  const [gameRegistry, setGameRegistry] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedScript, setSelectedScript] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch game scripts and registry
  useEffect(() => {
    fetchGameScripts();
    fetchGameRegistry();
  }, []);

  const fetchGameScripts = async () => {
    try {
      const response = await fetch('/.netlify/functions/scripts?type=game_specific');
      const data = await response.json();
      setGameScripts(data || []);
    } catch (error) {
      console.error('Failed to fetch game scripts:', error);
    }
  };

  const fetchGameRegistry = async () => {
    try {
      const response = await fetch('/.netlify/functions/game-registry');
      const data = await response.json();
      setGameRegistry(data || []);
    } catch (error) {
      console.error('Failed to fetch game registry:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredScripts = gameScripts.filter(script =>
    script.game_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    script.place_id?.toString().includes(searchTerm)
  );

  const getSupportLevelColor = (level) => {
    switch (level) {
      case 'full': return 'bg-green-500';
      case 'partial': return 'bg-yellow-500';
      case 'experimental': return 'bg-blue-500';
      case 'deprecated': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading game scripts...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-2">Game Script Manager</h1>
          <p className="text-muted-foreground">Manage PlaceId-based game scripts for the centralized loader system</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="p-6">
            <div className="flex items-center">
              <FiGamepad2 className="h-8 w-8 text-blue-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Games</p>
                <p className="text-2xl font-bold text-foreground">{gameRegistry.length}</p>
              </div>
            </div>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center">
              <FiCode className="h-8 w-8 text-green-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Scripts</p>
                <p className="text-2xl font-bold text-foreground">{gameScripts.filter(s => s.is_active).length}</p>
              </div>
            </div>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center">
              <FiUsers className="h-8 w-8 text-purple-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Supported Games</p>
                <p className="text-2xl font-bold text-foreground">{gameRegistry.filter(g => g.is_supported).length}</p>
              </div>
            </div>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center">
              <FiActivity className="h-8 w-8 text-orange-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Loads</p>
                <p className="text-2xl font-bold text-foreground">{gameRegistry.reduce((sum, g) => sum + (g.total_script_loads || 0), 0)}</p>
              </div>
            </div>
          </Card>
        </div>

        {/* Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <Input
              placeholder="Search by game name or PlaceId..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          <Button
            onClick={() => setShowAddModal(true)}
            className="flex items-center gap-2"
          >
            <FiPlus className="h-4 w-4" />
            Add Game Script
          </Button>
        </div>

        {/* Game Scripts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredScripts.map((script) => {
            const registryEntry = gameRegistry.find(g => g.place_id === script.place_id);
            
            return (
              <motion.div
                key={script.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="p-6 hover:shadow-lg transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-foreground mb-1">
                        {script.game_name || 'Unknown Game'}
                      </h3>
                      <p className="text-sm text-muted-foreground mb-2">
                        PlaceId: {script.place_id}
                      </p>
                      <div className="flex items-center gap-2 mb-2">
                        <Badge 
                          variant={script.is_active ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {script.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                        {registryEntry && (
                          <Badge 
                            className={`text-xs text-white ${getSupportLevelColor(registryEntry.support_level)}`}
                          >
                            {registryEntry.support_level}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedScript(script)}
                      >
                        <FiEye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {/* Edit functionality */}}
                      >
                        <FiEdit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700"
                        onClick={() => {/* Delete functionality */}}
                      >
                        <FiTrash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                    {script.description}
                  </p>
                  
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>Size: {Math.round((script.file_size_bytes || 0) / 1024)}KB</span>
                    <span>v{script.version}</span>
                  </div>
                  
                  {registryEntry && (
                    <div className="mt-3 pt-3 border-t border-border">
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-muted-foreground">Loads: {registryEntry.total_script_loads || 0}</span>
                        <span className="text-muted-foreground">Users: {registryEntry.unique_users || 0}</span>
                      </div>
                    </div>
                  )}
                </Card>
              </motion.div>
            );
          })}
        </div>

        {filteredScripts.length === 0 && (
          <div className="text-center py-12">
            <FiGamepad2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">No game scripts found</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm ? 'No scripts match your search criteria.' : 'Get started by adding your first game script.'}
            </p>
            <Button onClick={() => setShowAddModal(true)}>
              <FiPlus className="h-4 w-4 mr-2" />
              Add Game Script
            </Button>
          </div>
        )}

        {/* Script Details Modal */}
        {selectedScript && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-background rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
              <div className="p-6 border-b border-border">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-foreground">
                    {selectedScript.game_name} Script Details
                  </h2>
                  <Button
                    variant="ghost"
                    onClick={() => setSelectedScript(null)}
                  >
                    ✕
                  </Button>
                </div>
              </div>
              
              <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">PlaceId</label>
                    <p className="text-foreground">{selectedScript.place_id}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Version</label>
                    <p className="text-foreground">{selectedScript.version}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">File Size</label>
                    <p className="text-foreground">{Math.round((selectedScript.file_size_bytes || 0) / 1024)}KB</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                    <p className="text-foreground">{new Date(selectedScript.updated_at).toLocaleDateString()}</p>
                  </div>
                </div>
                
                <div className="mb-4">
                  <label className="text-sm font-medium text-muted-foreground">Description</label>
                  <p className="text-foreground mt-1">{selectedScript.description}</p>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Script Content Preview</label>
                  <pre className="bg-muted p-4 rounded-lg mt-2 text-sm overflow-x-auto">
                    {selectedScript.content?.substring(0, 1000)}
                    {selectedScript.content?.length > 1000 && '...'}
                  </pre>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GameScriptManager;
