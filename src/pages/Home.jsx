import React from 'react';
import { <PERSON>, useNavigate } from 'react-router-dom';
import { 
  FiArrowRight, 
  FiCode, 
  FiZap, 
  FiShield, 
  FiGithub, 
  FiDownload, 
  FiMail,
  FiStar,
  FiUsers,
  FiRefreshCw,
  FiCheckCircle,
  FiTrendingUp,
  FiAward
} from 'react-icons/fi';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import Team from '../components/Team';
import ExecutorsList from '../components/ExecutorsList';
import { cn } from '../lib/utils';
// Simplified background - removed complex grid patterns
const Home = () => {
  const navigate = useNavigate();
  const handleExploreScriptsClick = () => {
    navigate('/scripts');
  };
  const handleLearnMoreClick = () => {
    const el = document.getElementById('features');
    if (el) el.scrollIntoView({ behavior: 'smooth' });
  };
  const features = [
    {
      icon: <FiCode className="h-8 w-8 text-primary" />,
      title: 'Premium Scripts',
      description: 'Access to exclusive, high-performance Roblox scripts with advanced features and regular updates.',
    },
    {
      icon: <FiZap className="h-8 w-8 text-primary" />,
      title: 'Optimized Performance',
      description: 'Lightning-fast scripts optimized for maximum performance and minimal resource usage.',
    },
    {
      icon: <FiShield className="h-8 w-8 text-primary" />,
      title: 'Safe & Undetectable',
      description: 'Carefully tested to ensure safety and prevent detection by anti-cheat systems.',
    },
    {
      icon: <FiUsers className="h-8 w-8 text-primary" />,
      title: 'Active Community',
      description: 'Join thousands of Roblox players in our thriving community.',
    },
    {
      icon: <FiRefreshCw className="h-8 w-8 text-primary" />,
      title: 'Frequent Updates',
      description: 'Regular script updates to ensure compatibility with the latest Roblox versions.',
    },
    {
      icon: <FiCheckCircle className="h-8 w-8 text-primary" />,
      title: 'Easy to Use',
      description: 'Simple installation and user-friendly interface for all skill levels.',
    },
  ];
  const stats = [
    { value: '500+', label: 'Active Scripts' },
    { value: '100K+', label: 'Users' },
    { value: '99.9%', label: 'Uptime' },
    { value: '24/7', label: 'Support' },
  ];
  return (
    <div className="min-h-screen bg-background">{/* Simplified background */}
      {/* Hero Section */}
      <section className="pt-24 pb-20 md:pt-32 md:pb-28">{/* Simplified hero section */}
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-block px-6 py-3 mb-8 rounded-full bg-secondary border border-border text-primary text-sm font-medium">
              <span className="flex items-center">
                <FiStar className="mr-2 h-4 w-4" />
                Trusted by 100,000+ Roblox Players
              </span>
            </div>
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight leading-tight text-foreground mb-6">
              Take Your Roblox
              <br />
              Experience <span className="text-primary">Further</span>
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed mb-12">
              Unlock the full potential of your favorite Roblox games with our premium scripts.
              Join thousands of players who trust us for the best scripting experience.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-6">
              <Button
                size="lg"
                className="px-10 py-5 text-lg font-semibold"
                onClick={handleExploreScriptsClick}
                ariaLabel="Explore Scripts"
              >
                Explore Scripts
                <FiArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="px-10 py-5 text-lg font-semibold"
                onClick={handleLearnMoreClick}
                ariaLabel="Learn More about our features"
              >
                Learn More
              </Button>
            </div>
            {/* Stats */}
            <div className="mt-20 grid grid-cols-2 sm:grid-cols-4 gap-8 max-w-4xl mx-auto">
              {stats.map((stat, index) => (
                <div
                  key={index}
                  className="text-center p-6 bg-card rounded-lg border border-border"
                >
                  <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                    {stat.value}
                  </div>
                  <div className="text-sm font-medium text-muted-foreground uppercase tracking-wider">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
      {/* Features Section */}
      <section id="features" className="py-16 md:py-24 bg-secondary/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <div className="inline-block px-6 py-3 mb-6 rounded-full bg-secondary border border-border text-primary text-sm font-semibold">
              <span className="flex items-center">
                <FiZap className="mr-2 h-4 w-4" />
                Why Choose Us
              </span>
            </div>
            <h2 className="text-4xl sm:text-5xl font-bold tracking-tight text-foreground mb-6">
              Discover the Power of <span className="text-primary">Madara</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Our cutting-edge features are designed to provide you with an unparalleled Roblox experience,
              combining security, performance, and innovation.
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="p-8 text-center">
                <div className="w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-6 bg-primary/10 text-primary">
                  {feature.icon}
                </div>
                <Card.Title className="text-xl font-bold text-card-foreground mb-4">
                  {feature.title}
                </Card.Title>
                <Card.Description className="text-muted-foreground leading-relaxed">
                  {feature.description}
                </Card.Description>
              </Card>
            ))}
          </div>
        </div>
      </section>
      {/* Executors List Section */}
      <section className="py-16 md:py-24 bg-background dark:bg-gray-900">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold tracking-tight text-foreground">
              Our Supported <span className="text-primary">Executors</span>
            </h2>
            <p className="mt-4 text-lg text-muted-foreground max-w-2xl mx-auto">
              We proudly support a wide range of popular Roblox executors, ensuring broad compatibility and ease of use.
            </p>
          </div>
          <ExecutorsList />
        </div>
      </section>
      {/* Team Section */}
      <section className="py-16 md:py-24 bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20 relative overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <Team />
        </div>
      </section>
      {/* Contact Section */}
      <section id="contact" className="py-16 md:py-24 bg-background dark:bg-gray-900">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold tracking-tight text-foreground">
              Get in <span className="text-primary">Touch</span>
            </h2>
            <p className="mt-4 text-lg text-muted-foreground max-w-2xl mx-auto">
              Have questions or need support? Reach out to us through our contact channels.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button
              size="lg"
              className="px-8 py-4 text-lg font-semibold"
              onClick={() => navigate('/contact')}
              ariaLabel="Go to contact us page"
            >
              Contact Us
              <FiMail className="ml-2 h-5 w-5" />
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="px-8 py-4 text-lg font-semibold"
              onClick={() => navigate('/faq')}
              ariaLabel="Go to FAQ page"
            >
              Visit FAQ
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};
export default Home;
