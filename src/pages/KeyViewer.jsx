import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { FiKey, FiCopy, FiDownload, FiClock, FiShield } from 'react-icons/fi';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
const KeyViewer = () => {
  const { keyId } = useParams();
  const [loading, setLoading] = useState(true);
  const [keyData, setKeyData] = useState(null);
  const [error, setError] = useState(null);
  const [copied, setCopied] = useState(false);
  useEffect(() => {
    loadKeyData();
  }, [keyId]);
  const loadKeyData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/.netlify/functions/get-key-details/${keyId}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      if (response.ok) {
        const data = await response.json();
        setKeyData(data);
      } else {
        setError('Key not found or access denied');
      }
    } catch (error) {
      setError('Failed to load key details');
    } finally {
      setLoading(false);
    }
  };
  const handleCopyKey = async () => {
    try {
      // Direct copy without ads
      if (keyData?.key_code) {
        await navigator.clipboard.writeText(keyData.key_code);
        alert('Key copied to clipboard!');
      } else {
        alert('No key data available');
      }
    } catch (error) {
      alert('Failed to copy key');
    }
  };
  const handleDownloadKey = async () => {
    try {
      // Direct download without ads
      if (keyData?.key_code) {
        const blob = new Blob([keyData.key_code], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `madara-key-${keyId}.txt`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert('Failed to create download link');
      }
    } catch (error) {
      alert('Failed to create download link');
    }
  };
  const getTimeRemaining = (expiresAt) => {
    const now = new Date();
    const expires = new Date(expiresAt);
    const diff = expires - now;
    if (diff <= 0) return 'Expired';
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}d ${hours % 24}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };
  if (loading) {
    return (
      <div className="min-h-screen pt-16 bg-background flex items-center justify-center">
        <div className="text-center">
          <FiKey className="mx-auto text-4xl text-primary mb-4 animate-pulse" />
          <p className="text-muted-foreground">Loading your key...</p>
        </div>
      </div>
    );
  }
  if (error) {
    return (
      <div className="min-h-screen pt-16 bg-background flex items-center justify-center">
        <Card className="p-8 max-w-md text-center">
          <FiShield className="mx-auto text-4xl text-destructive mb-4" />
          <h2 className="text-xl font-bold text-destructive mb-2">Access Denied</h2>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={() => window.location.href = '/get-key'}>
            Go to Your Keys
          </Button>
        </Card>
      </div>
    );
  }
  return (
    <div className="min-h-screen pt-16 bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold mb-4 text-foreground">
              Your <span className="text-primary">License Key</span>
            </h1>
            <p className="text-muted-foreground">
              {keyData.game_name} - {keyData.script_name}
            </p>
          </div>
          <Card className="p-8">
            {/* Key Display */}
            <div className="text-center mb-8">
              <div className="bg-secondary rounded-lg p-6 mb-4">
                <div className="font-mono text-lg font-bold text-foreground break-all">
                  {keyData.key_code}
                </div>
              </div>
              <div className="flex justify-center space-x-4">
                <Button
                  onClick={handleCopyKey}
                  className="flex items-center"
                  variant={copied ? "outline" : "default"}
                >
                  <FiCopy className="mr-2" />
                  {copied ? 'Copied!' : 'Copy Key'}
                </Button>
                <Button
                  onClick={handleDownloadKey}
                  variant="outline"
                  className="flex items-center"
                >
                  <FiDownload className="mr-2" />
                  Download
                </Button>
              </div>
            </div>
            {/* Key Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="text-center p-4 bg-primary/10 rounded-lg">
                <FiClock className="mx-auto text-2xl text-primary mb-2" />
                <div className="font-semibold text-primary">Time Remaining</div>
                <div className="text-primary">{getTimeRemaining(keyData.expires_at)}</div>
              </div>
              <div className="text-center p-4 bg-primary/10 rounded-lg">
                <FiShield className="mx-auto text-2xl text-primary mb-2" />
                <div className="font-semibold text-primary">Status</div>
                <div className="text-primary capitalize">{keyData.status}</div>
              </div>
            </div>
            {/* Usage Instructions */}
            <div className="mt-8 p-4 bg-secondary border border-border rounded-lg">
              <h3 className="font-semibold text-foreground mb-2">How to Use Your Key</h3>
              <ol className="text-muted-foreground text-sm space-y-1">
                <li>1. Copy the key above</li>
                <li>2. Open your Roblox script executor</li>
                <li>3. Paste the key when prompted</li>
                <li>4. Enjoy your {keyData.game_name} script!</li>
              </ol>
            </div>
            {/* Key Details */}
            <div className="mt-6 text-sm text-muted-foreground space-y-1">
              <div>Generated: {new Date(keyData.generated_at).toLocaleString()}</div>
              <div>Expires: {new Date(keyData.expires_at).toLocaleString()}</div>
              <div>Usage Count: {keyData.usage_count || 0} times</div>
            </div>
            {/* Actions */}
            <div className="mt-8 text-center space-x-4">
              <Button
                onClick={() => window.location.href = '/get-key'}
                variant="outline"
              >
                Back to Dashboard
              </Button>
              <Button
                onClick={() => window.location.href = '/generate-key'}
              >
                Generate Another Key
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};
export default KeyViewer;
