import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiChevronDown, FiHelpCircle, FiMessageCircle, FiSearch, FiCode, FiShield, FiUsers } from 'react-icons/fi';
const FAQ = () => {
  const faqCategories = [
    {
      id: 'general',
      title: 'General Questions',
      icon: FiHelpCircle,
      items: [
        {
          question: 'What is Project Madara?',
          answer: 'Project Madara is a Roblox script service, which supports multipple games other then that nothing'
        },
        {
          question: 'Is Priject Madara free to use?',
          answer: 'Yes, Project Madara is free to use. However, if i feel like it i might add Premium Keys.'
        },
        {
          question: 'How do I get started with Roblox scripts?',
          answer: 'Getting started is easy! First, ensure you have a compatible script executor. Then, check our script page for script that you need for your game.'
        },
        {
          question: 'What makes Project Madara different from other script platforms?',
          answer: 'We prioritize security, reliability, and user experience. All our scripts are thoroughly tested, updated, and we are just better jk.'
        }
      ]
    },
    {
      id: 'scripts',
      title: 'Scripts & Usage',
      icon: FiCode,
      items: [
        {
          question: 'How do I use these scripts?',
          answer: 'Each script comes with detailed instructions. Generally, you\'ll need a script executor compatible with Roblox. Copy the script from our platform and run it using your preferred executor. We provide step-by-step guides for popular executors.'
        },
        {
          question: 'Are these scripts safe to use?',
          answer: 'We thoroughly verify and test all scripts before publishing. However, we recommend using them responsibly and at your own discretion. Always ensure you\'re downloading from our official platform and follow our safety guidelines.'
        },
        {
          question: 'Why isn\'t my script working?',
          answer: 'Common issues include: incompatible executor, outdated script version, or incorrect implementation. Make sure you\'re using a supported executor, the script is up to date, and you\'ve followed all instructions. Check our troubleshooting guide or contact support for assistance.'
        },
        {
          question: 'How often are scripts updated?',
          answer: 'We regularly update our scripts to ensure compatibility with the latest Roblox updates. Critical updates are released immediately, while feature updates follow a scheduled release cycle. Premium users receive priority access to updates.'
        }
      ]
    },
    {
      id: 'security',
      title: 'Security & Safety',
      icon: FiShield,
      items: [
        {
          question: 'How do you ensure script security?',
          answer: 'We employ multiple security measures including code review, automated scanning, sandbox testing, and community reporting. All scripts undergo rigorous testing before publication to ensure they meet our security standards.'
        },
        {
          question: 'What should I do if I encounter a malicious script?',
          answer: 'Immediately stop using the script and report it to our security team through our contact form. We take security reports seriously and investigate all claims promptly. Never download scripts from unofficial sources.'
        },
        {
          question: 'Can using scripts get my Roblox account banned?',
          answer: 'While we strive to provide safe scripts, using any third-party scripts carries inherent risks. We recommend reading Roblox\'s Terms of Service and using scripts responsibly. We cannot guarantee protection against account actions.'
        }
      ]
    },
    {
      id: 'support',
      title: 'Support & Community',
      icon: FiUsers,
      items: [
        {
          question: 'How can I get help with a script?',
          answer: 'You can get help through our contact form, community forums, or by checking our comprehensive documentation. Premium users have access to priority support with faster response times.'
        },
        {
          question: 'Can I request custom scripts?',
          answer: 'Yes! We accept custom script requests through our script request system. While we can\'t guarantee all requests will be fulfilled, we review each one and prioritize based on community demand and feasibility.'
        },
        {
          question: 'How do I report bugs or issues?',
          answer: 'Report bugs through our contact form with detailed information including the script name, executor used, error messages, and steps to reproduce the issue. This helps us resolve problems quickly.'
        }
      ]
    }
  ];
  const [openItems, setOpenItems] = useState({});
  const [searchQuery, setSearchQuery] = useState('');
  const handleToggleFaqItem = (categoryId, itemIndex) => {
    setOpenItems(prev => ({
      ...prev,
      [`${categoryId}-${itemIndex}`]: !prev[`${categoryId}-${itemIndex}`]
    }));
  };
  const handleContactSupportKeyDown = (event) => {
    if (event.key === 'Enter' || event.key === 'Space') {
      event.preventDefault();
      window.location.href = '/contact';
    }
  };
  // Filter FAQ items based on search query
  const filteredCategories = faqCategories.map(category => ({
    ...category,
    items: category.items.filter(item =>
      item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.answer.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(category => category.items.length > 0);
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    // Close all items when searching
    if (e.target.value) {
      setOpenItems({});
    }
  };
  return (
    <div className="min-h-screen pt-16 bg-background">{/* Simplified background */}
      <div className="container relative mx-auto px-4 py-12 md:py-16">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-block px-6 py-3 mb-6 rounded-full bg-secondary border border-border text-primary text-sm font-semibold">
            <span className="flex items-center">
              <FiHelpCircle className="mr-2 h-4 w-4" />
              FAQ
            </span>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6 text-foreground">
            Frequently Asked <span className="text-primary">Questions</span>
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            Find answers to common questions about Project Madara and our Roblox script platform. Can't find what you're looking for? Contact our support team.
          </p>
        </div>
        {/* Search Section */}
        <div className="max-w-2xl mx-auto mb-12">
          <div className="relative">
            <input
              type="text"
              placeholder="Search frequently asked questions..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="form-input pl-12"
              aria-label="Search FAQ"
            />
            <FiSearch className="absolute left-4 top-1/2 -translate-y-1/2 text-muted-foreground h-5 w-5" aria-hidden="true" />
          </div>
        </div>
        {/* FAQ Categories */}
        <div className="space-y-8">
          {filteredCategories.length > 0 ? (
            filteredCategories.map((category, categoryIndex) => (
              <div
                key={category.id}
                className="p-6 bg-card border border-border rounded-lg"
              >
                {/* Category Header */}
                <div className="flex items-center mb-6">
                  <div className="p-3 bg-primary/10 rounded-lg mr-4">
                    <category.icon className="h-6 w-6 text-primary" />
                  </div>
                  <h2 className="text-2xl font-bold text-card-foreground">{category.title}</h2>
                </div>
                {/* FAQ Items */}
                <div className="space-y-4">
                  {category.items.map((item, index) => (
                    <motion.div
                      key={index}
                      className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden shadow-lg"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: 0.9 + categoryIndex * 0.1 + index * 0.05 }}
                      whileHover={{ scale: 1.01 }}
                    >
                      <motion.button
                        onClick={() => handleToggleFaqItem(category.id, index)}
                        className="w-full flex items-center justify-between p-6 text-left hover:bg-white/5 transition-all duration-300 group"
                        aria-expanded={openItems[`${category.id}-${index}`]}
                        aria-controls={`faq-${category.id}-${index}`}
                        aria-label={`Toggle answer for ${item.question}`}
                        tabIndex={0}
                        whileHover={{ x: 5 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <span className="font-semibold text-card-foreground text-lg pr-4 group-hover:text-primary transition-colors duration-300">
                          {item.question}
                        </span>
                        <motion.div
                          animate={{ rotate: openItems[`${category.id}-${index}`] ? 180 : 0 }}
                          transition={{ duration: 0.3 }}
                          className="flex-shrink-0"
                        >
                          <FiChevronDown className="h-5 w-5 text-muted-foreground group-hover:text-primary transition-colors duration-300" aria-hidden="true" />
                        </motion.div>
                      </motion.button>
                      <AnimatePresence>
                        {openItems[`${category.id}-${index}`] && (
                          <motion.div
                            id={`faq-${category.id}-${index}`}
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: "auto", opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            className="overflow-hidden"
                          >
                            <motion.div
                              className="px-6 pb-6 pt-0"
                              initial={{ y: -10 }}
                              animate={{ y: 0 }}
                              transition={{ duration: 0.3, delay: 0.1 }}
                            >
                              <div className="h-px bg-gradient-to-r from-blue-500/20 to-purple-500/20 mb-4"></div>
                              <p className="text-foreground/80 leading-relaxed">
                                {item.answer}
                              </p>
                            </motion.div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  ))}
                </div>
              </div>
            ))
          ) : (
            <motion.div
              className="text-center py-12 p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <FiSearch className="h-12 w-12 text-foreground/40 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-foreground mb-2">No results found</h3>
              <p className="text-foreground/60">
                Try adjusting your search terms or browse all categories below.
              </p>
            </motion.div>
          )}
        </div>
        {/* Contact Support Section */}
        <div className="text-center py-12 p-8 bg-card border border-border rounded-lg">
          <div className="inline-block p-4 bg-primary/10 rounded-lg mb-6">
            <FiMessageCircle className="h-8 w-8 text-primary" />
          </div>
          <h3 className="text-2xl font-bold text-card-foreground mb-4">
            Still have questions?
          </h3>
          <p className="text-muted-foreground mb-8 max-w-md mx-auto leading-relaxed">
            Can't find what you're looking for? Our support team is here to help you with any questions or issues.
          </p>
          <a
            href="/contact"
            className="inline-flex items-center justify-center px-8 py-4 bg-primary text-primary-foreground rounded-lg font-semibold hover:bg-primary-hover transition-colors"
            aria-label="Contact Support"
            tabIndex={0}
            onKeyDown={handleContactSupportKeyDown}
          >
            <FiMessageCircle className="mr-2 h-5 w-5" />
            Contact Support
          </a>
        </div>
      </div>
    </div>
  );
};
export default FAQ;
