import { motion } from 'framer-motion';
import { cn } from '../lib/utils';
const About = () => {
  return (
    <div className={cn("container mx-auto px-4 py-16")}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className={cn("max-w-4xl mx-auto")}
      >
        <h1 className={cn("text-4xl md:text-5xl font-bold mb-8 text-center text-foreground")}>
          About <span className={cn("text-primary")}>Me</span>
        </h1>
        <div className={cn("bg-card border border-border rounded-lg p-8 mb-12")}>
          <div className={cn("prose dark:prose-invert max-w-none")}>
            <h2>Who am I?</h2>
            <p>
              I'm a passionate developer with expertise in modern web technologies. 
              I love creating beautiful, responsive, and user-friendly applications 
              that solve real-world problems.
            </p>
            <h3>My Skills</h3>
            <div className={cn("flex flex-wrap gap-2 mb-6")}>
              {['React', 'JavaScript', 'TypeScript', 'Node.js', 'Tailwind CSS',
                'Git', 'RESTful APIs', 'GraphQL', 'Next.js'].map((skill, index) => (
                <span
                  key={skill}
                  className={cn(
                    "px-3 py-1 bg-primary/10 text-primary rounded-full text-sm"
                  )}
                >
                  {skill}
                </span>
              ))}
            </div>
            <h3>Experience</h3>
            <div className={cn("space-y-6")}>
              <div className="">
                <h4 className={cn("font-semibold text-lg")}>Frontend Developer</h4>
                <p className={cn("text-muted-foreground")}>Company Name • 2022 - Present</p>
                <p className={cn("mt-1")}>
                  Building amazing web applications with React and modern JavaScript frameworks.
                </p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};
export default About;
