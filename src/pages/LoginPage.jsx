import { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { cn } from '../lib/utils';
import { <PERSON>Lock, FiUser, <PERSON><PERSON>ye, FiEyeOff } from 'react-icons/fi';
import { Form, FormInput, FormActions } from '../components/ui/Form';
import { Button } from '../components/ui/Button';
import { useFocus, useAnnouncement } from '../hooks/useAccessibility';
/**
 * Admin Login Page
 */
const LoginPage = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const location = useLocation();
  const { login, isAuthenticated } = useAuth();
  // Accessibility hooks
  const usernameRef = useFocus(true); // Focus username field on mount
  const announce = useAnnouncement();
  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const from = location.state?.from || '/admin';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location.state]);
  const handleLogin = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    // Announce loading state to screen readers
    announce('Signing in...', 'assertive');
    try {
      // Use ONLY the AuthContext login function to avoid double API calls
      const success = await login(username, password);
      if (success) {
        // Announce success
        announce('Sign in successful. Redirecting to dashboard...', 'assertive');
        // Use React Router navigation instead of window.location.href to avoid page reload
        const from = location.state?.from || '/admin';
        navigate(from, { replace: true });
      } else {
        const errorMessage = 'Invalid username or password';
        setError(errorMessage);
        announce(errorMessage, 'assertive');
      }
    } catch (err) {
      const errorMessage = 'Login failed. Please try again.';
      setError(errorMessage);
      announce(errorMessage, 'assertive');
    } finally {
      setIsLoading(false);
    }
  };
  const handleUsernameChange = (e) => {
    setUsername(e.target.value);
    setError('');
  };
  const handlePasswordChange = (e) => {
    setPassword(e.target.value);
    setError('');
  };
  const handleBackToHomeClick = (e) => {
    e.preventDefault();
    navigate('/');
  };
  const handleTogglePassword = () => setShowPassword((prev) => !prev);
  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="w-full max-w-md p-8 bg-card border border-border rounded-lg flex flex-col items-center text-card-foreground">
        {/* Logo/Icon */}
        <div className="mb-6 flex flex-col items-center">
          <div className="bg-primary/10 rounded-full p-3 mb-2">
            <FiLock className="text-primary w-8 h-8" aria-hidden="true" />
          </div>
          <h1 className="text-3xl font-bold mb-1 text-card-foreground">Admin Login</h1>
          <p className="text-muted-foreground text-sm">Sign in to access the admin dashboard</p>
        </div>
        {error && (
          <div className="mb-4 w-full p-3 bg-destructive/10 text-destructive rounded-md text-sm border border-destructive/20" aria-live="assertive">
            {error}
          </div>
        )}
        <form onSubmit={handleLogin} className="w-full space-y-6">
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-card-foreground mb-1">
              Username
            </label>
            <div className="relative">
              <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-muted-foreground">
                <FiUser className="w-5 h-5" />
              </span>
              <input
                id="username"
                type="text"
                className="form-input pl-10"
                placeholder="Enter your username"
                value={username}
                onChange={handleUsernameChange}
                disabled={isLoading}
                autoComplete="username"
                tabIndex={0}
                aria-label="Username"
                required
              />
            </div>
          </div>
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-card-foreground mb-1">
              Password
            </label>
            <div className="relative">
              <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-muted-foreground">
                <FiLock className="w-5 h-5" />
              </span>
              <input
                id="password"
                type={showPassword ? 'text' : 'password'}
                className="form-input pl-10 pr-10"
                placeholder="Enter your password"
                value={password}
                onChange={handlePasswordChange}
                disabled={isLoading}
                autoComplete="current-password"
                tabIndex={0}
                aria-label="Password"
                required
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 flex items-center px-3 text-muted-foreground hover:text-primary focus:outline-none"
                onClick={handleTogglePassword}
                tabIndex={0}
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? <FiEyeOff className="w-5 h-5" /> : <FiEye className="w-5 h-5" />}
              </button>
            </div>
          </div>
          <button
            type="submit"
            className="w-full flex justify-center py-2 px-4 bg-primary text-primary-foreground hover:bg-primary-hover rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-ring disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            disabled={isLoading}
            aria-label="Sign in"
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Signing in...
              </>
            ) : 'Sign In'}
          </button>
          <div className="text-center">
            <a
              href="/"
              className="text-sm font-medium text-primary hover:text-primary-hover hover:underline"
              onClick={handleBackToHomeClick}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === 'Space') {
                  e.preventDefault();
                  handleBackToHomeClick(e);
                }
              }}
              tabIndex={0}
              aria-label="Back to Home page"
            >
               Back to Home
            </a>
          </div>
        </form>
      </div>
    </div>
  );
};
export default LoginPage;
