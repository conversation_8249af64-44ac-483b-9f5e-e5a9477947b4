import { useState } from 'react';
import { motion } from 'framer-motion';
import { FiMessageCircle, FiHelpCircle, FiMail, FiUser, FiEdit3, FiSend, FiClock, FiCheckCircle, FiExternalLink } from 'react-icons/fi';
import { Link } from 'react-router-dom';
const Contact = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const contactMethods = [
    {
      icon: FiMessageCircle,
      title: 'Discord Community',
      description: 'Join our community for real-time help and support',
      value: 'Join Our Discord',
      action: 'https://discord.gg/uh',
      isExternal: true,
      color: 'bg-primary/10',
      iconColor: 'text-primary'
    },
    {
      icon: FiHelpCircle,
      title: 'FAQ Center',
      description: 'Find answers to frequently asked questions',
      value: 'Browse FAQ',
      action: '/faq',
      isExternal: false,
      color: 'bg-secondary',
      iconColor: 'text-primary'
    },
  ];
  return (
    <div className="min-h-screen pt-16 bg-background">{/* Simplified background */}
      <div className="container relative mx-auto px-4 py-12 md:py-16">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-block px-6 py-3 mb-6 rounded-full bg-secondary border border-border text-primary text-sm font-semibold">
            <span className="flex items-center">
              <FiMail className="mr-2 h-4 w-4" />
              Get in Touch
            </span>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6 text-foreground">
            Contact <span className="text-primary">Us</span>
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            Have questions or need support? Our team is here to help you with any inquiries about 6FootScripts.
          </p>
        </div>
        {/* Contact Methods */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-16 max-w-4xl mx-auto">
          {contactMethods.map((method, index) => (
            <div key={index} className="p-6 bg-card border border-border rounded-lg">
              <div className={`p-4 ${method.color} rounded-lg mb-4 w-fit mx-auto`}>
                <method.icon className={`h-8 w-8 ${method.iconColor}`} />
              </div>
              <h3 className="text-xl font-bold text-card-foreground mb-2 text-center">{method.title}</h3>
              <p className="text-muted-foreground mb-4 text-center text-sm leading-relaxed">{method.description}</p>
              {method.isExternal ? (
                <a
                  href={method.action}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-center px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover transition-colors"
                >
                  <span className="mr-2 text-sm font-medium">{method.value}</span>
                  <FiExternalLink className="h-4 w-4" />
                </a>
              ) : (
                <Link
                  to={method.action}
                  className="flex items-center justify-center px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover transition-colors"
                >
                  <span className="mr-2 text-sm font-medium">{method.value}</span>
                  <FiExternalLink className="h-4 w-4" />
                </Link>
              )}
            </div>
          ))}
        </div>
        {/* Contact Form */}
        <div className="max-w-2xl mx-auto">
          <div className="p-8 bg-card border border-border rounded-lg">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-card-foreground mb-2">Send us a Message</h2>
              <p className="text-muted-foreground">Fill out the form below and we'll get back to you as soon as possible.</p>
            </div>
            {isSubmitted && (
              <div className="mb-6 p-4 bg-primary/10 border border-primary/20 rounded-lg flex items-center">
                <FiCheckCircle className="h-5 w-5 text-primary mr-3" />
                <span className="text-primary font-medium">Message sent successfully! We'll get back to you soon.</span>
              </div>
            )}
            <div className="mt-8 p-4 bg-secondary border border-border rounded-lg">
              <div className="flex items-center text-muted-foreground">
                <FiClock className="h-4 w-4 mr-2" />
                <span className="text-sm">
                  <strong>Response Time:</strong> We typically respond within 24-48 hours.
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default Contact;
