// Real security service that connects to the database
const getAdminHeaders = () => ({
  'x-session-token': sessionStorage.getItem('adminSessionToken') || '',
  'Content-Type': 'application/json',
});

const securityService = {
  // License key management
  keys: {
    list: async (params = {}) => {
      const queryParams = new URLSearchParams({
        page: params.page || 1,
        limit: params.limit || 50,
        status: params.status || 'all',
        ...(params.search && { search: params.search })
      });

      const response = await fetch(`/.netlify/functions/security?action=keys&${queryParams}`, {
        headers: getAdminHeaders(),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch keys');
      }

      return await response.json();
    },

    create: async (keyData) => {
      const response = await fetch('/.netlify/functions/security?action=create-key', {
        method: 'POST',
        headers: getAdminHeaders(),
        body: JSON.stringify(keyData),
      });

      if (!response.ok) {
        throw new Error('Failed to create key');
      }

      return await response.json();
    },

    update: async (keyId, updateData) => {
      const response = await fetch('/.netlify/functions/security?action=update-key', {
        method: 'PUT',
        headers: getAdminHeaders(),
        body: JSON.stringify({ id: keyId, ...updateData }),
      });

      if (!response.ok) {
        throw new Error('Failed to update key');
      }

      return await response.json();
    },

    delete: async (keyId) => {
      const response = await fetch('/.netlify/functions/security?action=delete-key', {
        method: 'DELETE',
        headers: getAdminHeaders(),
        body: JSON.stringify({ id: keyId }),
      });

      if (!response.ok) {
        throw new Error('Failed to delete key');
      }

      return await response.json();
    },

    getStats: async () => {
      const response = await fetch('/.netlify/functions/admin-keys?stats=true', {
        headers: getAdminHeaders(),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch key stats');
      }

      return await response.json();
    }
  },

  // Security events and monitoring
  security: {
    getEvents: async () => {
      const response = await fetch('/.netlify/functions/security?action=events', {
        headers: getAdminHeaders(),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch security events');
      }

      return await response.json();
    },

    getIpBans: async () => {
      const response = await fetch('/.netlify/functions/security?action=ip-bans', {
        headers: getAdminHeaders(),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch IP bans');
      }

      return await response.json();
    },

    banIp: async (banData) => {
      const response = await fetch('/.netlify/functions/security?action=ban-ip', {
        method: 'POST',
        headers: getAdminHeaders(),
        body: JSON.stringify(banData),
      });

      if (!response.ok) {
        throw new Error('Failed to ban IP');
      }

      return await response.json();
    },

    unbanIp: async (ipAddress) => {
      const response = await fetch('/.netlify/functions/security?action=unban-ip', {
        method: 'POST',
        headers: getAdminHeaders(),
        body: JSON.stringify({ ip_address: ipAddress }),
      });

      if (!response.ok) {
        throw new Error('Failed to unban IP');
      }

      return await response.json();
    }
  },

  // Report security violations (for client-side security)
  reportViolation: async (violationData) => {
    try {
      // This could be expanded to report to a security endpoint
      console.warn('Security violation detected:', violationData);

      // In a real implementation, you might want to send this to a logging service
      // const response = await fetch('/.netlify/functions/security?action=report-violation', {
      //   method: 'POST',
      //   headers: getAdminHeaders(),
      //   body: JSON.stringify(violationData),
      // });

      return { success: true };
    } catch (error) {
      console.error('Failed to report security violation:', error);
      return { success: false, error: error.message };
    }
  }
};

export default securityService;