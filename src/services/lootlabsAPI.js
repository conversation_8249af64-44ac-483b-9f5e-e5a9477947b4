import { LOOTLABS_CONFIG } from '../config/keys.js';
/**
 * Lootlabs API Integration Service
 * Handles all interactions with the Lootlabs API for content locking and URL encryption
 */
class LootlabsAPI {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseURL = LOOTLABS_CONFIG.API_BASE_URL;
  }
  /**
   * Create a content locker link using Lootlabs API
   * @param {Object} options - Link creation options
   * @param {string} options.title - Title for the link (max 30 characters)
   * @param {string} options.url - Destination URL to lock
   * @param {number} options.tier_id - Advertisement tier (1-3)
   * @param {number} options.number_of_tasks - Number of ads (1-5)
   * @param {number} options.theme - Theme ID (1-5, optional)
   * @param {string} options.thumbnail - Thumbnail URL (optional)
   * @returns {Promise<Object>} API response with loot_url and short code
   */
  async createContentLocker(options) {
    const { title, url, tier_id, number_of_tasks, theme, thumbnail } = options;
    // Validate required parameters
    if (!title || !url || !tier_id || !number_of_tasks) {
      throw new Error('Missing required parameters: title, url, tier_id, number_of_tasks');
    }
    // Validate parameter ranges
    if (title.length > 30) {
      throw new Error('Title must be 30 characters or less');
    }
    if (tier_id < 1 || tier_id > 3) {
      throw new Error('tier_id must be between 1 and 3');
    }
    if (number_of_tasks < LOOTLABS_CONFIG.MIN_TASKS || number_of_tasks > LOOTLABS_CONFIG.MAX_TASKS) {
      throw new Error(`number_of_tasks must be between ${LOOTLABS_CONFIG.MIN_TASKS} and ${LOOTLABS_CONFIG.MAX_TASKS}`);
    }
    if (theme && (theme < 1 || theme > 5)) {
      throw new Error('theme must be between 1 and 5');
    }
    const requestBody = {
      title,
      url,
      tier_id,
      number_of_tasks,
      ...(theme && { theme }),
      ...(thumbnail && { thumbnail })
    };
    try {
      const response = await fetch(`${this.baseURL}${LOOTLABS_CONFIG.CONTENT_LOCKER_ENDPOINT}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
      }
      if (data.type === 'error') {
        throw new Error(data.message || 'Lootlabs API returned an error');
      }
      return data;
    } catch (error) {
      throw new Error(`Failed to create content locker: ${error.message}`);
    }
  }
  /**
   * Encrypt a URL for use with the anti-bypass redirect API
   * @param {string} destinationUrl - URL to encrypt
   * @returns {Promise<string>} Encrypted URL string for use in &data parameter
   */
  async encryptUrl(destinationUrl) {
    if (!destinationUrl) {
      throw new Error('destinationUrl is required');
    }
    const requestBody = {
      destination_url: destinationUrl,
      api_token: this.apiKey
    };
    try {
      const response = await fetch(`${this.baseURL}${LOOTLABS_CONFIG.URL_ENCRYPTOR_ENDPOINT}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
      }
      if (data.type === 'error') {
        throw new Error(data.message || 'Lootlabs API returned an error');
      }
      return data.message; // The encrypted URL string
    } catch (error) {
      throw new Error(`Failed to encrypt URL: ${error.message}`);
    }
  }
  /**
   * Create a content locker with anti-bypass protection
   * @param {Object} options - Link creation options
   * @param {string} options.baseUrl - Base destination URL
   * @param {string} options.redirectUrl - Alternative redirect URL for anti-bypass
   * @param {Object} options.lockerOptions - Content locker options (title, tier_id, etc.)
   * @returns {Promise<Object>} Content locker with encrypted redirect capability
   */
  async createProtectedContentLocker(options) {
    const { baseUrl, redirectUrl, lockerOptions } = options;
    try {
      // Create the main content locker
      const contentLocker = await this.createContentLocker({
        ...lockerOptions,
        url: baseUrl
      });
      // If redirect URL is provided, encrypt it for anti-bypass
      let encryptedRedirect = null;
      if (redirectUrl) {
        encryptedRedirect = await this.encryptUrl(redirectUrl);
      }
      return {
        ...contentLocker,
        encrypted_redirect: encryptedRedirect,
        protected_url: encryptedRedirect
          ? `${contentLocker.message[0].loot_url}&data=${encryptedRedirect}`
          : contentLocker.message[0].loot_url
      };
    } catch (error) {
      throw new Error(`Failed to create protected content locker: ${error.message}`);
    }
  }
  /**
   * Get tier information for display purposes
   * @returns {Object} Tier information with descriptions
   */
  static getTierInfo() {
    return {
      [LOOTLABS_CONFIG.TIERS.MCPEDL_SAFE]: {
        id: LOOTLABS_CONFIG.TIERS.MCPEDL_SAFE,
        name: 'Trending & Recommended',
        description: 'MCPEDL Safe - Family-friendly gaming content',
        revenue: 'Standard'
      },
      [LOOTLABS_CONFIG.TIERS.GAMING_OFFERS]: {
        id: LOOTLABS_CONFIG.TIERS.GAMING_OFFERS,
        name: 'Gaming Offers & Recommendations',
        description: 'Gaming-focused advertisements and offers',
        revenue: 'Higher'
      },
      [LOOTLABS_CONFIG.TIERS.PROFIT_MAX]: {
        id: LOOTLABS_CONFIG.TIERS.PROFIT_MAX,
        name: 'Profit Maximization',
        description: 'Maximum revenue potential with premium ads',
        revenue: 'Highest'
      }
    };
  }
  /**
   * Get theme information for display purposes
   * @returns {Object} Theme information with descriptions
   */
  static getThemeInfo() {
    return {
      [LOOTLABS_CONFIG.THEMES.CLASSIC]: {
        id: LOOTLABS_CONFIG.THEMES.CLASSIC,
        name: 'Classic',
        description: 'Clean, professional design'
      },
      [LOOTLABS_CONFIG.THEMES.SIMS]: {
        id: LOOTLABS_CONFIG.THEMES.SIMS,
        name: 'Sims',
        description: 'Sims-themed design'
      },
      [LOOTLABS_CONFIG.THEMES.MINECRAFT]: {
        id: LOOTLABS_CONFIG.THEMES.MINECRAFT,
        name: 'Minecraft',
        description: 'Minecraft-themed design'
      },
      [LOOTLABS_CONFIG.THEMES.GTA]: {
        id: LOOTLABS_CONFIG.THEMES.GTA,
        name: 'GTA',
        description: 'Grand Theft Auto themed design'
      },
      [LOOTLABS_CONFIG.THEMES.SPACE]: {
        id: LOOTLABS_CONFIG.THEMES.SPACE,
        name: 'Space',
        description: 'Futuristic space-themed design'
      }
    };
  }
}
export default LootlabsAPI;
// Export a configured instance for use throughout the app
// Note: API key should be set from environment variables in production
export const lootlabsAPI = new LootlabsAPI(process.env.LOOTLABS_API_KEY || '2f98a08a2f6f50d0e019640ee4b0e272fe4446c7f0979a5ceddcd0f6a9629ace');
