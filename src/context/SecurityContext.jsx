import React, { createContext, useState, useEffect, useCallback, useMemo, useContext } from 'react';
import { message } from 'antd';
import clientProtection from '../utils/clientProtection';
import { getUnbreakableFingerprint } from '../utils/deviceFingerprint';
import securityService from '../services/securityService';
import { ScriptIntegrityProtector } from '../utils/scriptIntegrity';
import { initializeSecurityConfig, securityConfig } from '../utils/securityConfig';
const SecurityContext = createContext();
export const SecurityProvider = ({ children }) => {
  const [isSecure, setIsSecure] = useState(false);
  const [fingerprint, setFingerprint] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [globalSecurityActive, setGlobalSecurityActive] = useState(false);
  // Initialize global security system
  const initializeGlobalSecurity = useCallback(async () => {
    try {
      // Initialize security configuration
      await initializeSecurityConfig();
      // Create global script integrity protector
      const globalProtector = new ScriptIntegrityProtector();
      // Store protector instance globally for access from other components
      window.globalSecurityProtector = globalProtector;
      setGlobalSecurityActive(true);
      // Silent global security activation - no console output to prevent exposure
      securityConfig.logSecurityEvent('GLOBAL_SECURITY_ACTIVATED', {
        level: 'NORMAL',
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        route: window.location.pathname + window.location.search,
        securityLevel: 'MAXIMUM'
      });
    } catch (err) {
      // Silent error handling - no console output to prevent error exposure
      setError('Failed to initialize global security.');
    }
  }, []);
  const runChecks = useCallback(async () => {
    try {
      setLoading(true);
      // Initialize global security first
      if (!globalSecurityActive) {
        await initializeGlobalSecurity();
      }
      const fp = await getUnbreakableFingerprint();
      setFingerprint(fp);
      const checks = await clientProtection.runChecks();
      const failedChecks = Object.values(checks).filter(c => c && c.valid === false);
      if (failedChecks.length > 0) {
        // Silent security check failure - no console output to prevent exposure
        message.error('Security checks failed. Some functionality may be limited.');
        setIsSecure(false);
        // Report to backend
        securityService.reportViolation?.({ type: 'client-tampering', details: failedChecks });
      } else {
        setIsSecure(true);
      }
    } catch (err) {
      // Silent error handling - no console output to prevent error exposure
      setError('Failed to initialize security context.');
      setIsSecure(false);
    } finally {
      setLoading(false);
    }
  }, [globalSecurityActive, initializeGlobalSecurity]);
  // Initialize security immediately when component mounts
  useEffect(() => {
    // Activate security as early as possible
    const activateSecurity = async () => {
      await initializeGlobalSecurity();
      await runChecks();
    };
    activateSecurity();
  }, [initializeGlobalSecurity, runChecks]);
  // Monitor for security violations globally
  useEffect(() => {
    if (!globalSecurityActive) return;
    const handleSecurityViolation = (event) => {
      if (event.detail?.type === 'console_access' || event.detail?.type === 'devtools_opened') {
        setIsSecure(false);
        message.error('Security violation detected. Please close developer tools and refresh the page.');
        // Report violation
        securityService.reportViolation?.({
          type: event.detail.type,
          details: event.detail,
          timestamp: Date.now(),
          route: window.location.pathname
        });
      }
    };
    // Listen for security violations from the global protector
    window.addEventListener('securityViolation', handleSecurityViolation);
    return () => {
      window.removeEventListener('securityViolation', handleSecurityViolation);
    };
  }, [globalSecurityActive]);
  const value = useMemo(() => ({
    isSecure,
    fingerprint,
    loading,
    error,
    globalSecurityActive,
    securityService, // Add securityService to context
    refreshChecks: runChecks,
    initializeGlobalSecurity,
  }), [isSecure, fingerprint, loading, error, globalSecurityActive, securityService, runChecks, initializeGlobalSecurity]);
  return (
    <SecurityContext.Provider value={value}>
      {children}
    </SecurityContext.Provider>
  );
};
export const useSecurity = () => {
  const context = useContext(SecurityContext);
  if (context === undefined) {
    throw new Error('useSecurity must be used within a SecurityProvider');
  }
  return context;
};
export default SecurityContext;
