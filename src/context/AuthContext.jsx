import { createContext, useContext, useState, useCallback, useEffect } from 'react';
const AuthContext = createContext({});
export const AuthProvider = ({ children }) => {
  const [admin, setAdmin] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastValidationAttempt, setLastValidationAttempt] = useState(0);
  // Validate credentials with backend on mount using secure session
  useEffect(() => {
    const validateAdmin = async () => {
      // Rate limiting: prevent rapid validation attempts (reduced cooldown)
      const now = Date.now();
      if (now - lastValidationAttempt < 500) { // Reduced to 500ms cooldown
        setLoading(false);
        return;
      }
      setLastValidationAttempt(now);
      // Check for session token in sessionStorage
      const sessionToken = sessionStorage.getItem('adminSessionToken');
      if (!sessionToken) {
        setAdmin(null);
        setLoading(false);
        return;
      }
      try {
        // Make a request to validate session with token
        const response = await fetch('/.netlify/functions/security?action=validate-session', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'x-session-token': sessionToken,
          },
        });
        if (response.ok) {
          const data = await response.json();
          if (data && data.username) {
            setAdmin({
              username: data.username,
              role: data.role || 'admin',
              permissions: data.permissions || {}
            });
          } else {
            setAdmin(null);
            sessionStorage.removeItem('adminSessionToken');
          }
        } else {
          // Handle different error status codes
          if (response.status === 401) {
            // Unauthorized - clear token and set as not authenticated
            setAdmin(null);
            sessionStorage.removeItem('adminSessionToken');
          } else {
            // Other errors - still set as not authenticated but don't clear token immediately
            setAdmin(null);
          }
        }
      } catch (err) {
        // Network or other errors - don't clear token immediately in case it's temporary
        setAdmin(null);
        setError('Connection error. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    validateAdmin();
  }, []);
  // Admin login with username and password using secure session
  const login = async (username, password) => {
    try {
      const response = await fetch('/.netlify/functions/security?action=login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });
      if (response.ok) {
        const data = await response.json();
        if (data && data.sessionToken) {
          // Store session token in sessionStorage (more secure than localStorage)
          sessionStorage.setItem('adminSessionToken', data.sessionToken);
          setAdmin({
            username: data.username,
            role: data.role || 'admin',
            permissions: data.permissions || {}
          });
          return true; // Success
        } else {
          setAdmin(null);
          return false;
        }
      } else {
        setAdmin(null);
        return false;
      }
    } catch (error) {
      setAdmin(null);
      return false;
    }
  };
  // Logout admin
  const logout = async () => {
    // Clear session token
    sessionStorage.removeItem('adminSessionToken');
    // Clear any remaining localStorage items (cleanup)
    localStorage.removeItem('adminUsername');
    localStorage.removeItem('adminPassword');
    // Clear all session storage
    sessionStorage.clear();
    setAdmin(null);
    setError(null);
  };
  // Check if admin has specific permission
  const hasPermission = useCallback((requiredPermission) => {
    if (!admin) return false;
    if (admin.role === 'owner') return true; // Owner has all permissions
    // Check role-based permissions
    if (admin.role === 'ml_security') {
      const mlPermissions = ['ml_analysis', 'behavior_monitoring', 'security_events', 'user_behavior_profiles'];
      return mlPermissions.includes(requiredPermission);
    }
    if (admin.role === 'admin') {
      const adminPermissions = ['scripts', 'keys', 'users', 'basic_security'];
      return adminPermissions.includes(requiredPermission);
    }
    // Check explicit permissions
    return admin.permissions?.[requiredPermission] === true;
  }, [admin]);
  // Check if user is authenticated (now relies on admin state)
  const isAuthenticated = !!admin;
  // Get current admin
  const getCurrentAdmin = useCallback(() => {
    return admin;
  }, [admin]);
  return (
    <AuthContext.Provider
      value={{
        admin,
        loading,
        error,
        login,
        logout,
        isAuthenticated,
        isAdmin: !!admin,
        isOwner: admin?.role === 'owner',
        isMLSecurity: admin?.role === 'ml_security',
        isSuperAdmin: admin?.role === 'super_admin',
        hasPermission,
        getCurrentAdmin
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
