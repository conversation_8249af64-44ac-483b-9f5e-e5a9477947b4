-- PROJECT MADARA - MAIN LOADER (WORKING SYSTEM)
-- Fixed version with proper HWID, UI, and game script loading

-- Check if game is supported first
local function isGameSupported()
    local placeId = tostring(game.PlaceId)
    local supportedGames = {
        ["17574618959"] = true,  -- Add more PlaceIds here
    }
    return supportedGames[placeId] == true
end

-- Load game script after key validation
local function loadGameScript()
    local placeId = tostring(game.PlaceId)
    local scriptUrl = "https://projectmadara.com/.netlify/functions/secure-script?action=get_game_script&placeId=" .. placeId

    print("🎮 Loading game script for Place ID: " .. placeId)

    local success, result = pcall(function()
        return game:HttpGet(scriptUrl)
    end)

    if success and result and result ~= "" and not string.find(result, "error") then
        local loadSuccess, response = pcall(loadstring(result))
        if loadSuccess then
            print("✅ Game script loaded successfully!")
        else
            warn("❌ Failed to execute game script")
        end
    else
        warn("❌ No script found for this game")
        warn("❌ Place ID " .. placeId .. " is not supported yet")
    end
end

local function checkKey(key)
    assert(key and #key > 0, "A valid key is required")

    -- Get proper HWID
    local hwid = game:GetService("RbxAnalyticsService"):GetClientId()
    if not hwid or hwid == "" then
        warn("❌ Unable to get HWID")
        return false
    end

    print("🔑 Validating key: " .. key)
    print("🆔 HWID: " .. hwid)

    local url = "https://projectmadara.com/.netlify/functions/validate-key?key=" .. key .. "&hwid=" .. hwid

    local success, result = pcall(function()
        return game:HttpGet(url)
    end)

    if success and result then
        print("📨 Server response: " .. result)

        -- Check if it's a success response (contains "return true")
        if string.find(result, "return true") then
            local loadSuccess, response = pcall(loadstring(result))
            if loadSuccess and response then
                return response
            else
                return true -- Key is valid even if script execution fails
            end
        else
            -- It's an error message, execute it to show the warning
            local loadSuccess, response = pcall(loadstring(result))
            if loadSuccess then
                -- This will show the warn() message from server
            end
            return false
        end
    else
        warn("Unable to reach validation server.")
        print("🔍 Error details: " .. tostring(result))
        return false
    end
end
    
    local function createKeyInputGUI()
        local ProjectLUI = Instance.new("ScreenGui")
        ProjectLUI.Name = "ProjectLUI"
        ProjectLUI.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
        ProjectLUI.Parent = game.CoreGui

        local CanvasGroup = Instance.new("CanvasGroup")
        CanvasGroup.AnchorPoint = Vector2.new(0.5, 0.5)
        CanvasGroup.Position = UDim2.new(0.5, 0, 0.5082417726516724, 0)
        CanvasGroup.BorderColor3 = Color3.fromRGB(0, 0, 0)
        CanvasGroup.Size = UDim2.new(0, 400, 0, 300)
        CanvasGroup.BorderSizePixel = 0
        CanvasGroup.BackgroundColor3 = Color3.fromRGB(20, 20, 20)
        CanvasGroup.Parent = ProjectLUI

        local UICorner = Instance.new("UICorner")
        UICorner.CornerRadius = UDim.new(0, 6)
        UICorner.Parent = CanvasGroup

        local TopFrame = Instance.new("Frame")
        TopFrame.Name = "TopFrame"
        TopFrame.BorderColor3 = Color3.fromRGB(29, 29, 29)
        TopFrame.Size = UDim2.new(1, 0, 0.15094339847564697, 0)
        TopFrame.BorderSizePixel = 0
        TopFrame.BackgroundColor3 = Color3.fromRGB(31, 31, 31)
        TopFrame.Parent = CanvasGroup

        local CloseButton = Instance.new("TextButton")
        CloseButton.FontFace =
            Font.new("rbxasset://fonts/families/SourceSansPro.json", Enum.FontWeight.Bold, Enum.FontStyle.Normal)
        CloseButton.TextColor3 = Color3.fromRGB(0, 0, 0)
        CloseButton.BorderColor3 = Color3.fromRGB(0, 0, 0)
        CloseButton.Text = ""
        CloseButton.BorderSizePixel = 0
        CloseButton.Name = "CloseButton"
        CloseButton.Position = UDim2.new(0.9001429080963135, 0, 0.106999970972538, 0)
        CloseButton.Size = UDim2.new(0.08571428805589676, 0, 0.75, 0)
        CloseButton.ZIndex = 2
        CloseButton.TextSize = 14
        CloseButton.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
        CloseButton.Parent = TopFrame

        local UICorner = Instance.new("UICorner")
        UICorner.CornerRadius = UDim.new(0, 5)
        UICorner.Parent = CloseButton

        local ImageButton = Instance.new("ImageButton")
        ImageButton.ImageColor3 = Color3.fromRGB(158, 158, 158)
        ImageButton.ImageTransparency = 0.10000000149011612
        ImageButton.BorderColor3 = Color3.fromRGB(0, 0, 0)
        ImageButton.LayoutOrder = 3
        ImageButton.Size = UDim2.new(0.6666666865348816, 0, 0.6666666865348816, 0)
        ImageButton.AnchorPoint = Vector2.new(0.5, 0.5)
        ImageButton.Image = "rbxassetid://3926305904"
        ImageButton.BackgroundTransparency = 1
        ImageButton.ImageRectSize = Vector2.new(36, 36)
        ImageButton.Position = UDim2.new(0.5, 0, 0.5, 0)
        ImageButton.ZIndex = 2
        ImageButton.ImageRectOffset = Vector2.new(924, 724)
        ImageButton.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
        ImageButton.Parent = CloseButton

        local TextLabel = Instance.new("TextLabel")
        TextLabel.FontFace =
            Font.new("rbxasset://fonts/families/SourceSansPro.json", Enum.FontWeight.Regular, Enum.FontStyle.Normal)
        TextLabel.TextColor3 = Color3.fromRGB(220, 220, 220)
        TextLabel.BorderColor3 = Color3.fromRGB(0, 0, 0)
        TextLabel.Text = "<b>Project L - Key System V2 </b>"
        TextLabel.BackgroundTransparency = 1
        TextLabel.Size = UDim2.new(0, 215, 0, 45)
        TextLabel.BorderSizePixel = 0
        TextLabel.RichText = true
        TextLabel.TextSize = 20
        TextLabel.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
        TextLabel.Parent = TopFrame

        local TextLabel = Instance.new("TextLabel")
        TextLabel.FontFace =
            Font.new("rbxasset://fonts/families/SourceSansPro.json", Enum.FontWeight.Bold, Enum.FontStyle.Normal)
        TextLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
        TextLabel.BorderColor3 = Color3.fromRGB(0, 0, 0)
        TextLabel.Text = "Project L - Key last 24hr with only 2 checkpoints"
        TextLabel.BackgroundTransparency = 1
        TextLabel.Position = UDim2.new(0.033999983221292496, 0, 0.19683019816875458, 0)
        TextLabel.Size = UDim2.new(0.93, 0, 0.19, 0)
        TextLabel.BorderSizePixel = 0
        TextLabel.TextSize = 16
        TextLabel.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
        TextLabel.Parent = CanvasGroup

        local Frame = Instance.new("Frame")
        Frame.Size = UDim2.new(0.9290000200271606, 0, 0.1889999955892563, 0)
        Frame.BackgroundTransparency = 1
        Frame.Position = UDim2.new(0.03400000184774399, 0, 0.4020000100135803, 0)
        Frame.BorderColor3 = Color3.fromRGB(0, 0, 0)
        Frame.ZIndex = 2
        Frame.BorderSizePixel = 0
        Frame.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
        Frame.Parent = CanvasGroup

        local UICorner = Instance.new("UICorner")
        UICorner.CornerRadius = UDim.new(0, 5)
        UICorner.Parent = Frame

        local UIStroke = Instance.new("UIStroke")
        UIStroke.Color = Color3.fromRGB(42, 109, 64)
        UIStroke.ApplyStrokeMode = Enum.ApplyStrokeMode.Border
        UIStroke.Parent = Frame

        local KeyBox = Instance.new("TextBox")
        KeyBox.FontFace =
            Font.new("rbxasset://fonts/families/SourceSansPro.json", Enum.FontWeight.Regular, Enum.FontStyle.Normal)
        KeyBox.TextTruncate = Enum.TextTruncate.AtEnd
        KeyBox.PlaceholderText = "Insert Key..."
        KeyBox.TextSize = 16
        KeyBox.Size = UDim2.new(0.9100000262260437, 0, 0.1599999964237213, 0)
        KeyBox.TextColor3 = Color3.fromRGB(200, 200, 200)
        KeyBox.BorderColor3 = Color3.fromRGB(0, 0, 0)
        KeyBox.Text = ""
        KeyBox.TextXAlignment = Enum.TextXAlignment.Left
        KeyBox.CursorPosition = -1
        KeyBox.Name = "KeyBox"
        KeyBox.Position = UDim2.new(0.04285714402794838, 0, 0.41661980748176575, 0)
        KeyBox.ZIndex = 3
        KeyBox.BorderSizePixel = 0
        KeyBox.PlaceholderColor3 = Color3.fromRGB(178, 178, 178)
        KeyBox.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
        KeyBox.Parent = CanvasGroup

        local UICorner = Instance.new("UICorner")
        UICorner.CornerRadius = UDim.new(0, 4)
        UICorner.Parent = KeyBox

        local UIPadding = Instance.new("UIPadding")
        UIPadding.PaddingLeft = UDim.new(0, 15)
        UIPadding.Parent = KeyBox

        local UIStroke = Instance.new("UIStroke")
        UIStroke.Color = Color3.fromRGB(65, 65, 65)
        UIStroke.ApplyStrokeMode = Enum.ApplyStrokeMode.Border
        UIStroke.Parent = KeyBox

        local GetFrame = Instance.new("Frame")
        GetFrame.Name = "GetFrame"
        GetFrame.Position = UDim2.new(0.5059999823570251, 0, 0.6420000195503235, 0)
        GetFrame.BorderColor3 = Color3.fromRGB(0, 0, 0)
        GetFrame.Size = UDim2.new(0.4569999873638153, 0, 0.13199999928474426, 0)
        GetFrame.BorderSizePixel = 0
        GetFrame.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
        GetFrame.Parent = CanvasGroup

        local UICorner = Instance.new("UICorner")
        UICorner.CornerRadius = UDim.new(0, 4)
        UICorner.Parent = GetFrame

        local UIGradient = Instance.new("UIGradient")
        UIGradient.Rotation = 90
        UIGradient.Color =
            ColorSequence.new {
            ColorSequenceKeypoint.new(0, Color3.fromRGB(255, 102, 102)),
            ColorSequenceKeypoint.new(1, Color3.fromRGB(153, 0, 0))
        }
        UIGradient.Parent = GetFrame

        local GetKey = Instance.new("TextButton")
        GetKey.FontFace =
            Font.new("rbxasset://fonts/families/SourceSansPro.json", Enum.FontWeight.Bold, Enum.FontStyle.Normal)
        GetKey.TextColor3 = Color3.fromRGB(255, 255, 255)
        GetKey.BorderColor3 = Color3.fromRGB(0, 0, 0)
        GetKey.Text = "Get Key"
        GetKey.BackgroundTransparency = 1
        GetKey.Name = "GetKey"
        GetKey.Size = UDim2.new(1, 0, 1, 0)
        GetKey.BorderSizePixel = 0
        GetKey.TextSize = 18
        GetKey.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
        GetKey.Parent = GetFrame

        local DiscordFrame = Instance.new("Frame")
        DiscordFrame.Name = "DiscordFrame"
        DiscordFrame.Position = UDim2.new(0.03400000184774399, 0, 0.7979999780654907, 0)
        DiscordFrame.BorderColor3 = Color3.fromRGB(0, 0, 0)
        DiscordFrame.Size = UDim2.new(0.9290000200271606, 0, 0.13199999928474426, 0)
        DiscordFrame.BorderSizePixel = 0
        DiscordFrame.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
        DiscordFrame.Parent = CanvasGroup

        local UICorner = Instance.new("UICorner")
        UICorner.CornerRadius = UDim.new(0, 4)
        UICorner.Parent = DiscordFrame

        local UIGradient = Instance.new("UIGradient")
        UIGradient.Rotation = 90
        UIGradient.Color =
            ColorSequence.new {
            ColorSequenceKeypoint.new(0, Color3.fromRGB(114, 137, 218)),
            ColorSequenceKeypoint.new(1, Color3.fromRGB(78, 93, 148))
        }
        UIGradient.Parent = DiscordFrame

        local Discord = Instance.new("TextButton")
        Discord.FontFace =
            Font.new("rbxasset://fonts/families/SourceSansPro.json", Enum.FontWeight.Bold, Enum.FontStyle.Normal)
        Discord.TextColor3 = Color3.fromRGB(255, 255, 255)
        Discord.BorderColor3 = Color3.fromRGB(0, 0, 0)
        Discord.Text = "Join Discord"
        Discord.Name = "Discord"
        Discord.BackgroundTransparency = 1
        Discord.Size = UDim2.new(1, 0, 1, 0)
        Discord.BorderSizePixel = 0
        Discord.TextWrapped = true
        Discord.TextSize = 18
        Discord.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
        Discord.Parent = DiscordFrame

        local CheckFrame = Instance.new("Frame")
        CheckFrame.Name = "CheckFrame"
        CheckFrame.Position = UDim2.new(0.03400000184774399, 0, 0.6420000195503235, 0)
        CheckFrame.BorderColor3 = Color3.fromRGB(0, 0, 0)
        CheckFrame.Size = UDim2.new(0.4569999873638153, 0, 0.13199999928474426, 0)
        CheckFrame.BorderSizePixel = 0
        CheckFrame.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
        CheckFrame.Parent = CanvasGroup

        local CheckKey = Instance.new("TextButton")
        CheckKey.FontFace =
            Font.new("rbxasset://fonts/families/SourceSansPro.json", Enum.FontWeight.Bold, Enum.FontStyle.Normal)
        CheckKey.TextColor3 = Color3.fromRGB(255, 255, 255)
        CheckKey.BorderColor3 = Color3.fromRGB(0, 0, 0)
        CheckKey.Text = "Check Key"
        CheckKey.BackgroundTransparency = 1
        CheckKey.Name = "CheckKey"
        CheckKey.Size = UDim2.new(1, 0, 1, 0)
        CheckKey.BorderSizePixel = 0
        CheckKey.TextSize = 18
        CheckKey.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
        CheckKey.Parent = CheckFrame

        local UIGradient = Instance.new("UIGradient")
        UIGradient.Rotation = 90
        UIGradient.Color =
            ColorSequence.new {
            ColorSequenceKeypoint.new(0, Color3.fromRGB(61, 207, 117)),
            ColorSequenceKeypoint.new(1, Color3.fromRGB(25, 102, 61))
        }
        UIGradient.Parent = CheckFrame

        local UICorner = Instance.new("UICorner")
        UICorner.CornerRadius = UDim.new(0, 4)
        UICorner.Parent = CheckFrame

        local Notif = {}
        local CoreGUI = game:GetService("CoreGui")
        local TS = game:GetService("TweenService")

        local HIDEUI = get_hidden_gui or gethui
        if syn and typeof(syn) == "table" and RenderWindow then
            syn.protect_gui = gethui
        end
        local function Hide_UI(gui)
            if HIDEUI then
                gui["Parent"] = HIDEUI()
            elseif (not is_sirhurt_closure) and (syn and syn.protect_gui) then
                syn.protect_gui(gui)
                gui["Parent"] = CoreGUI
            elseif CoreGUI:FindFirstChild("RobloxGui") then
                gui["Parent"] = CoreGUI.RobloxGui
            else
                gui["Parent"] = CoreGUI
            end
        end

        local screen_gui = Instance.new("ScreenGui")
        Hide_UI(screen_gui)

        local frame = Instance.new("Frame")
        frame.AnchorPoint = Vector2.new(0.5, 0.949999988079071)
        frame.BackgroundColor3 = Color3.new(1, 1, 1)
        frame.BackgroundTransparency = 1
        frame.BorderColor3 = Color3.new(0, 0, 0)
        frame.BorderSizePixel = 0
        frame.Position = UDim2.new(0.5, 0, 0.954999983, 0)
        frame.Size = UDim2.new(0, 100, 0, 100)
        frame.Visible = true
        frame.Parent = screen_gui

        local uilist_layout = Instance.new("UIListLayout")
        uilist_layout.HorizontalAlignment = Enum.HorizontalAlignment.Center
        uilist_layout.SortOrder = Enum.SortOrder.LayoutOrder
        uilist_layout.VerticalAlignment = Enum.VerticalAlignment.Bottom
        uilist_layout.Parent = frame

        function Notif.New(text, timee)
            local frame_2 = Instance.new("Frame")
            frame_2.BackgroundColor3 = Color3.new(1, 1, 1)
            frame_2.BorderColor3 = Color3.new(0, 0, 0)
            frame_2.BorderSizePixel = 0
            frame_2.BackgroundTransparency = 1
            frame_2.Size = UDim2.new(0, 100, 0, 0)
            frame_2.Visible = true
            frame_2.Parent = frame

            -- Main Notification Frame
            local frame_3 = Instance.new("Frame")
            frame_3.AnchorPoint = Vector2.new(0.5, 1)
            frame_3.AutomaticSize = Enum.AutomaticSize.X
            frame_3.BackgroundColor3 = Color3.new(0.141176, 0.141176, 0.141176)
            frame_3.BackgroundTransparency = 0.20000000298023224
            frame_3.BorderColor3 = Color3.new(0, 0, 0)
            frame_3.Position = UDim2.new(0.5, 0, 1, 60)
            frame_3.Size = UDim2.new(0, 0, 0, 30)
            frame_3.Visible = true
            frame_3.Parent = frame_2

            local uicorner = Instance.new("UICorner")
            uicorner.CornerRadius = UDim.new(0, 6)
            uicorner.Parent = frame_3

            local uipadding = Instance.new("UIPadding")
            uipadding.PaddingBottom = UDim.new(0, 3)
            uipadding.PaddingLeft = UDim.new(0, 3)
            uipadding.PaddingRight = UDim.new(0, 3)
            uipadding.PaddingTop = UDim.new(0, 3)
            uipadding.Parent = frame_3

            local uistroke = Instance.new("UIStroke")
            uistroke.ApplyStrokeMode = Enum.ApplyStrokeMode.Border
            uistroke.Color = Color3.new(0.0313726, 0.0313726, 0.0313726)
            uistroke.Parent = frame_3

            local text_label = Instance.new("TextLabel")
            text_label.Font = Enum.Font.Gotham
            text_label.Text = text
            text_label.TextColor3 = Color3.new(0.784314, 0.784314, 0.784314)
            text_label.TextSize = 14
            text_label.AutomaticSize = Enum.AutomaticSize.X
            text_label.BackgroundColor3 = Color3.new(1, 1, 1)
            text_label.BackgroundTransparency = 1
            text_label.BorderColor3 = Color3.new(0, 0, 0)
            text_label.BorderSizePixel = 0
            text_label.Size = UDim2.new(0, 0, 0, 24)
            text_label.Visible = true
            text_label.Parent = frame_3

            local uipadding_2 = Instance.new("UIPadding")
            uipadding_2.PaddingLeft = UDim.new(0, 5)
            uipadding_2.PaddingRight = UDim.new(0, 30)
            uipadding_2.Parent = text_label

            local text_button = Instance.new("TextButton")
            text_button.Font = Enum.Font.SourceSans
            text_button.Text = ""
            text_button.TextColor3 = Color3.new(0, 0, 0)
            text_button.TextSize = 14
            text_button.AnchorPoint = Vector2.new(1, 0.5)
            text_button.BackgroundColor3 = Color3.new(0, 0, 0)
            text_button.BackgroundTransparency = 1
            text_button.BorderColor3 = Color3.new(0, 0, 0)
            text_button.BorderSizePixel = 0
            text_button.Position = UDim2.new(1, 0, 0.5, 0)
            text_button.Size = UDim2.new(0, 24, 0, 24)
            text_button.Visible = true
            text_button.Parent = frame_3

            local uicorner_2 = Instance.new("UICorner")
            uicorner_2.CornerRadius = UDim.new(0, 5)
            uicorner_2.Parent = text_button

            local image_button = Instance.new("ImageButton")
            image_button.Image = "rbxassetid://3926305904"
            image_button.ImageColor3 = Color3.new(0.784314, 0.784314, 0.784314)
            image_button.ImageRectOffset = Vector2.new(924, 724)
            image_button.ImageRectSize = Vector2.new(36, 36)
            image_button.AnchorPoint = Vector2.new(0.5, 0.5)
            image_button.BackgroundTransparency = 1
            image_button.LayoutOrder = 3
            image_button.Position = UDim2.new(0.5, 0, 0.5, 0)
            image_button.Size = UDim2.new(0, 18, 0, 18)
            image_button.Visible = true
            image_button.ZIndex = 2
            image_button.Parent = text_button

            TS:Create(frame_3, TweenInfo.new(0.2, Enum.EasingStyle.Quint), {Position = UDim2.new(0.5, 0, 1, 0)}):Play()
            TS:Create(frame_2, TweenInfo.new(0.2, Enum.EasingStyle.Quint), {Size = UDim2.new(0, 100, 0, 35)}):Play()

            local function close_notif()
                TS:Create(image_button, TweenInfo.new(0.15, Enum.EasingStyle.Quint), {ImageTransparency = 1}):Play()
                TS:Create(text_button, TweenInfo.new(0.15, Enum.EasingStyle.Quint), {BackgroundTransparency = 1}):Play()
                TS:Create(text_label, TweenInfo.new(0.15, Enum.EasingStyle.Quint), {TextTransparency = 1}):Play()
                task.wait(.17)
                TS:Create(frame_3, TweenInfo.new(0.25, Enum.EasingStyle.Quint), {BackgroundTransparency = 1}):Play()
                TS:Create(uistroke, TweenInfo.new(0.24, Enum.EasingStyle.Quint), {Transparency = 1}):Play()
                task.wait(.05)
                TS:Create(frame_2, TweenInfo.new(0.2, Enum.EasingStyle.Quint), {Size = UDim2.new(0, 100, 0, 0)}):Play()
                task.wait(.2)
                frame_2:Destroy()
            end
            text_button.MouseEnter:Connect(
                function()
                    TS:Create(text_button, TweenInfo.new(0.25, Enum.EasingStyle.Quint), {BackgroundTransparency = 0.8}):Play(

                    )
                    TS:Create(
                        image_button,
                        TweenInfo.new(0.3, Enum.EasingStyle.Quint),
                        {ImageColor3 = Color3.new(0.890196, 0.054902, 0.054902)}
                    ):Play()
                end
            )

            text_button.MouseLeave:Connect(
                function()
                    TS:Create(text_button, TweenInfo.new(0.25, Enum.EasingStyle.Quint), {BackgroundTransparency = 1}):Play(

                    )
                    TS:Create(
                        image_button,
                        TweenInfo.new(0.3, Enum.EasingStyle.Quint),
                        {ImageColor3 = Color3.new(0.784314, 0.784314, 0.784314)}
                    ):Play()
                end
            )

            text_button.MouseButton1Click:Connect(
                function()
                    TS:Create(image_button, TweenInfo.new(0.15, Enum.EasingStyle.Quint), {ImageTransparency = 1}):Play()
                    TS:Create(text_button, TweenInfo.new(0.15, Enum.EasingStyle.Quint), {BackgroundTransparency = 1}):Play(

                    )
                    TS:Create(text_label, TweenInfo.new(0.15, Enum.EasingStyle.Quint), {TextTransparency = 1}):Play()
                    task.wait(.17)
                    TS:Create(frame_3, TweenInfo.new(0.25, Enum.EasingStyle.Quint), {BackgroundTransparency = 1}):Play()
                    TS:Create(uistroke, TweenInfo.new(0.24, Enum.EasingStyle.Quint), {Transparency = 1}):Play()
                    task.wait(.05)
                    TS:Create(frame_2, TweenInfo.new(0.2, Enum.EasingStyle.Quint), {Size = UDim2.new(0, 100, 0, 0)}):Play(

                    )
                    task.wait(.2)
                    frame_2:Destroy()
                end
            )
            image_button.MouseButton1Click:Connect(close_notif)
            task.delay(tonumber(timee) and timee or 10, close_notif)
        end

        local function makeDraggable(uiElement)
            local UserInputService = game:GetService("UserInputService")
            local dragging
            local dragInput
            local dragStart
            local startPos

            local function update(input)
                local delta = input.Position - dragStart
                uiElement.Position =
                    UDim2.new(
                    startPos.X.Scale,
                    startPos.X.Offset + delta.X,
                    startPos.Y.Scale,
                    startPos.Y.Offset + delta.Y
                )
            end

            uiElement.InputBegan:Connect(
                function(input)
                    if
                        input.UserInputType == Enum.UserInputType.MouseButton1 or
                            input.UserInputType == Enum.UserInputType.Touch
                     then
                        dragging = true
                        dragStart = input.Position
                        startPos = uiElement.Position

                        input.Changed:Connect(
                            function()
                                if input.UserInputState == Enum.UserInputState.End then
                                    dragging = false
                                end
                            end
                        )
                    end
                end
            )

            uiElement.InputChanged:Connect(
                function(input)
                    if
                        input.UserInputType == Enum.UserInputType.MouseMovement or
                            input.UserInputType == Enum.UserInputType.Touch
                     then
                        dragInput = input
                    end
                end
            )

            UserInputService.InputChanged:Connect(
                function(input)
                    if input == dragInput and dragging then
                        update(input)
                    end
                end
            )
        end
        makeDraggable(CanvasGroup)

        if fileKeyResult then
            ProjectLUI:Destroy()
            Notif.New("Correct Key File Key", 3)
            correctFunction()
        else
            ProjectLUI.Enabled = true 
        end
    
        ImageButton.MouseButton1Click:Connect(function()
            ProjectLUI:Destroy()
            Notif.New("Key System has been closed", 3)
        end)
    
        Discord.MouseButton1Click:Connect(function()
            setclipboard("https://discord.gg/fNxgAAjBdq")
            Notif.New("Discord Invite Link Copied!", 3)
        end)
    
        GetKey.MouseButton1Click:Connect(function()
            setclipboard("https://projectl.xyz/")
            Notif.New("Key website has been copied!")
        end)
    
        CheckKey.MouseButton1Click:Connect(function()
            local playerKey = KeyBox.Text
            local playerKeyResult = checkKey(playerKey)
    
            if playerKeyResult then
                writefile("projectL.txt", playerKey)
                ProjectLUI:Destroy()
                Notif.New("Loaded Script Successfully!", 3)
                main()
            else
                Notif.New("Incorrect Key", 3)
                print("Incorrect Key")
            end
        end)
    end

    local function promptForKey()
        local gui, textBox, submitButton = createKeyInputGUI()
        local keyEntered = false

        local function validateAndProceed()
            if keyEntered then return end
            keyEntered = true

            local key = textBox.Text:gsub("%s+", "")
            if key == "" then
                warn("Please enter a key")
                keyEntered = false
                return
            end

            textBox.Text = "Validating..."
            textBox.TextEditable = false
            submitButton.Text = "Validating..."

            local isValid = checkKey(key)

            if isValid then
                gui:Destroy()
                loadGameScript()
            else
                textBox.Text = ""
                textBox.TextEditable = true
                submitButton.Text = "Validate Key"
                keyEntered = false
            end
        end

        submitButton.MouseButton1Click:Connect(validateAndProceed)
        textBox.FocusLost:Connect(function(enterPressed)
            if enterPressed then
                validateAndProceed()
            end
        end)
    end

-- Main execution logic
local function main()
    local placeId = tostring(game.PlaceId)
    print("🚀 Project Madara - Starting...")
    print("🎮 Place ID: " .. placeId)

    -- First check if game is supported
    if not isGameSupported() then
        warn("❌ This game is not supported yet!")
        warn("❌ Place ID: " .. placeId)
        warn("❌ Contact support to add this game")
        return
    end

    print("✅ Game is supported!")
    print("🔑 Starting key validation...")

    -- Game is supported, show key system
    promptForKey()
end

-- Start the system
main()
