// Simple test to check if your key exists in database
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.log('Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testKey() {
  try {
    console.log('Testing key: MADARA-B8DB-9130-18F1');
    
    const { data: key, error } = await supabase
      .from('license_keys')
      .select('*')
      .eq('key_code', 'MADARA-B8DB-9130-18F1')
      .single();
      
    if (error) {
      console.log('Database error:', error);
      return;
    }
    
    if (key) {
      console.log('✅ Key found in database!');
      console.log('Key details:', {
        id: key.id,
        is_active: key.is_active,
        is_revoked: key.is_revoked,
        expires_at: key.expires_at
      });
    } else {
      console.log('❌ Key not found in database');
    }
    
  } catch (error) {
    console.log('Test error:', error);
  }
}

testKey();
