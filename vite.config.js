import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory
  const env = loadEnv(mode, process.cwd(), '');
  // Get and validate Supabase configuration
  const supabaseUrl = env.VITE_SUPABASE_URL || env.SUPABASE_URL;
  const supabaseAnonKey = env.VITE_SUPABASE_ANON_KEY || env.SUPABASE_ANON_KEY;
  if (!supabaseUrl) {
    // Silent validation - no console output to prevent environment exposure
    process.exit(1);
  }
  if (!supabaseAnonKey) {
    // Silent validation - no console output to prevent environment exposure
    process.exit(1);
  }
  return {
    define: {
      // Hardcode the values to ensure they're properly inlined
      'import.meta.env.VITE_SUPABASE_URL': JSON.stringify(supabaseUrl),
      'import.meta.env.VITE_SUPABASE_ANON_KEY': JSON.stringify(supabase<PERSON>nonKey),
      'process.env': {}
    },
    plugins: [react()],
    resolve: {
      alias: {
        "@": resolve("./src"),
      },
    },
    build: {
      outDir: "dist",
      minify: "terser",
      sourcemap: true,
      rollupOptions: {
        external: [],
        output: {
          manualChunks: {
            // Split vendor modules into separate chunks
            react: ['react', 'react-dom', 'react-router-dom', 'react-is'],
            radix: ['@radix-ui/themes', '@radix-ui/react-slot'],
            antd: ['antd', '@ant-design/icons'],
            // Split large dependencies into separate chunks
            framer: ['framer-motion'],
            charts: ['recharts'],
            // Group other node modules
            vendor: [
              'axios',
              'dayjs',
              'react-hook-form',
              'react-intersection-observer',
              'react-icons',
              'crypto-js',
            ],
          },
        },
      },
      chunkSizeWarningLimit: 1000, // Revert to original limit
      assetsInlineLimit: 0, // Don't inline assets to prevent TensorFlow.js issues
      // Clean console statements in production for security
      ...(mode === 'production' && {
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
          },
        },
      }),
    },
    server: {
      port: 3000,
      strictPort: true,
      open: true,
    },
  };
});